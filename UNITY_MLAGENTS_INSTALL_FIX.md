# 🔧 Unity ML-Agents Installation Fix

You're getting ML-Agents namespace errors because the package isn't installed yet. Here are multiple solutions:

## 🚀 **Solution 1: Manual Package Installation (Recommended)**

### **Step 1: Download ML-Agents Package**
Since you have network connectivity issues, let's install manually:

1. **Download ML-Agents from GitHub**:
   - Go to: https://github.com/Unity-Technologies/ml-agents/releases
   - Download the latest release (v2.0.1 or newer)
   - Extract the ZIP file

2. **Copy ML-Agents to Unity**:
   ```
   Downloaded ML-Agents/
   ├── com.unity.ml-agents/          ← Copy this folder
   ├── com.unity.ml-agents.extensions/
   └── Project/
   ```

3. **Install in Unity**:
   - Open Unity Package Manager
   - Click "+" → "Add package from disk"
   - Navigate to `com.unity.ml-agents/package.json`
   - Click "Open"

### **Step 2: Alternative - Local Package Installation**
If Package Manager still has issues:

1. **Create Packages folder** in your Unity project root:
   ```
   SquadMate-AI/
   ├── Assets/
   ├── Packages/          ← Create this folder
   │   └── manifest.json
   └── ProjectSettings/
   ```

2. **Edit Packages/manifest.json**:
   ```json
   {
     "dependencies": {
       "com.unity.ml-agents": "file:../path/to/com.unity.ml-agents",
       "com.unity.collab-proxy": "2.0.7",
       "com.unity.feature.development": "1.0.1",
       "com.unity.textmeshpro": "3.0.6",
       "com.unity.timeline": "1.7.5",
       "com.unity.ugui": "1.0.0",
       "com.unity.modules.ai": "1.0.0",
       "com.unity.modules.androidjni": "1.0.0",
       "com.unity.modules.animation": "1.0.0",
       "com.unity.modules.assetbundle": "1.0.0",
       "com.unity.modules.audio": "1.0.0",
       "com.unity.modules.cloth": "1.0.0",
       "com.unity.modules.director": "1.0.0",
       "com.unity.modules.imageconversion": "1.0.0",
       "com.unity.modules.imgui": "1.0.0",
       "com.unity.modules.jsonserialize": "1.0.0",
       "com.unity.modules.particlesystem": "1.0.0",
       "com.unity.modules.physics": "1.0.0",
       "com.unity.modules.physics2d": "1.0.0",
       "com.unity.modules.screencapture": "1.0.0",
       "com.unity.modules.terrain": "1.0.0",
       "com.unity.modules.terrainphysics": "1.0.0",
       "com.unity.modules.tilemap": "1.0.0",
       "com.unity.modules.ui": "1.0.0",
       "com.unity.modules.uielements": "1.0.0",
       "com.unity.modules.umbra": "1.0.0",
       "com.unity.modules.unityanalytics": "1.0.0",
       "com.unity.modules.unitywebrequest": "1.0.0",
       "com.unity.modules.unitywebrequestassetbundle": "1.0.0",
       "com.unity.modules.unitywebrequestexture": "1.0.0",
       "com.unity.modules.unitywebrequestwww": "1.0.0",
       "com.unity.modules.vehicles": "1.0.0",
       "com.unity.modules.video": "1.0.0",
       "com.unity.modules.vr": "1.0.0",
       "com.unity.modules.wind": "1.0.0",
       "com.unity.modules.xr": "1.0.0"
     }
   }
   ```

## 🌐 **Solution 2: Fix Network Connectivity**

### **Check Proxy Settings**
If you're behind a corporate firewall:

1. **Windows Proxy Settings**:
   - Settings → Network & Internet → Proxy
   - Note your proxy server and port

2. **Set Unity Proxy**:
   - Unity → Edit → Preferences → External Tools
   - Set HTTP Proxy settings if needed

3. **Environment Variables** (if using proxy):
   ```cmd
   set HTTP_PROXY=http://proxy.company.com:8080
   set HTTPS_PROXY=https://proxy.company.com:8080
   ```

### **Alternative Package Manager URLs**
Try these in Package Manager:

1. **Git URL Method**:
   - Package Manager → "+" → "Add package from git URL"
   - Enter: `https://github.com/Unity-Technologies/ml-agents.git?path=com.unity.ml-agents#release_21`

2. **Specific Version**:
   - Enter: `com.unity.ml-agents@2.0.1`

## 🔧 **Solution 3: Temporary Workaround**

While installing ML-Agents, create a temporary script to avoid errors:

### **Create Temporary ML-Agents Stubs**
Create `Assets/Scripts/Temp/MLAgentsStubs.cs`:

```csharp
// Temporary stubs until ML-Agents is installed
namespace Unity.MLAgents
{
    public class Agent : UnityEngine.MonoBehaviour
    {
        public virtual void Initialize() { }
        public virtual void OnEpisodeBegin() { }
        public virtual void CollectObservations(Sensors.VectorSensor sensor) { }
        public virtual void OnActionReceived(Actuators.ActionBuffers actionBuffers) { }
        public virtual void Heuristic(in Actuators.ActionBuffers actionsOut) { }
        public void AddReward(float reward) { }
        public void EndEpisode() { }
    }
}

namespace Unity.MLAgents.Sensors
{
    public class VectorSensor
    {
        public void AddObservation(UnityEngine.Vector3 obs) { }
        public void AddObservation(float obs) { }
        public void AddObservation(int obs) { }
    }
}

namespace Unity.MLAgents.Actuators
{
    public struct ActionBuffers
    {
        public ActionSegment<float> ContinuousActions;
        public ActionSegment<int> DiscreteActions;
    }
    
    public struct ActionSegment<T>
    {
        public T this[int index] { get { return default(T); } set { } }
    }
}
```

**Delete this file after ML-Agents is properly installed!**

## ✅ **Solution 4: Unity 6 Specific Installation**

### **Unity 6 Package Manager**
Unity 6 has an improved Package Manager:

1. **Clear Package Cache**:
   - Close Unity
   - Delete: `%APPDATA%/Unity/Asset Store-5.x`
   - Delete: `%LOCALAPPDATA%/Unity/cache`
   - Restart Unity

2. **Use Unity 6 Registry**:
   - Package Manager → Advanced → "Show preview packages"
   - Look for ML-Agents in the list

3. **Manual Registry Addition**:
   Edit `Packages/manifest.json` to add Unity's registry:
   ```json
   {
     "scopedRegistries": [
       {
         "name": "Unity",
         "url": "https://packages.unity.com",
         "scopes": [
           "com.unity"
         ]
       }
     ],
     "dependencies": {
       "com.unity.ml-agents": "2.0.1"
     }
   }
   ```

## 🚀 **Quick Fix Steps**

### **Immediate Action Plan**:

1. **Try Git URL first** (fastest if network works):
   ```
   Package Manager → "+" → Add package from git URL
   → https://github.com/Unity-Technologies/ml-agents.git?path=com.unity.ml-agents
   ```

2. **If that fails, download manually**:
   - Download from GitHub releases
   - Use "Add package from disk" method

3. **Verify installation**:
   - Check that `com.unity.ml-agents` appears in Package Manager
   - Compile errors should disappear
   - VS Code IntelliSense should work

### **After Installation**:

1. **Generate Unity project files**:
   - In VS Code: Ctrl+Shift+P → "Tasks: Run Task" → "Unity: Generate Project Files"

2. **Verify ML-Agents**:
   - Create a simple test script with `using Unity.MLAgents;`
   - Should compile without errors

3. **Test SquadMate scripts**:
   - All compilation errors should be resolved
   - IntelliSense should show ML-Agents methods

## 🔍 **Troubleshooting**

### **If Installation Still Fails**:

1. **Check Unity Version Compatibility**:
   - ML-Agents 2.0+ requires Unity 2021.3+
   - Unity 6 is fully supported

2. **Manual File Copy** (last resort):
   - Download ML-Agents source
   - Copy scripts directly to `Assets/ML-Agents/`
   - Not recommended but works

3. **Alternative Versions**:
   - Try ML-Agents 1.0.8 if 2.0+ has issues
   - Use: `com.unity.ml-agents@1.0.8`

## ✅ **Success Indicators**

You'll know it's working when:
- ✅ No compilation errors in Unity Console
- ✅ `using Unity.MLAgents;` works in scripts
- ✅ VS Code shows ML-Agents IntelliSense
- ✅ SquadMateAgent script compiles successfully

## 📞 **Need Help?**

If you're still having issues:
1. Check Unity Console for specific error messages
2. Verify your Unity version (should be 6000.1.6f1)
3. Try the manual download method
4. Check if corporate firewall is blocking Unity

The manual installation method almost always works, even with network issues! 🚀
