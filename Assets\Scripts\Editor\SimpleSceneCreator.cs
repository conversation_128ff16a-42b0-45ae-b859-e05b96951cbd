using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;

/// <summary>
/// Simple and reliable scene creator for SquadMate training
/// </summary>
public class SimpleSceneCreator : EditorWindow
{
    [MenuItem("SquadMate AI/Create Training Scene (Simple)")]
    public static void ShowWindow()
    {
        GetWindow<SimpleSceneCreator>("Simple Scene Creator");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Simple Training Scene Creator", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("This will create a basic working training scene:");
        GUILayout.Label("✅ Ground plane");
        GUILayout.Label("✅ Player (Blue capsule)");
        GUILayout.Label("✅ SquadMate (Green capsule) with ML-Agents");
        GUILayout.Label("✅ Environment manager");
        GUILayout.Label("✅ Debug GUI");
        GUILayout.Label("✅ All components and references configured");
        
        GUILayout.Space(20);
        
        if (GUILayout.Button("🎮 CREATE WORKING SCENE NOW", GUILayout.Height(50)))
        {
            CreateWorkingScene();
        }
        
        GUILayout.Space(10);
        
        EditorGUILayout.HelpBox("Scene will be saved as 'TrainingEnvironment' in Assets/Scenes/", MessageType.Info);
    }
    
    public static void CreateWorkingScene()
    {
        Debug.Log("🎮 Creating Simple Working Training Scene...");
        
        try
        {
            // Step 1: Create new scene
            Scene newScene = EditorSceneManager.NewScene(NewSceneSetup.DefaultGameObjects, NewSceneMode.Single);
            
            // Step 2: Create ground
            CreateGround();
            
            // Step 3: Create player
            GameObject player = CreatePlayer();
            
            // Step 4: Create squadmate with ML-Agents
            GameObject squadmate = CreateSquadMate();
            
            // Step 5: Create environment
            GameObject environment = CreateEnvironment();
            
            // Step 6: Configure all references
            ConfigureReferences(player, squadmate, environment);
            
            // Step 7: Add debug system
            AddDebugSystem();
            
            // Step 8: Save scene
            SaveScene();
            
            Debug.Log("✅ WORKING TRAINING SCENE CREATED!");
            Debug.Log("📁 Scene saved to: Assets/Scenes/TrainingEnvironment.unity");
            Debug.Log("🎮 Press Play to start training!");
            
            EditorUtility.DisplayDialog("Success!", 
                "Working training scene created!\n\n" +
                "📁 Location: Assets/Scenes/TrainingEnvironment.unity\n" +
                "🎮 Press Play to start training!\n" +
                "📊 Debug GUI will show training status\n" +
                "🔍 Watch Console for training messages", "Great!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error creating scene: {e.Message}");
            EditorUtility.DisplayDialog("Error", $"Failed to create scene:\n{e.Message}", "OK");
        }
    }
    
    private static void CreateGround()
    {
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(10, 1, 10); // 100x100 units
        
        // Make it static for NavMesh
        GameObjectUtility.SetStaticEditorFlags(ground, StaticEditorFlags.NavigationStatic);
        
        Debug.Log("✅ Created ground plane");
    }
    
    private static GameObject CreatePlayer()
    {
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        player.transform.localScale = new Vector3(1, 2, 1);
        
        // Add Rigidbody
        Rigidbody rb = player.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
        
        // Add PlayerController
        player.AddComponent<PlayerController>();
        
        // Make it blue
        Material blueMat = new Material(Shader.Find("Standard"));
        blueMat.color = Color.blue;
        player.GetComponent<Renderer>().material = blueMat;
        
        Debug.Log("✅ Created Player with PlayerController");
        return player;
    }
    
    private static GameObject CreateSquadMate()
    {
        GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        squadmate.name = "SquadMate";
        squadmate.transform.position = new Vector3(3, 1, 0);
        squadmate.transform.localScale = new Vector3(1, 2, 1);
        
        // Add Rigidbody
        Rigidbody rb = squadmate.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
        
        // Add ML-Agents components
        BehaviorParameters behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;
        
        // Add SquadMate scripts
        squadmate.AddComponent<SquadMateAgent>();
        squadmate.AddComponent<RewardCalculator>();
        
        // Make it green
        Material greenMat = new Material(Shader.Find("Standard"));
        greenMat.color = Color.green;
        squadmate.GetComponent<Renderer>().material = greenMat;
        
        Debug.Log("✅ Created SquadMate with ML-Agents components");
        return squadmate;
    }
    
    private static GameObject CreateEnvironment()
    {
        GameObject environment = new GameObject("Environment");
        environment.transform.position = Vector3.zero;
        
        // Add environment script
        environment.AddComponent<GameEnvironment>();
        
        Debug.Log("✅ Created Environment with GameEnvironment script");
        return environment;
    }
    
    private static void ConfigureReferences(GameObject player, GameObject squadmate, GameObject environment)
    {
        Debug.Log("🔗 Configuring all references...");
        
        // Configure SquadMate references
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.player = player.transform;
            agent.environment = environment.GetComponent<GameEnvironment>();
            Debug.Log("✅ SquadMate references configured");
        }
        
        // Configure Environment references
        GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
        if (gameEnv != null)
        {
            gameEnv.player = player.transform;
            gameEnv.squadMate = agent;
            gameEnv.environmentSize = new Vector3(100f, 0f, 100f);
            Debug.Log("✅ Environment references configured");
        }
        
        // Configure RewardCalculator
        RewardCalculator rewardCalc = squadmate.GetComponent<RewardCalculator>();
        if (rewardCalc != null)
        {
            rewardCalc.agent = agent;
            Debug.Log("✅ RewardCalculator configured");
        }
    }
    
    private static void AddDebugSystem()
    {
        GameObject debugger = new GameObject("TrainingDebugger");
        TrainingDebugger debugComponent = debugger.AddComponent<TrainingDebugger>();
        
        // Configure debugger
        debugComponent.showDebugInfo = true;
        debugComponent.showGUI = true;
        debugComponent.logRewards = true;
        
        Debug.Log("✅ Added TrainingDebugger with GUI");
    }
    
    private static void SaveScene()
    {
        // Create Scenes folder if it doesn't exist
        if (!AssetDatabase.IsValidFolder("Assets/Scenes"))
        {
            AssetDatabase.CreateFolder("Assets", "Scenes");
        }
        
        // Save the scene
        string scenePath = "Assets/Scenes/TrainingEnvironment.unity";
        bool success = EditorSceneManager.SaveScene(SceneManager.GetActiveScene(), scenePath);
        
        if (success)
        {
            Debug.Log($"✅ Scene saved to: {scenePath}");
            AssetDatabase.Refresh();
        }
        else
        {
            Debug.LogError("❌ Failed to save scene");
        }
    }
}

/// <summary>
/// Menu item to quickly open the training scene
/// </summary>
public class SceneHelper
{
    [MenuItem("SquadMate AI/Open Training Scene")]
    public static void OpenTrainingScene()
    {
        string scenePath = "Assets/Scenes/TrainingEnvironment.unity";
        
        if (System.IO.File.Exists(scenePath))
        {
            EditorSceneManager.OpenScene(scenePath);
            Debug.Log($"✅ Opened training scene: {scenePath}");
        }
        else
        {
            Debug.LogWarning("❌ Training scene not found. Create it first using 'Create Training Scene (Simple)'");
            EditorUtility.DisplayDialog("Scene Not Found", 
                "Training scene not found!\n\n" +
                "Use 'SquadMate AI → Create Training Scene (Simple)' to create it first.", "OK");
        }
    }
    
    [MenuItem("SquadMate AI/Show Scene Location")]
    public static void ShowSceneLocation()
    {
        string scenePath = "Assets/Scenes/TrainingEnvironment.unity";
        
        if (System.IO.File.Exists(scenePath))
        {
            // Ping the scene in Project window
            Object sceneAsset = AssetDatabase.LoadAssetAtPath<Object>(scenePath);
            EditorGUIUtility.PingObject(sceneAsset);
            Selection.activeObject = sceneAsset;
            
            Debug.Log($"📁 Training scene location: {scenePath}");
            EditorUtility.DisplayDialog("Scene Location", 
                $"Training scene is located at:\n{scenePath}\n\n" +
                "It has been highlighted in the Project window.", "OK");
        }
        else
        {
            Debug.LogWarning("❌ Training scene not found");
            EditorUtility.DisplayDialog("Scene Not Found", 
                "Training scene not found!\n\n" +
                "Use 'SquadMate AI → Create Training Scene (Simple)' to create it first.", "OK");
        }
    }
}
