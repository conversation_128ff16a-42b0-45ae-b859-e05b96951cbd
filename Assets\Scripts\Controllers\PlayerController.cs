using UnityEngine;

public class PlayerController : MonoBeh<PERSON>our
{
    [Head<PERSON>("Player Settings")]
    public float maxHealth = 100f;
    public float moveSpeed = 5f;
    public float rotationSpeed = 100f;
    
    [Header("Player State")]
    public float currentHealth;
    public bool isDowned = false;
    public bool isReviving = false;
    
    [Head<PERSON>("AI Behavior")]
    public bool useAIControl = true;
    public float aiDecisionInterval = 1f;
    
    private Rigidbody rb;
    private float lastAIDecision;
    private Vector3 aiTargetPosition;
    private GameEnvironment environment;
    
    void Start()
    {
        rb = GetComponent<Rigidbody>();
        currentHealth = maxHealth;
        environment = FindObjectOfType<GameEnvironment>();
        
        if (useAIControl)
        {
            aiTargetPosition = transform.position;
        }
    }
    
    void Update()
    {
        if (isDowned) return;
        
        if (useAIControl)
        {
            HandleAIControl();
        }
        else
        {
            HandlePlayerInput();
        }
        
        // Check if player should be downed
        if (currentHealth <= 0 && !isDowned)
        {
            GetDowned();
        }
    }
    
    void FixedUpdate()
    {
        if (isDowned) return;
        
        if (useAIControl)
        {
            MoveTowardsTarget();
        }
    }
    
    private void HandlePlayerInput()
    {
        // Manual player control
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        Vector3 movement = new Vector3(horizontal, 0, vertical) * moveSpeed * Time.deltaTime;
        rb.MovePosition(transform.position + transform.TransformDirection(movement));
        
        // Mouse look
        float mouseX = Input.GetAxis("Mouse X") * rotationSpeed * Time.deltaTime;
        transform.Rotate(0, mouseX, 0);
    }
    
    private void HandleAIControl()
    {
        // Simple AI behavior for the player
        if (Time.time - lastAIDecision > aiDecisionInterval)
        {
            MakeAIDecision();
            lastAIDecision = Time.time;
        }
    }
    
    private void MakeAIDecision()
    {
        if (environment == null) return;
        
        // Simple AI logic: move randomly, avoid enemies, seek items
        Vector3 randomDirection = Random.insideUnitSphere;
        randomDirection.y = 0;
        randomDirection.Normalize();
        
        // Check for nearby enemies
        Transform[] enemies = environment.GetEnemies();
        Vector3 avoidanceVector = Vector3.zero;
        
        foreach (Transform enemy in enemies)
        {
            if (enemy == null) continue;
            
            float distance = Vector3.Distance(transform.position, enemy.position);
            if (distance < 10f)
            {
                Vector3 awayFromEnemy = (transform.position - enemy.position).normalized;
                avoidanceVector += awayFromEnemy * (10f - distance) / 10f;
            }
        }
        
        // Combine random movement with avoidance
        Vector3 finalDirection = (randomDirection + avoidanceVector * 2f).normalized;
        aiTargetPosition = transform.position + finalDirection * Random.Range(3f, 8f);
        
        // Keep within environment bounds
        if (environment != null)
        {
            Vector3 envSize = environment.environmentSize;
            aiTargetPosition.x = Mathf.Clamp(aiTargetPosition.x, -envSize.x/2, envSize.x/2);
            aiTargetPosition.z = Mathf.Clamp(aiTargetPosition.z, -envSize.z/2, envSize.z/2);
        }
    }
    
    private void MoveTowardsTarget()
    {
        Vector3 direction = (aiTargetPosition - transform.position).normalized;
        Vector3 movement = direction * moveSpeed * Time.fixedDeltaTime;
        
        // Check if we're close enough to the target
        if (Vector3.Distance(transform.position, aiTargetPosition) > 1f)
        {
            rb.MovePosition(transform.position + movement);
            
            // Rotate towards movement direction
            if (movement.magnitude > 0.1f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(movement);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.fixedDeltaTime);
            }
        }
    }
    
    public void TakeDamage(float damage)
    {
        if (isDowned) return;
        
        currentHealth -= damage;
        currentHealth = Mathf.Max(currentHealth, 0);
        
        Debug.Log($"Player took {damage} damage. Health: {currentHealth}");
        
        if (currentHealth <= 0)
        {
            GetDowned();
        }
    }
    
    public void GetDowned()
    {
        if (isDowned) return;
        
        isDowned = true;
        currentHealth = 0;
        
        Debug.Log("Player is downed!");
        
        // Stop movement
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
        }
        
        // Visual feedback (you can add animations here)
        transform.localScale = new Vector3(1f, 0.5f, 1f); // Make player shorter when downed
        
        // Notify environment
        if (environment != null && environment.squadMate != null)
        {
            // The squadmate should detect this and come to revive
        }
    }
    
    public void Revive()
    {
        if (!isDowned) return;
        
        isDowned = false;
        currentHealth = maxHealth * 0.3f; // Revive with 30% health
        
        Debug.Log("Player has been revived!");
        
        // Visual feedback
        transform.localScale = Vector3.one; // Restore normal scale
        
        // Give brief invincibility
        StartCoroutine(ReviveInvincibility());
    }
    
    private System.Collections.IEnumerator ReviveInvincibility()
    {
        // Brief period where player can't take damage
        bool originalDownedState = isDowned;
        float invincibilityTime = 2f;
        float elapsed = 0f;
        
        while (elapsed < invincibilityTime)
        {
            // Flash effect (optional)
            if (GetComponent<Renderer>() != null)
            {
                GetComponent<Renderer>().enabled = (Time.time % 0.2f) < 0.1f;
            }
            
            elapsed += Time.deltaTime;
            yield return null;
        }
        
        // Restore normal appearance
        if (GetComponent<Renderer>() != null)
        {
            GetComponent<Renderer>().enabled = true;
        }
    }
    
    public void Heal(float amount)
    {
        if (isDowned) return;
        
        currentHealth += amount;
        currentHealth = Mathf.Min(currentHealth, maxHealth);
        
        Debug.Log($"Player healed for {amount}. Health: {currentHealth}");
    }
    
    // For testing purposes
    void OnGUI()
    {
        if (!useAIControl) return;
        
        GUI.Label(new Rect(10, 10, 200, 20), $"Player Health: {currentHealth:F1}");
        GUI.Label(new Rect(10, 30, 200, 20), $"Player Status: {(isDowned ? "DOWNED" : "ALIVE")}");
        GUI.Label(new Rect(10, 50, 200, 20), $"AI Target: {aiTargetPosition}");
        
        if (isDowned && GUI.Button(new Rect(10, 70, 100, 30), "Force Revive"))
        {
            Revive();
        }
        
        if (GUI.Button(new Rect(10, 110, 100, 30), "Take Damage"))
        {
            TakeDamage(25f);
        }
    }
    
    void OnDrawGizmosSelected()
    {
        if (useAIControl)
        {
            // Draw AI target position
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(aiTargetPosition, 0.5f);
            Gizmos.DrawLine(transform.position, aiTargetPosition);
        }
        
        // Draw health indicator
        Gizmos.color = Color.Lerp(Color.red, Color.green, currentHealth / maxHealth);
        Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, 0.3f);
    }
}
