# 🔧 VS Code + Unity 6 Setup for SquadMate AI Development

Perfect! VS Code with Unity extension is an excellent choice for ML-Agents development. Here's how to optimize your setup.

## ✅ **VS Code Extensions for Unity 6 ML-Agents**

### **Essential Extensions (Install These):**

1. **C# Dev Kit** (Microsoft) - Latest C# support
2. **Unity** (Microsoft) - Unity integration you already have
3. **C#** (Microsoft) - Core C# language support
4. **IntelliCode** (Microsoft) - AI-powered code completion
5. **Debugger for Unity** (Unity Technologies) - Advanced debugging

### **ML-Agents Specific Extensions:**
6. **Python** (Microsoft) - For training scripts
7. **Pylance** (Microsoft) - Python language server
8. **YAML** (Red Hat) - For ML-Agents config files
9. **JSON** (Built-in) - For decision tree configs

### **Optional but Helpful:**
10. **GitLens** - Git integration
11. **Bracket Pair Colorizer** - Better code readability
12. **Error Lens** - Inline error display
13. **Todo Tree** - Track TODO comments

## ⚙️ **VS Code Settings for Unity 6**

Create `.vscode/settings.json` in your project:

```json
{
    "dotnet.defaultSolution": "SquadMate-AI.sln",
    "omnisharp.useModernNet": true,
    "omnisharp.enableRoslynAnalyzers": true,
    "omnisharp.enableEditorConfigSupport": true,
    "files.exclude": {
        "**/.git": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/*.meta": true,
        "**/Library": true,
        "**/Temp": true,
        "**/Obj": true,
        "**/Build": true,
        "**/Builds": true,
        "**/Logs": true,
        "**/UserSettings": true
    },
    "search.exclude": {
        "**/Library": true,
        "**/Temp": true,
        "**/Obj": true,
        "**/Build": true,
        "**/Builds": true,
        "**/Logs": true
    },
    "files.associations": {
        "*.cs": "csharp",
        "*.yaml": "yaml",
        "*.yml": "yaml"
    },
    "python.defaultInterpreterPath": "python",
    "python.terminal.activateEnvironment": true,
    "yaml.schemas": {
        "https://raw.githubusercontent.com/Unity-Technologies/ml-agents/main/config/trainer_config_schema.json": [
            "config/*.yaml",
            "config/*.yml"
        ]
    }
}
```

## 🚀 **VS Code Tasks for Unity 6 ML-Agents**

Create `.vscode/tasks.json`:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Unity: Generate Project Files",
            "type": "shell",
            "command": "Unity",
            "args": [
                "-batchmode",
                "-quit",
                "-projectPath",
                "${workspaceFolder}",
                "-executeMethod",
                "UnityEditor.SyncVS.SyncSolution"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "ML-Agents: Validate Setup",
            "type": "shell",
            "command": "python",
            "args": ["validate_setup.py"],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            }
        },
        {
            "label": "ML-Agents: Start Training",
            "type": "shell",
            "command": "python",
            "args": [
                "train_squadmate.py",
                "--run-id=squadmate_vscode",
                "--tensorboard"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new"
            }
        },
        {
            "label": "Unity 6: Quick Start",
            "type": "shell",
            "command": "python",
            "args": ["unity6_quickstart.py"],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new"
            }
        }
    ]
}
```

## 🐛 **VS Code Launch Configuration**

Create `.vscode/launch.json` for debugging:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Unity Editor",
            "type": "unity",
            "request": "launch"
        },
        {
            "name": "Unity Player",
            "type": "unity",
            "request": "launch",
            "target": "player"
        },
        {
            "name": "Python: ML-Agents Training",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/train_squadmate.py",
            "args": ["--run-id=debug_session"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}"
        }
    ]
}
```

## 📁 **Recommended Folder Structure in VS Code**

```
SquadMate-AI/
├── .vscode/
│   ├── settings.json
│   ├── tasks.json
│   └── launch.json
├── Assets/
│   ├── Scripts/
│   │   ├── Agents/
│   │   ├── Controllers/
│   │   ├── Environment/
│   │   └── Utils/
│   ├── Prefabs/
│   ├── Scenes/
│   └── Data/
├── config/
├── results/
├── models/
└── documentation/
```

## 🔥 **VS Code Workflow for Unity 6 ML-Agents**

### **1. Project Setup**
1. Open VS Code
2. File → Open Folder → Select your Unity project
3. VS Code will detect Unity and prompt to install extensions
4. Accept all Unity-related extension installations

### **2. Code Development**
- **IntelliSense**: Full autocomplete for Unity and ML-Agents APIs
- **Error Detection**: Real-time error highlighting
- **Refactoring**: Advanced code refactoring tools
- **Debugging**: Set breakpoints in Unity scripts

### **3. ML-Agents Integration**
- **Config Editing**: YAML syntax highlighting for training configs
- **Python Scripts**: Full Python support for training scripts
- **Terminal Integration**: Run training commands directly in VS Code
- **Git Integration**: Version control for your AI development

### **4. Unity 6 Specific Features**
- **Solution Files**: VS Code works with Unity 6's improved .sln files
- **Assembly Definitions**: Better support for Unity 6's assembly system
- **Package Manager**: Integration with Unity 6's package system

## 🛠️ **VS Code Shortcuts for Unity Development**

### **Essential Shortcuts:**
- `Ctrl+Shift+P` - Command Palette
- `Ctrl+.` - Quick Fix (very useful for Unity)
- `F12` - Go to Definition
- `Shift+F12` - Find All References
- `Ctrl+K Ctrl+D` - Format Document
- `Ctrl+Shift+F` - Search in Files
- `Ctrl+`` ` - Toggle Terminal

### **Unity Specific:**
- `Ctrl+Shift+M` - Problems Panel (see Unity errors)
- `F5` - Start Debugging (attach to Unity)
- `Ctrl+F5` - Run Without Debugging

## 🎯 **VS Code Tips for ML-Agents Development**

### **1. Code Snippets**
Create custom snippets for ML-Agents code. Add to `csharp.json`:

```json
{
    "ML-Agents Observation": {
        "prefix": "mla-obs",
        "body": [
            "public override void CollectObservations(VectorSensor sensor)",
            "{",
            "    // Add observations here",
            "    sensor.AddObservation($1);",
            "}"
        ],
        "description": "ML-Agents observation method"
    },
    "ML-Agents Action": {
        "prefix": "mla-action",
        "body": [
            "public override void OnActionReceived(ActionBuffers actionBuffers)",
            "{",
            "    // Continuous actions",
            "    float $1 = actionBuffers.ContinuousActions[0];",
            "    ",
            "    // Discrete actions", 
            "    int $2 = actionBuffers.DiscreteActions[0];",
            "}"
        ],
        "description": "ML-Agents action method"
    }
}
```

### **2. Multi-File Editing**
- Use split editor for comparing scripts
- Open multiple terminal tabs for different tasks
- Use breadcrumbs for navigation in large files

### **3. Integrated Development**
- Edit Unity scripts in VS Code
- Run training scripts in integrated terminal
- Monitor training with TensorBoard in browser
- Debug Unity in real-time

## 🔧 **Troubleshooting VS Code + Unity 6**

### **Common Issues:**

#### **IntelliSense Not Working**
1. Ensure C# Dev Kit is installed
2. Run "Unity: Generate Project Files" task
3. Restart VS Code
4. Check that .sln file exists in project root

#### **Unity Extension Not Detecting Project**
1. Make sure you opened the Unity project folder (not parent folder)
2. Verify Unity 6 is properly installed
3. Check that Assembly-CSharp.csproj exists

#### **Python Integration Issues**
1. Install Python extension
2. Select correct Python interpreter (Ctrl+Shift+P → "Python: Select Interpreter")
3. Ensure ML-Agents packages are installed in selected environment

#### **YAML Schema Validation**
1. Install YAML extension
2. Configure schema in settings.json (provided above)
3. Restart VS Code

## 🚀 **Advanced VS Code Features for Unity 6**

### **1. Live Share**
- Collaborate on AI development in real-time
- Share training sessions with team members
- Debug together remotely

### **2. Remote Development**
- Develop on powerful remote machines
- Train on cloud instances while coding locally
- Use containers for consistent environments

### **3. Extensions for AI Development**
- **Jupyter** - For data analysis and visualization
- **TensorBoard** - Monitor training directly in VS Code
- **GitHub Copilot** - AI-powered code suggestions

## 🎉 **You're All Set!**

Your VS Code + Unity 6 setup is now optimized for ML-Agents development:

✅ **Full IntelliSense** for Unity and ML-Agents APIs
✅ **Integrated debugging** with Unity 6
✅ **Python support** for training scripts
✅ **YAML validation** for ML-Agents configs
✅ **Custom tasks** for common operations
✅ **Advanced debugging** capabilities

## 📋 **Next Steps:**

1. **Install recommended extensions** from the list above
2. **Copy the configuration files** to your `.vscode` folder
3. **Open your Unity project** in VS Code
4. **Generate Unity project files** using the task
5. **Start developing** your SquadMate AI!

Your development environment is now **professional-grade** and optimized for Unity 6 ML-Agents development! 🚀
