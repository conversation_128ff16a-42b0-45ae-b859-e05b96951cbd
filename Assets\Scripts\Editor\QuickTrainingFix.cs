using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;

/// <summary>
/// Quick fix for common training issues
/// </summary>
public class QuickTrainingFix : EditorWindow
{
    [MenuItem("SquadMate AI/Quick Training Fix")]
    public static void ShowWindow()
    {
        GetWindow<QuickTrainingFix>("Quick Fix");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Quick Training Fix", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("If nothing happens when you press Play:");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🔧 FIX EVERYTHING NOW", GUILayout.Height(50)))
        {
            FixEverything();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("📊 Add Training Debugger", GUILayout.Height(30)))
        {
            AddTrainingDebugger();
        }
        
        if (GUILayout.But<PERSON>("🎮 Create Simple Test Scene", GUILayout.Height(30)))
        {
            CreateSimpleTestScene();
        }
    }
    
    public static void FixEverything()
    {
        Debug.Log("🔧 QUICK TRAINING FIX - FIXING EVERYTHING!");
        Debug.Log("=" * 60);
        
        // Step 1: Ensure basic GameObjects exist
        EnsureBasicGameObjects();
        
        // Step 2: Add all required components
        AddAllRequiredComponents();
        
        // Step 3: Configure ML-Agents properly
        ConfigureMLAgents();
        
        // Step 4: Set up all references
        SetupAllReferences();
        
        // Step 5: Add debug component
        AddTrainingDebugger();
        
        // Step 6: Configure for immediate testing
        ConfigureForTesting();
        
        Debug.Log("=" * 60);
        Debug.Log("✅ EVERYTHING FIXED!");
        Debug.Log("🎮 Press Play now - you should see activity in Console and GUI!");
        
        EditorUtility.DisplayDialog("Fixed!", 
            "Training scene has been fixed!\n\n" +
            "✅ All components added\n" +
            "✅ All references configured\n" +
            "✅ Debug GUI enabled\n" +
            "✅ Ready to train!\n\n" +
            "Press Play to start training!", "Great!");
    }
    
    private static void EnsureBasicGameObjects()
    {
        Debug.Log("🎮 Ensuring basic GameObjects exist...");
        
        // Create Player if missing
        GameObject player = GameObject.Find("Player");
        if (player == null)
        {
            player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.name = "Player";
            player.transform.position = new Vector3(0, 1, 0);
            player.transform.localScale = new Vector3(1, 2, 1);
            
            // Add Rigidbody
            Rigidbody playerRb = player.AddComponent<Rigidbody>();
            playerRb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
            
            Debug.Log("✅ Created Player GameObject");
        }
        
        // Create SquadMate if missing
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate == null)
        {
            squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            squadmate.name = "SquadMate";
            squadmate.transform.position = new Vector3(3, 1, 0);
            squadmate.transform.localScale = new Vector3(1, 2, 1);
            
            // Add Rigidbody
            Rigidbody squadmateRb = squadmate.AddComponent<Rigidbody>();
            squadmateRb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
            
            Debug.Log("✅ Created SquadMate GameObject");
        }
        
        // Create Environment if missing
        GameObject environment = GameObject.Find("Environment");
        if (environment == null)
        {
            environment = new GameObject("Environment");
            Debug.Log("✅ Created Environment GameObject");
        }
        
        // Apply colors for easy identification
        ApplyColors(player, squadmate);
    }
    
    private static void ApplyColors(GameObject player, GameObject squadmate)
    {
        // Create simple colored materials
        Material blueMat = new Material(Shader.Find("Standard"));
        blueMat.color = Color.blue;
        
        Material greenMat = new Material(Shader.Find("Standard"));
        greenMat.color = Color.green;
        
        if (player != null)
            player.GetComponent<Renderer>().material = blueMat;
        
        if (squadmate != null)
            squadmate.GetComponent<Renderer>().material = greenMat;
    }
    
    private static void AddAllRequiredComponents()
    {
        Debug.Log("🔧 Adding all required components...");
        
        GameObject player = GameObject.Find("Player");
        GameObject squadmate = GameObject.Find("SquadMate");
        GameObject environment = GameObject.Find("Environment");
        
        // Add PlayerController to Player
        if (player != null && player.GetComponent<PlayerController>() == null)
        {
            player.AddComponent<PlayerController>();
            Debug.Log("✅ Added PlayerController to Player");
        }
        
        // Add all SquadMate components
        if (squadmate != null)
        {
            if (squadmate.GetComponent<SquadMateAgent>() == null)
            {
                squadmate.AddComponent<SquadMateAgent>();
                Debug.Log("✅ Added SquadMateAgent to SquadMate");
            }
            
            if (squadmate.GetComponent<RewardCalculator>() == null)
            {
                squadmate.AddComponent<RewardCalculator>();
                Debug.Log("✅ Added RewardCalculator to SquadMate");
            }
            
            if (squadmate.GetComponent<BehaviorParameters>() == null)
            {
                squadmate.AddComponent<BehaviorParameters>();
                Debug.Log("✅ Added BehaviorParameters to SquadMate");
            }
        }
        
        // Add Environment components
        if (environment != null)
        {
            if (environment.GetComponent<GameEnvironment>() == null)
            {
                environment.AddComponent<GameEnvironment>();
                Debug.Log("✅ Added GameEnvironment to Environment");
            }
        }
    }
    
    private static void ConfigureMLAgents()
    {
        Debug.Log("🧠 Configuring ML-Agents...");
        
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            BehaviorParameters behaviorParams = squadmate.GetComponent<BehaviorParameters>();
            if (behaviorParams != null)
            {
                behaviorParams.BehaviorName = "SquadMate";
                behaviorParams.BehaviorType = BehaviorType.Default;
                behaviorParams.TeamId = 0;
                behaviorParams.UseChildSensors = true;
                
                Debug.Log("✅ Configured BehaviorParameters");
            }
        }
    }
    
    private static void SetupAllReferences()
    {
        Debug.Log("🔗 Setting up all references...");
        
        GameObject player = GameObject.Find("Player");
        GameObject squadmate = GameObject.Find("SquadMate");
        GameObject environment = GameObject.Find("Environment");
        
        // Configure SquadMate references
        if (squadmate != null && player != null)
        {
            SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                agent.player = player.transform;
                
                if (environment != null)
                {
                    agent.environment = environment.GetComponent<GameEnvironment>();
                }
                
                Debug.Log("✅ Configured SquadMateAgent references");
            }
        }
        
        // Configure Environment references
        if (environment != null)
        {
            GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
            if (gameEnv != null && player != null && squadmate != null)
            {
                gameEnv.player = player.transform;
                gameEnv.squadMate = squadmate.GetComponent<SquadMateAgent>();
                gameEnv.environmentSize = new Vector3(50f, 0f, 50f);
                
                Debug.Log("✅ Configured GameEnvironment references");
            }
        }
    }
    
    public static void AddTrainingDebugger()
    {
        Debug.Log("📊 Adding Training Debugger...");
        
        GameObject debugger = GameObject.Find("TrainingDebugger");
        if (debugger == null)
        {
            debugger = new GameObject("TrainingDebugger");
            debugger.AddComponent<TrainingDebugger>();
            
            // Configure the debugger
            TrainingDebugger debugComponent = debugger.GetComponent<TrainingDebugger>();
            debugComponent.showDebugInfo = true;
            debugComponent.showGUI = true;
            debugComponent.logRewards = true;
            
            Debug.Log("✅ Added TrainingDebugger with GUI");
        }
    }
    
    private static void ConfigureForTesting()
    {
        Debug.Log("🎮 Configuring for immediate testing...");
        
        // Set reasonable time scale
        Time.timeScale = 1f;
        
        // Enable some basic logging
        Debug.Log("✅ Configured for testing");
        Debug.Log("💡 After pressing Play, you should see:");
        Debug.Log("   • Debug GUI on the left side of Game view");
        Debug.Log("   • Console messages about agent status");
        Debug.Log("   • Agent and Player moving around");
        Debug.Log("   • Reward messages in Console");
    }
    
    public static void CreateSimpleTestScene()
    {
        Debug.Log("🎮 Creating Simple Test Scene...");
        
        // Clear scene first
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name != "Main Camera")
            {
                DestroyImmediate(obj);
            }
        }
        
        // Create simple ground
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.localScale = new Vector3(5, 1, 5);
        
        // Run the full fix
        FixEverything();
        
        Debug.Log("✅ Simple test scene created!");
    }
}
