﻿== Error ==
エラー

== NoResultFileSelected ==
結果ファイルが選択されていません

== UsageCaption ==
使用方法

== Usage ==
使用方法:binmergetool <mergeOptions>

    mergeOptions:<generalFiles> <baseFile> [<baseSymbolicName>] [[<automatic>] <resultFile>] [<mergeType>] [<generalOptions>]

        baseFile:              {-b | --base}=<filename>
        baseSymbolicName:      {-bn | --basesymbolicname}=<symbolicname>
        automatic:             -a | --automatic
        resultFile:            {-r | --result}=<filename>
        mergeType:             {-m | --mergeresolutiontype}={onlyone | onlysrc | onlydst | try | forced}

    generalFiles:<sourceFile> [<srcSymbolicName>] <destination> [<dstSymbolicName>]

        sourceFile:            {-s | --source}=<filename>
        srcSymbolicName:       {-sn | --srcsymbolicname}=<symbolicname>
        destinationFile:       {-d | --destination}=<filename>
        dstSymbolicName:       {-dn | --dstsymbolicname}=<symbolicname>

        
    例:
        
        binmergetool -s=file1.txt -d=file2.txt
        binmergetool -s=file1.txt -b=file0.txt --destination=file2.txt
        binmergetool --base=file0.txt -d=file2.txt --source=file1.txt --automatic --result=result.txt
        binmergetool -b=file0.txt -s=file1.txt -d=file2.txt -a -r=result.txt -m=onlyone

== DiffRequiresTwoArguments ==
差分を表示するには 2 つのファイル (のみ) が必要です

== UnsupportedFileTypeForDiff ==
ファイルタイプでバイナリ差分がサポートされていません。サポートされるのは画像 (JPEG、PNG、GIF、BMP) のみです。

== SaveChanges ==
結果ファイルに対する変更を保存しますか?

== ExitPrompt ==
終了

== CantLoadImage ==
画像 '{0}' をロードできません。

== NoArguments ==
引数が指定されていません

== UsageHint ==
このユーティリティのヘルプをさらに表示するには、[binmergetool -?] を使用してください