== CMD_DESCRIPTION_ACCESS_TOKEN ==
Allows the user to manage Access Tokens.

== CMD_USAGE_ACCESS_TOKEN ==
Usage:

    cm ^accesstoken <command> [options]

Commands:

    - ^create
    - ^list
    - ^reveal
    - ^revoke

    To get more information about each command run:
    cm ^accesstoken <command> --^usage
    cm ^accesstoken <command> --^help

== CMD_HELP_ACCESS_TOKEN ==
Examples:

    cm ^accesstoken ^create "To be used by the Build Server"
    cm ^accesstoken ^list
    cm ^accesstoken ^reveal 19c57d0f-c525-4767-8670-82f7ecc2ccdb
    cm ^accesstoken ^revoke 19c57d0f-c525-4767-8670-82f7ecc2ccdb

== CMD_DESCRIPTION_ACCESS_TOKEN_CREATE == 
Creates a new access token.

== CMD_USAGE_ACCESS_TOKEN_CREATE == 
Usage:

    cm ^accesstoken ^create <description> [<repserverspec>]
        [--^format=<str_format>] [--dateformat=<str_date_format>]

    description         A description that helps identify the purpose
                        of the access token.
    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about rep server
                        specs.)
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^dateformat        Sets the output format to print dates.

== CMD_HELP_ACCESS_TOKEN_CREATE ==
Remarks:

    The access token lifespan will be determined by the server configuration.
    It is not possible to set the token lifespan at creation time.

    Output format parameters (--^format option):

    This command accepts a format string to show the output.

    The output parameters of this command are the following:
    {^id}             | {0}         ID of the access token.
    {^description}    | {1}         Description of the access token.
    {^owner}          | {2}         Owner of the access token.
    {^creationdate}   | {3}         Creation date of the access token.
    {^expirationdate} | {4}         Expiration date of the access token.
    {^lastusedate}    | {5}         Last use date of the access token.
    {^tab}                          Inserts a tab space.
    {^newline}                      Inserts a new line.

    If the format parameter is not specified, the output will be printed
    using a table format.

Examples:

    cm ^accesstoken ^create "Token for Build Server"
    (Creates a new access token with the provided description against the default
     repserver.)

    cm ^accesstoken ^create "Token for Build Server" skull:8087
    (Creates a new access token with the provided description on repserver skull:8087.)

    cm ^accesstoken ^create "Token for Build Server" --^format="{^id}"
    (Creates a new access token with the provided description against the default
     repserver and only displays its ID.)

== CMD_DESCRIPTION_ACCESS_TOKEN_LIST ==
Lists access tokens.

== CMD_USAGE_ACCESS_TOKEN_LIST ==
Usage:

    cm ^accesstoken ^list [<repserverspec>]
        [--^format=<str_format>] [--^dateformat=<str_date_format>]

    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about rep server
                        specs.)
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^dateformat        Sets the output format to print dates.

== CMD_HELP_ACCESS_TOKEN_LIST ==
Remarks:

    You can only list your own access tokens. Revoked access tokens will not be
    returned as part of the result. On the other hand, expired access tokens will.

    Output format parameters (--^format option):

    This command accepts a format string to show the output.

    The output parameters of this command are the following:
    {^id}             | {0}         ID of the access token.
    {^description}    | {1}         Description of the access token.
    {^owner}          | {2}         Owner of the access token.
    {^creationdate}   | {3}         Creation date of the access token.
    {^expirationdate} | {4}         Expiration date of the access token.
    {^lastusedate}    | {5}         Last use date of the access token.
    {^tab}                          Inserts a tab space.
    {^newline}                      Inserts a new line.

    If the format parameter is not specified, the output will be printed
    using a table format.

Examples:

    cm ^accesstoken ^list
    (Lists access tokens on the default repserver.)

    cm ^accesstoken ^list skull:8087
    (Lists access tokens on repserver skull:8087.)

    cm ^accesstoken ^list --^format="{^id}"
    (Lists access tokens on the default repserver and only displays their IDs.)

== CMD_DESCRIPTION_ACCESS_TOKEN_REVOKE ==
Revokes an existing access token.

== CMD_USAGE_ACCESS_TOKEN_REVOKE ==
Usage:

    cm ^accesstoken ^revoke <id> [<repserverspec>]

    id                  The ID of the access token to be revoked.
    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about rep server
                        specs.)

== CMD_HELP_ACCESS_TOKEN_REVOKE ==
Remarks:

    You can only revoke your own access tokens. Trying to revoke an access token
    that does not exist or does not belong to you will result in an error.
    (See '^cm ^accesstoken ^list --^help' for further information.)

    To revoke an access token you need to provide the full ID.

    Revoking an access token does not mean that the already revealed Unity VCS
    tokens will stop functioning immediately.
    The maximum amount of time that can pass between revoking an access token
    and the revealed Unity VCS token stopping working depends on the
    'TokenExpirationTimeSpan' configuration key in your server.conf.
    By default, it is 1 hour.
    If you need to know the exact value configured for your Unity VCS server,
    please contact your repository server administrator.
    (See '^cm ^accesstoken ^reveal --^help' for further information.)

Examples:

    cm ^accesstoken ^revoke de0be8a4-2a7a-44c6-9c4c-82c8222dbc6f
    (Revokes the access token with the provided ID from the default repserver.)

    cm ^accesstoken ^revoke de0be8a4-2a7a-44c6-9c4c-82c8222dbc6f skull:8087
    (Revokes the access token with the provided ID from repserver skull:8087.)

== CMD_DESCRIPTION_ACCESS_TOKEN_REVEAL ==
Reveals an access token so it can be used as the authentication credentials.

== CMD_USAGE_ACCESS_TOKEN_REVEAL ==
Usage:

    cm ^accesstoken ^reveal <id> [<repserverspec>]

    id                  The ID of the access token to be revealed.
    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about rep server
                        specs.)


== CMD_HELP_ACCESS_TOKEN_REVEAL ==
Remarks:

    You can only reveal your own access tokens. Trying to reveal an access token
    that does not exist or does not belong to you will result in an error.
    (See '^cm ^accesstoken ^list --^help' for further information.)

    To reveal an access token you need to provide the full ID.

    The revealed token can be used for automation purposes. You can pass it down
    in subsequent 'cm' commands (either in the same machine or a different one)
    by using the following 'cm' arguments (replace accordingly):

        $ cm ^repo ^list \
            --^username=^Peter \
            --^workingmode=^OIDCWorkingMode \
            --^token="the revealed token" \
            --^server=skull:8087

    The revealed token will be valid from the very moment it was revealed, and
    will be automatically renewed as long as the related access token is not
    expired nor revoked.

Examples:

    cm ^accesstoken ^reveal d2f43753-f612-4e51-b9b4-3c2883d7cf95
    (Reveals the access token with the provided ID on the default repserver.)

    cm ^accesstoken ^reveal d2f43753-f612-4e51-b9b4-3c2883d7cf95 skull:8087
    (Reveals the access token with the provided ID on repserver skull:8087.)

== CMD_DESCRIPTION_ACL ==
Sets permissions on an object.

== CMD_USAGE_ACL ==
Usage:

    cm ^acl (--^user=<usr_name> | --^group=<group_name>)
           (-^allowed|-^denied|-^overrideallowed|-^overridedenied=+|-<permission>[,...])[,...]
           <objectspec>

Options:
    --^user             User name.
    --^group            Group name.
    -^allowed           Enables the specified permission or permissions. Use a
                       comma to separate permissions. (Use 'cm ^showpermissions'
                       to display all the available permissions.)
    -^denied            Denies the specified permission or permission. Use a
                       comma to separate permissions. (Use 'cm ^showpermissions'
                       to display all the available permissions.)
    -^overrideallowed   Overrides the allowed permission or permissions. Use a
                       comma to separate permissions. (Use 'cm ^showpermissions'
                       to display all the available permissions.)
    -^overridedenied    Overrides the denied permission or permissions. Use a
                       comma to separate permissions. (Use 'cm ^showpermissions'
                       to display all the available permissions.)
    objectspec         The object whose permissions will be set.
                       The valid objects for this command are:
                       repserver, repository, branch, label, and attribute.
                       (Use 'cm ^help ^objectspec' to learn more about specs.)

Special usage for secured paths:
    cm ^acl [(--^user=<usr_name> | --^group=<group_name>)
            (-^allowed|-^denied|-^overrideallowed|-^overridedenied=+|-<permission>[,...])[,...]]
            [--^delete] [--^branches=[+ | -]<branch>[,...]] 
            <spec>

    --^delete           Removes a secured path.
                       See Remarks for more info.
    --^branches         Sets the secured path permissions to a group of branches.
                       Use a comma to separate branches. 
                       Optionally, each branch can be preceded by the + or -
                       sign to specify whether a branch must be added or deleted
                       to the list when editing.
                       See Remarks for more info.
    spec               The secured path where to set the permissions.

== CMD_HELP_ACL ==
Configuring permissions requires understanding how Unity VCS security works.
Check the Security Guide to learn how permissions work:
https://www.plasticscm.com/download/help/securityguide

Remarks:

    This command sets permissions for a user or group on the specified objects,
    repositories, branches, labels and/or server paths.

    Object specs:
    (Use 'cm ^help ^objectspec' to learn how to specify objects.)
    The '^acl' command uses a special type of spec: secured paths.

    Secured paths specs: ^path:server_path[#tag]
    Examples: 
    - ^path:/src/foo.c
    - ^path:/doc/pdf
    - ^path:/doc/pdf#documents

    Permission action:
    Use -^allowed and -^denied to specify what permissions to set.
    Use -^overrideallowed and -^overridedenied arguments to specify what
    permissions to override.

    Each action requires a permission list separated by commas.

    Permission names:
    Each permission name is preceded by + or - symbol.
    The + symbol sets the permission and the - symbol clears it.
    To see the permissions of an object, use the 'cm ^showacl' command.

    Overridden permissions:
    Overriding a permission using -^overrideallowed and -^overridedenied
    allows you to bypass inheritance.
    It is helpful to bypass permissions set at the repository or server
    level.
    Example:
    cm ^acl --^user=vio -^allowed=+^ci -^overrideallowed=+^ci ^br:qa@test
    (Allows user 'vio' to checkin on the branch 'qa' on repo 'test'
    even if she has the permission denied at the repo level.)

    Server path permissions (a.k.a. secured paths):
    - It is possible to specify permissions for a given server path.
    - These permissions are checked during the checkin operation.
    - These permissions can also be checked during the update operation,
      and can be used as a way to prevent certain directories and files to
      be downloaded to the workspace.
    - For every item to checkin, the server tries to match the item path
      with a secured path. If it matches, the checkin operation checks
      whether the item has permissions to be checked in.

    The permissions that can be defined for a secured path are the
    following:
        '^ci', '^change', '^add', '^move', '^rm', '^read'

    If the permissions check is not successful for any of the involved
    items, the checkin operation will be rolled back.

    To set secured path permissions to a group of branches, use the
    --^branches option.
    Example:
      cm ^acl --^user=jo -^denied=+^ci ^path:/src#rule0 --^branches=main,main/rel0

    To edit the ACL associated to the secured path, the tag is useful.
    Example:
      cm ^acl --^user=jo -^denied=+^rm ^path:/src#rule0
      (Without the tag, the list of branches would need to be specified
      again.)

    The list of branches of the secured path can be edited.
    Example:
      cm ^acl ^path:/src#rule0 --^branches=-main,+main/rel1
      (Removes 'main' from the list and adds 'main/rel1'.)

    To remove a secured path, use the --^delete argument.
    Example:
      cm ^acl --^user=jo --^delete ^path:/src#rule0

    Inheritance:
    Inheritance is an option that comes from the days of Plastic SCM 3.0.
    It is advanced, but almost deprecated.
    It lets an object inherit its permissions from any other object,
    overriding the default inheritance relationships.

    Use the option -^cut to cut the inheritance chain.
    Use the option -^cutncpy to cut and copy the current inherited
    permissions. (This is inspired on the Windows filesystem permissions
    where you can cut inheritance but retain the actual permissions.)

    The -^inherit option allows the user to inherit from an object spec.
    Example: '-^inherit=object_spec'

Examples:

    cm ^acl --^user=danipen -^denied=+^ci ^rep:core
    (Denies checkin for user 'danipen' on repo 'core'.)

    cm ^acl --^group=developers -^allowed=+^view,-^read -^denied=+^chgperm ^br:main
    (The command grants view permission, clears read permission,
    and denies chgperm permission to 'developers' group in 'main' branch.)

Secured path examples:

    cm ^acl --^group=devs -^denied=+^ci ^path:/server#rel --^branches=main,main/2.0
    (The command denies the checkin permission to 'devs' group for any path
    that matches '/server' in the branches 'main' and 'main/2.0'. The tag '#rel'
    is created to be able to refer to it later.)

    cm ^acl ^path:/server#rel --^branches=-/main,+/main/Rel2.1
    (Updates the secured path '/server' whose tag is 'rel', removing the
    'main' branch and adding the branch 'main/Rel2.1' to the branch
    group the secured path applies to. Considering the previous example,
    now the branches list will contain 'main/Rel2.1' and 'main/2.0'.)

    cm ^acl --^user=vsanchezm -^allowed=-^read -^overrideallowed=+^read ^path:/doc
    (Removes '^read' permission to 'vsanchezm' overriding it in '/doc' path.)

== CMD_DESCRIPTION_ACTIVATEUSER ==
Activates a licensed user.

== CMD_USAGE_ACTIVATEUSER ==
Usage:

    cm ^activateuser | ^au <user-name>[ ...] [--^server=<rep-server-spec>]

Options:
    --^server=<rep-server-spec>  Activates the user in the specified server.
                                If no server is specified, executes the command
                                in the default server in the client.conf file.
                                (Use 'cm ^help ^objectspec' to learn more about
                                repserver specs.)
    user-name                    The user name or user names to activate. Use double quotes (" ")
                                to specify user names containing spaces. Use a whitespace to
                                separate user names.

== CMD_HELP_ACTIVATEUSER ==
Remarks:

    To activate a user, it must have been previously deactivated.
    By default, a user is activated the first time they perform a write
    operation in Unity VCS. The user is automatically activated only if
    the maximum number of users has not been exceeded.

    (See the 'cm ^help ^deactivateuser' command for more information about
    deactivating Unity VCS users.)

Examples:

    cm ^activateuser john
    cm ^activateuser david "mary collins"
    cm ^au peter --^server=localhost:8087

== CMD_DESCRIPTION_ADD ==
Adds an item to version control.

== CMD_USAGE_ADD ==
Usage:

    cm ^add [-^R | -^r | --^recursive] [--^silent] [--^ignorefailed]
           [--^skipcontentcheck] [--^coparent] [--^filetypes=<file>] [--^noinfo]
           [--^format=<str-format>] [--^errorformat=<str-format>] 
           <item-path>[ ...]

Options:

    -^R | -^r | --^recursive   Adds items recursively.
    --^silent            Does not show any output.
    --^ignorefailed      If an item cannot be added, the add operation will
                        continue without it. Note: If a directory cannot be
                        added, its content is not added.
    --^skipcontentcheck  When the extension is not enough to set the file as
                        text or binary, it will be set as binary instead of
                        checking the content to detect the type. This is done
                        to increase performance on huge checkins.
    --^coparent          Runs a checkout of the parent of the item being added.
    --^filetypes         The filetypes file to use. Check the following link for
                        more information:
                        http://blog.plasticscm.com/2008/03/custom-file-types.html
    --^noinfo            Doesn't print progress information.
    --^format            Retrieves the output message in a specific format. Check
                        the examples for more information.
    --^errorformat       Retrieves the error message (if any) in a specific
                        format. Check the examples for more information.
    item-path            The item or items to add. Use double quotes (" ") to specify
                        paths containing spaces. Use a whitespace to separate items. 
                        Use * to add all the contents of the current directory.

== CMD_HELP_ADD ==
Remarks:

    Requirements to add items:
    - The parent directory of the item to add must have been previously added.

Reading input from stdin:

    The '^add' command can read paths from stdin. To do this, pass a single dash
    "-".
    Example: 
      cm ^add -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which files to add.
    Example:
      dir /S /B *.c | cm ^add -
      (In Windows, adds all .c files in the workspace.)

Examples:

    cm ^add file1.txt file2.txt
    (Adds 'file1.txt' and 'file2.txt' items.)

    cm ^add c:\workspace\file.txt
    (Adds 'file.txt' item in path 'c:\workspace'.)

    cm ^add -^R c:\workspace\src
    (Recursively adds 'src'.)

    cm ^add -^R *
    (Recursively adds all the contents of the current directory.)

    cm ^add -^R * --^filetypes=filetypes.conf
    (Recursively adds all the contents of the current directory, using
    'filetypes.conf' to assign a type to each file based on its extension,
    instead of checking its content.)

    cm ^add --^coparent c:\workspace\dir\file.txt
    (Adds 'file.txt' to source control, and performs a checkout of 'dir'.)

    cm ^add -^R * --^format="ADD {0}" --^errorformat="ERR {0}"
    (Recursively adds all the contents of the current directory, printing
    '^ADD <item>' for successfully added files, and '^ERR <item>' for items that
    could not be added.)

== CMD_DESCRIPTION_ADMIN ==
Executes administrative commands on the server.

== CMD_USAGE_ADMIN ==
Usage:

    cm ^admin <command> [options]

Commands:

    - ^readonly

    To get more information about each command run:
    cm ^admin <command> --^usage
    cm ^admin <command> --^help

== CMD_HELP_ADMIN ==
Remarks:
    Only the server administrator can execute administrative commands.

Examples:

    cm ^admin ^readonly ^enter
    cm ^admin ^readonly ^status

== CMD_DESCRIPTION_ADMIN_READONLY ==
Enables/disables the server readonly mode.

== CMD_USAGE_ADMIN_READONLY ==
Usage:

    cm ^admin ^readonly (^enter | ^leave | ^status) [<server>]

Options:

    ^enter   The server enters read-only mode. Write operations will be rejected.
    ^leave   The server leaves read-only mode.
    ^status  Shows the server read-only mode status.
    server   Executes the command in the specified server (server:port). (Use 
             'cm ^help ^objectspec' to learn more about server specs.)
             If no server is specified, the command works with the server of the
             current workspace.
             If the current path is not in a workspace, the command works with
             the default server defined in the client.conf config file.

== CMD_HELP_ADMIN_READONLY ==
Remarks:
    Only the server administrator can enter the server readonly mode.

Examples:

    cm ^admin ^readonly ^enter diana:8086
    cm ^admin ^readonly ^leave

== CMD_DESCRIPTION_ANNOTATE ==
Shows the changeset where each line of a file was last modified and its author.

== CMD_USAGE_ANNOTATE ==
Usage:

    cm ^annotate | ^blame <spec>[ ...]
        [--^format=<str_format>]
        [--^comparisonmethod=(^ignoreeol | ^ignorewhitespaces 
            | ^ignoreeolandwhitespaces | ^recognizeall)]
        [--^dateformat=<str_date_format>]
        [--^encoding=<name>]
        [--^stats]
        [--^repository=<repspec>]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^comparisonmethod  Sets the specified comparison method. See Remarks for more info.
    --^dateformat        Sets the output format to print dates.
    --^encoding          Specifies the output encoding, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^stats             Shows statistics information.
    --^repository        Specifies a repository spec used to calculate
                        the annotations. By default, this command uses the
                        repository where the loaded revision repository in the
                        workspace is stored. (Use 'cm ^help ^objectspec' to learn 
                        more about repspecs.)
    spec                 The spec of the file to annotate.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)
                        Use double quotes (" ") to specify paths containing spaces.

== CMD_HELP_ANNOTATE ==
Remarks:

    Binary files can't be annotated.

    Comparison methods (--^comparisonmethod option):
        - ^ignoreeol                 Ignores the end of line differences.
        - ^ignorewhitespaces         Ignores the whitespace differences.
        - ^ignoreeolandwhitespaces   Ignores the end of line and whitespace differences.
        - ^recognizeall              Detects the end of line and whitespace differences.

    Output format parameters (--^format option):
        This command accepts a format string to show the output.
        The output parameters of this command are the following:
        - {^owner}        User who changed the line the last time.
        - {^rev}          Source revision specification of the line.
        - {^content}      Line content.
        - {^date}         Date when the line was checked in.
        - {^comment}      Comment of the source revision of the line.
        - {^changeset}    Changeset of the source revision of the line.
        - {^line}         Line number of the file.
        - {^id}           Item id.
        - {^parentid}     Parent id of the item.
        - {^rep}          Repository of the item.
        - {^branch}       Branch of the source revision of the line.
        - {^ismergerev}   Whether the revision of the line was created in a merge.

    Date format parameters (--^dateformat):
        To specify the output format in which dates will be printed.
        See the supported formats specified at:
        https://docs.microsoft.com/en-us/dotnet/standard/base-types/custom-date-and-time-format-strings

    Repository specification (--^repository):
        To retrieve data from a remote repository. Useful for distributed
        scenarios.

Examples:

    cm ^blame c:\workspace\src --^comparisonmethod=^ignoreeolandwhitespaces --^encoding=utf-8
    cm ^annotate c:\workspace\file.txt --^comparisonmethod=^ignoreeol

    cm ^annotate c:\workspace\file.txt --^format="{^owner} {^date, 10} {^content}"
    (Writes the owner field, then a blank, then the date field (aligned to
    right), then a blank, and the content.)

    cm ^blame c:\workspace\file.txt --^format="{^owner, -7} {^comment} {^date}" \
        --^dateformat=yyyyMMdd
    (Writes the owner field in 7 spaces (aligned to the left) followed by
    a blank, then the comment, followed by another blank, and ending with the
    formatted date (for example, 20170329).)

    cm ^annotate c:\workspace\file.txt --^repository=centralRep@myserver:8084

    cm ^blame ^serverpath:/src/client/checkin/Checkin.cs#^cs:73666
    (Annotates the file starting in changeset 73666 using a server path.)

== CMD_DESCRIPTION_APPLYLOCAL ==
Checks for local changes (locally moved, locally deleted, and locally changed) and applies them, so that Unity VCS starts tracking those changes.

== CMD_USAGE_APPLYLOCAL ==
Usage:

    cm ^applylocal | ^al [--^dependencies] [<item_path>[ ...]]
                    [--^machinereadable [--^startlineseparator=<sep>]
                      [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    --^dependencies        Adds local change dependencies into the items to
                          apply.
    item_path             Items to be applied. Use a whitespace to separate
                          paths. Use double quotes (" ") to specify paths
                          containing spaces.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag, specifies how
                          the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag, specifies how
                          the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag, specifies how
                          the fields should be separated.

== CMD_HELP_APPLYLOCAL ==
Remarks:

    - If --^dependencies and <item_path> are not specified, the operation involves
      all the local changes in the workspace.

    - It is always applied recursively from the given path.

Examples:

    cm ^applylocal foo.c bar.c

    cm ^applylocal .
    (Applies all local changes in the current directory.)

    cm ^applylocal
    (Applies all local changes in the workspace.)

    cm ^applylocal --^machinereadable
    (Applies all local changes in the workspace, and prints the result in a
    simplified, easier-to-parse format.)

    cm ^applylocal --^machinereadable --^startlineseparator=">" \
      --^endlineseparator="<" --^fieldseparator=","
    (Applies all local changes in the workspace, and prints the result in a
    simplified, easier-to-parse format, starting and ending the lines and
    separating the fields with the specified strings.)

== CMD_DESCRIPTION_ARCHIVE ==
Archives data in external storage.

== CMD_USAGE_ARCHIVE ==
Usage:

    cm ^archive | ^arch <revspec>[ ...] [-^c=<str_comment>]
                        [--^file=<base_file>]
    (Extracts data from the repository and stores it on external storage.)

    cm ^archive | ^arch <revspec>[ ...] --^restore
    (Restores previously archived revisions back into the repository.)

Options:

    -^c                  Sets a comment in the archive storage files to create.
    --^file              Name prefix and (optional) path for the new archive
                        data files.
    --^restore           Restores previously archived data from generated archive
                        files. The external storage location and the 
                        externaldata.conf file must be available at the moment
                        of the revision restoration. See Remarks for more
                        information.
    revspec              One or more revision specs. (Use 'cm ^help ^objectspec'
                        to learn more about revspecs.)

== CMD_HELP_ARCHIVE ==
Remarks:

    This command extracts data from the repository database and store it on 
    external storage, saving database space.
    The command can also restore (--^restore) previously archived revisions back
    into the repository database.

    Use 'cm ^help ^objectspec' to learn how to specify a revspec.

    The user running this command must be the Unity VCS server administrator
    (repository server owner) to be allowed to complete the operation.

    Every data segment from the specified revisions will be stored in a
    different file, with a name starting with the value defined by the --^file
    argument. This argument can contain either a full path value including a
    prefix for future archive files or just this prefix value.

    Once archived, the data from the specified revisions will be accessible in
    two ways:

    1. From the client: The client will detect if the data was archived and it
       will prompt the user to enter the location of the files.
       Users can configure the external data location by creating a file named
       externaldata.conf (at the standard configuration files locations, using
       the same rules that apply for the client.conf file) containing the paths
       where archived data have been located.
    2. From the server: This way users won't have to know whether the data was
       archived or not, since requests will be transparently resolved by the
       server. To do so, the administrator will create a file called
       externaldata.conf in the server directory and will fill it with the
       paths where the archived volumes are.

    To unarchive (restore) a revision (or set of revisions), the archived
    files must be accessible from the client. Hence, it is not possible to
    unarchive data being resolved by the server (method 2) because the client
    will not be able to identify it as archived. 
    If method 2 is used, to unarchive successfully, the administrator will have
    to edit the externaldata.conf server file first to remove access to the
    archived files which have to be unarchived.

    Archive example:
    1) Archive one revision:
       cm ^archive Assets/RoofTextures/Textures/Wooden_Roof_05.png --^file=/Users/<USER>/archive/battle
    2) See the archived revision in the specified output path:
       ^ls -^al /Users/<USER>/archive/battle*
       -rw-r--r--  1 <USER>  <GROUP>  2220039 Nov  9 10:52 /Users/<USER>/archive/battle-100280-167
    
    Unarchive (restore) example:
    1) Add the output archive folder to the externaldata.conf file:
       ^vi /Users/<USER>/.plastic4/externaldata.conf
       /Users/<USER>/archive
    2) Unarchive the revision:
       cm ^archive Assets/RoofTextures/Textures/Wooden_Roof_05.png --^restore

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Reading input from stdin:

    The '^archive' command can read paths from stdin. To do this, pass a single
    dash "-".
    Example: cm ^archive -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which files to archive.
    Example:
      dir /S /B *.c | cm ^archive -
      (In Windows, archives all .c files in the workspace.)

Examples:

    cm ^archive bigfile.zip#^br:/^main
    (Archives the last revision of 'bigfile.zip' in branch 'main'.)

    cm ^archive bigfile.zip#^br:/^main --^restore
    (Restores the archived revision.)

    cm ^archive ^rev:myfile.pdf#^cs:2 -^c="big pdf file" --^file=c:\arch_files\arch
    (Archives the revision with changeset 2 of myfile.pdf in 'c:\archived_files'
    folder. The archived file name will start with 'arch' (for example, arch_11_56).)

    cm ^find "^revs ^where ^size > 26214400" --^format="{^item}#{^branch}" \
      --^nototal | cm ^archive -^c="volume00" --^file="volume00" -
    (Archives all the files bigger than 25Mb on files starting with name
    'volume00'.)


== CMD_DESCRIPTION_ATTRIBUTE ==
Allows the user to manage attributes.

== CMD_USAGE_ATTRIBUTE ==
Usage:

    cm ^attribute | ^att <command> [options]

Commands:

    - ^create | ^mk
    - ^delete | ^rm
    - ^set
    - ^unset
    - ^rename
    - ^edit

    To get more information about each command run:
    cm ^attribute <command> --^usage
    cm ^attribute <command> --^help

== CMD_HELP_ATTRIBUTE ==
Examples:

    cm ^attribute ^create status
    cm ^attribute ^set ^att:status ^br:/main/SCM105 open
    cm ^attribute ^unset ^att:status ^br:/main/SCM105
    cm ^attribute ^delete ^att:status
    cm ^attribute ^rename ^att:status "buildStatus"
    cm ^attribute ^edit ^att:status "Status of the task in the CI pipeline"

== CMD_DESCRIPTION_CHANGELIST ==
Groups pending changes in changelists.

== CMD_USAGE_CHANGELIST ==
Usage:

    cm ^changelist | ^clist [--^symlink]
    (Displays all changelists.)

    cm ^changelist | ^clist ^create <clist_name>
        [<clist_desc>] [--^persistent | --^notpersistent] [--^symlink]
    (Creates a changelist.)

    cm ^changelist | ^clist ^delete <clist_name> [--^symlink]
    (Deletes the selected changelist. If this changelist contains pending
    changes, then these will be moved to the ^default changelist.)

    cm ^changelist | ^clist ^edit <clist_name> [<action_name> <action_value>]
                          [--^persistent | --^notpersistent] [--^symlink]
    (Edits the selected changelist.)

    cm ^changelist | ^clist <clist_name> (^add | ^rm) <path_name>[ ...]
                          [--^symlink]
    (Edits the selected changelist by adding ('^add') or removing ('^rm') the
    change(s) that match with the given path_name(s). Use a whitespace to 
    separate path_names. Use double quotes (" ") to specify paths containing
    spaces. The status of the paths must be '^Added' or '^Checked-out'.)

Options:

    clist_name          The name of the changelist. A path to a file containing 
                       the name can be used instead. More info at --^namefile.
    clist_desc          The description of the changelist. A path to a file 
                       containing the description can be used instead. More 
                       info at --^descriptionfile.
    action_name         Choose between '^rename' or '^description' to edit the 
                       changelist.
    action_value        Applies the new name or new description when editing
                       the changelist.
    --^persistent       The changelist will remain in the workspace even if its
                       contents are checked-in or reverted.
    --^notpersistent    (Default) The changelist will not remain in the
                       workspace even if its contents are checked-in or
                       reverted.
    --^symlink          Applies the operation to the symlink and not to the
                       target.
    --^namefile         A valid path to a file containing the name of the 
                       changelist. Bear in mind the file must exist and its 
                       content cannot be neither empty nor multiline.
    --^newnamefile      A valid path to a file containing the new name of the 
                       changelist when renaming. Bear in mind the file must exist 
                       and its content cannot be neither empty nor multiline.
    --^descriptionfile  A valid path to a file containing the description for the 
                       changelist. Bear in mind the file must exist.

== CMD_HELP_CHANGELIST ==
Remarks:

    The '^changelist' command handles both the workspace pending changelists and
    the changes contained in a changelist.

Examples:

    cm ^changelist
    (Shows the current workspace changelists.)

    cm ^changelist ^create config_changes "dotConf files" --^persistent
    (Creates a new changelist named 'config_changes' and description 'dotConf
    files' which will remain persistent in the current workspace once the
    pending changelist is either checked-in or reverted.)

    cm ^changelist ^create --^namefile="name.txt" --^descriptionfile="desc.txt"
    (Creates a new changelist which name and description are both taken from files.)

    cm ^changelist ^edit config_changes ^rename config_files --^notpersistent
    (Edits the changelist named 'config_changes' and renames it to
    'config_files'. Also, it turns the changelist into "not persistent".)
        
    cm ^changelist ^edit config_changes --^notpersistent
    (Edits the changelist named 'config_changes' by turning it into "not persistent".)

    cm ^changelist ^delete config_files
    (Removes the pending changelist 'config_files' from the current workspace.)

    cm ^changelist ^delete --namefile="name.txt"
    (Removes the changelist identified by the content of 'name.txt' file from the current 
    workspace.)

    cm ^changelist config_files ^add foo.conf
    (Adds the file 'foo.conf' to the 'config_files' changelist.)

    cm ^changelist config_files ^rm foo.conf readme.txt
    (Removes the files 'foo.conf' and 'readme.txt' from the 'config_files'
    changelist and moves the files to the system default changelist.)

    cm ^changelist ^edit --^namefile="name.txt" ^description --^descriptionfile="desc.txt"
    (Edits the changelist identified by the content of 'name.txt' file, changing its 
    description to the text content of the 'desc.txt' file.)

    cm ^changelist ^edit --^namefile="name.txt" ^rename --^newnamefile="newname.txt"
    (Edits the changelist identified by the content of 'name.txt' file, renaming it to 
    the text content of the 'newname.txt' file.)

== CMD_DESCRIPTION_CHANGESET ==
Executes advanced operations on changesets.

== CMD_USAGE_CHANGESET ==
Usage:

    cm ^changeset <command> [options]

Commands:

    - ^move | ^mv
    - ^delete | ^rm
    - ^editcomment | ^edit

    To get more information about each command run:
    cm ^changeset <command> --^usage
    cm ^changeset <command> --^help

== CMD_HELP_CHANGESET ==
Examples:

    cm ^changeset ^move ^cs:15@myrepo ^br:/main/scm005@myrepo
    cm ^changeset ^delete ^cs:2b55f8aa-0b29-410f-b99c-60e573a309ca@devData
    cm ^changeset ^editcomment ^cs:15@myrepo "I forgot to add the checkin details"

== CMD_DESCRIPTION_CHANGESET_EDITCOMMENT ==
Modifies the comment of a changeset.

== CMD_USAGE_CHANGESET_EDITCOMMENT ==
Usage:

    cm ^changeset ^editcomment | ^edit <csetspec> <new_comment>

Options:

    csetspec            The target changeset whose comment will be edited.
                        (Use 'cm ^help ^objectspec' to learn more about changeset
                        specs.)
    new_comment         The new comment that will be added to the targeted
                        changeset.

== CMD_HELP_CHANGESET_EDITCOMMENT ==
Remarks:

    - The targeted changeset spec must be valid.

Examples:

    cm ^changeset ^editcomment ^cs:15@myrepo "I forgot to add the checkin details"
    cm ^changeset ^edit ^cs:cb11ecdb-1aa9-4f11-8698-dcab14e5885a \
         "This comment text will replace the previous one."
    cm ^changeset ^edit "89095131-895d-4173-9440-ff9ef9b2538d@project@cloud" \
         "Changing my comment"

== CMD_DESCRIPTION_CHANGESET_MOVE ==
Moves a changeset and all its descendants to a different branch.

== CMD_USAGE_CHANGESET_MOVE ==
Usage:

    cm ^changeset ^move | ^mv <csetspec> <branchspec>

Options:

    csetspec            First changeset to be moved to a different branch. All
                        descendant changesets in the same branch will be
                        targeted by the command as well.
                        (Use 'cm ^help ^objectspec' to learn more about changeset
                        specs.)
    branchspec          The target branch where the targeted changesets are
                        stored. It needs to be empty or non-existing; if the
                        destination branch doesn't exist, it will be created by
                        the command.
                        (Use 'cm ^help ^objectspec' to learn more about branch
                        specs.)

== CMD_HELP_CHANGESET_MOVE ==
Remarks:

    - The targeted changeset spec must be valid.
    - The destination branch must be either empty or non-existing.
    - If the destination branch doesn't exist, it will created.
    - Merge links will be kept unchanged since branches don't affect them.

Examples:

    cm ^changeset ^move ^cs:15@myrepo ^br:/main/scm005@myrepo
    cm ^changeset ^move ^cs:cb11ecdb-1aa9-4f11-8698-dcab14e5885a ^br:/hotfix/TL-352

== CMD_DESCRIPTION_CHANGESET_DELETE ==
Deletes a changeset from the repository.

== CMD_USAGE_CHANGESET_DELETE ==
Usage:

    cm ^changeset ^delete | ^rm <csetspec>

Options:

    csetspec           The target changeset to be removed. It must fulfill
                       some specific conditions. See Remarks for more info.
                       (Use 'cm ^help ^objectspec' to learn more about changeset
                        specs.)

== CMD_HELP_CHANGESET_DELETE ==
Remarks:

    - The target changeset must be the last in its branch.
    - The target changeset cannot be the parent of any other changeset.
    - The target changeset cannot be neither the source of a merge link nor
      part of an interval merge as source.
    - No label must be applied to the target changeset.
    - The target changeset must not be the root changeset ('^cs:0').

Examples:

    cm ^changeset ^rm ^cs:4525@myrepo@myserver
    cm ^changeset ^delete ^cs:cb11ecdb-1aa9-4f11-8698-dcab14e5885a

== CMD_DESCRIPTION_CHANGEUSERPASSWORD ==
Changes the user's password (UP).

== CMD_USAGE_CHANGEUSERPASSWORD ==
Usage:

    cm ^changepassword | ^passwd

== CMD_HELP_CHANGEUSERPASSWORD ==
Remarks:

    This command is only available when the security configuration is UP
    (user/password).
    See the Administrator Guide for more information:
    https://www.plasticscm.com/download/help/adminguide

    The old and new passwords are required.

Examples:

    cm ^passwd

== CMD_DESCRIPTION_CHECKCONNECTION ==
Checks the connection to the server.

== CMD_USAGE_CHECKCONNECTION ==
Usage:

      cm ^checkconnection | ^cc [<repserverspec>]

Options:

    repserverspec   Repositories server.
                    (Use 'cm ^help ^objectspec' to learn more about repserver
                    specs.)

Examples:

    cm ^checkconnection myorg@cloud

== CMD_HELP_CHECKCONNECTION ==
Remarks:

    - This command returns a message indicating whether there is a valid
      connection to the specified server. If repserverspec is not specified,
      the check will be performed with the server configured in client.conf.
    - The command checks checks the version compatibility with the server.
    - The command also checks whether the configured user is valid or not.

== CMD_DESCRIPTION_CHECKDB ==
> **This command is deprecated.** Checks the repositories integrity.

== CMD_USAGE_CHECKDB ==
Usage:

    cm ^checkdatabase | ^chkdb [<repserverspec> | <repspec>]

Options:

    repserverspec   Repositories server.
                    (Use 'cm ^help ^objectspec' to learn more about repserver
                    specs.)
    repspec         Repository.
                    (Use 'cm ^help ^objectspec' to learn more about rep specs.)

== CMD_HELP_CHECKDB ==
Remarks:

    - If neither repserverspec nor repspec are specified, the check will be
      performed in the server specified in the client.conf file.

Examples:

    cm ^checkdatabase ^repserver:localhost:8084
    cm ^chkdb ^rep:default@localhost:8084

== CMD_DESCRIPTION_CHECKIN ==
Stores changes in the repository.

== CMD_USAGE_CHECKIN ==
Usage:

    cm ^checkin | ^ci [<item_path>[ ...]]
        [-^c=<str_comment> | -^commentsfile=<comments_file>]
        [--^all|-^a] [--^applychanged] [--^private] [--^update] [--^symlink]
        [--^noshowchangeset]
        [--^machinereadable [--^startlineseparator=<sep>]
          [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    item_path             Items to be checked-in. Use double quotes (" ") to 
                          specify paths containing spaces. Use a whitespace to 
                          separate item paths.
                          Use . to apply checkin to current directory.
    -^c                    Applies the specified comment to the changeset created 
                          in the checkin operation.
    -^commentsfile         Applies the comment in the specified file to the
                          changeset created in the checkin operation.
    --^all | -^a            The items changed, moved and deleted locally on the
                          given paths are also included.
    --^applychanged        Applies the checkin operation to the changed items
                          detected in the workspace along with the checked out
                          items.
    --^private             Private items detected in the workspace are also
                          included.
    --^update              Processes the update-merge automatically if it
                          eventually happens.
    --^symlink             Applies the checkin operation to the symlink and not
                          to the target.
    --^noshowchangeset     Doesn't print the result changeset.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag, specifies how
                          the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag, specifies how
                          the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag, specifies how
                          the fields should be separated.

== CMD_HELP_CHECKIN ==
Remarks:

    - If <item_path> is not specified, the checkin involves all the
      pending changes in the workspace.
    - The checkin operation is always applied recursively from the given path.
    - To checkin an item:
        - The item must be under source code control.
        - If the item is private (not under source code control), the --^private
          flag is necessary in order to checkin it.
        - The item must be checked out.
        - If the item is changed but not checked out, the --^applychanged flag 
          is not necessary unless <item_path> is a directory or it contains
          wildcards ('*').

    Revision content should be different from previous revision in order to be
    checked in.

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Reading input from stdin:

    The '^checkin' command can read paths from stdin. To do this, pass a single
    dash "-".
    Example: cm ^checkin -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which files to checkin.
    Example:
      dir /S /B *.c | cm ^checkin --^all -
      (In Windows, checkins all .c files in the workspace.)

Examples:

    cm ^checkin file1.txt file2.txt
    (Checkins the 'file1.txt' and 'file2.txt' checked-out files.)

    cm ^checkin . -^commentsfile=mycomment.txt
    (Checkins the current directory and sets the comment in the 
    'mycomment.txt' file.)

    cm ^checkin link --^symlink
    (Checkins the symlink file and not the target.)

    cm ^ci file1.txt -^c="my comment"
    (Checkins 'file1.txt' and includes a comment.)

    cm ^status --^short --^compact --^changelist=pending_to_review | cm ^checkin -
    (Lists the paths in the changelist named 'pending_to_review' and redirects
    this list to the input of the checkin command.)

    cm ^ci . --^machinereadable
    (Checkins the current directory, and prints the result in a simplified,
    easier-to-parse format.)

    cm ^ci . --^machinereadable --^startlineseparator=">" --^endlineseparator="<" --^fieldseparator=","
    (Checkins the current directory, and prints the result in a simplified,
    easier-to-parse format, starting and ending the lines, and
    separating the fields with the specified strings.)

== CMD_DESCRIPTION_CHECKOUT ==
Marks files as ready to modify.

== CMD_USAGE_CHECKOUT ==
Usage:

    cm ^checkout | ^co [<item_path>[ ...]] [-^R | -^r | --^recursive]
                     [--^format=<str_format>]
                     [--^errorformat=<str_format>] [--^resultformat=<str_format>]
                     [--^silent] [--^symlink] [--^ignorefailed]
                     [--^machinereadable [--^startlineseparator=<sep>]
                       [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    item_path             Items to be checked-out. Use double quotes (" ") to 
                          specify paths containing spaces. Use a whitespace to 
                          separate item paths.
                          Use . to apply checkout to current directory.
    -^R                    Checks out files recursively.
    --^format              Retrieves the output progress message in a specific
                          format. Check the examples for more information.
    --^resultformat        Retrieves the output result message in a specific
                          format. Check the examples for more information.
    --^silent              Does not show any output at all.
    --^symlink             Applies the checkout operation to the symlink and not
                          to the target.
    --^ignorefailed        If an item cannot be locked (the exclusive checkout
                          cannot be performed), the checkout operation will
                          continue without it.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag, specifies how
                          the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag, specifies how
                          the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag, specifies how
                          the fields should be separated.

== CMD_HELP_CHECKOUT ==
Remarks:

    To checkout an item:
    - The item must be under source code control.
    - The item must be checked-in.

    If locks are configured on the server (lock.conf exists), then each time
    a checkout on a path happens, Unity VCS checks if it meets any of the rules
    and if so, the path will be in exclusive checkout (locked) so that none can
    simultaneously checkout.
    You can get all the locks in the server by using 'cm ^lock ^list'.
    See the Administrator Guide for more information:
    https://www.plasticscm.com/download/help/adminguide

    The format string replaces the placeholder '{0}' with the path of the item
    being checked out. Check the examples to see how to use it.

Reading input from stdin:

    The '^checkout' command can read paths from stdin. To do this, pass a single
    dash "-".
    Example: cm ^checkout -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which files to checkout.
    Example:
      dir /S /B *.c | cm ^checkout -
      (In Windows, checkouts all .c files in the workspace.)

Examples:

    cm ^checkout file1.txt file2.txt
    (Checkouts 'file1.txt' and 'file2.txt' files.)

    cm ^co *.txt
    (Checkouts all txt files.)

    cm ^checkout .
    (Checkouts current directory.)

    cm ^checkout -^R c:\workspace\src
    (Recursively checkouts 'src' folder.)

    cm ^co file.txt --^format="Checking out item {0}" 
        --^resultformat="Item {0} checked out"
    (Checkouts 'file.txt' using the specified formatting strings
    to show the progress and the result of the operation.)

    cm ^checkout link --^symlink
    (Checkouts the symlink file and not the target.)

    cm ^checkout . -^R --^ignorefailed
    (Recursively checkouts the current folder, ignoring those files that can
    not be checked out.)

    cm ^co . --^machinereadable --^startlineseparator=">"
    (Checkouts the current directory, and prints the result in a simplified,
    easier-to-parse format, starting the lines with the specified strings.)

== CMD_DESCRIPTION_CHECKSELECTORSYNTAX ==
Checks the syntax of a selector.

== CMD_USAGE_CHECKSELECTORSYNTAX ==
Usage:

    cm ^checkselectorsyntax | ^css --^file=<selector_file>
    (Checks the selector file syntax.)

    ^cat <selector_file> | cm ^checkselectorsyntax | ^css -
    (Unix. Checks selector file from standard input.)

    ^type <selector_file> | cm ^checkselectorsyntax | ^css -
    (Windows. Checks selector file from standard input.)

Options:

    --^file     The file to read a selector from.

== CMD_HELP_CHECKSELECTORSYNTAX ==
Remarks:

    This command reads a selector on either a file or standard input, and
    checks it for valid syntax. If the syntax check fails, the reason is
    printed on standard output.

Examples:

    cm ^checkselectorsyntax --^file=myselector.txt
    (Checks the syntax of 'myselector.txt' file.)

    ^cat myselector.txt | cm ^checkselectorsyntax
    (Checks the syntax of 'myselector.txt' from standard input.)

== CMD_DESCRIPTION_CHANGEREVISIONTYPE ==
Changes an item revision type (binary or text).

== CMD_USAGE_CHANGEREVISIONTYPE ==
Usage:

    cm ^changerevisiontype | ^chgrevtype | ^crt <item_path>[ ...] --^type=(^bin | ^txt)

Options:

    item_path           Items to change revision type. Use double quotes (" ")
                        to specify paths containing spaces. Use a whitespace to
                        separate item paths.
    --^type             Target revisions type. Choose '^bin' or '^txt'.

== CMD_HELP_CHANGEREVISIONTYPE ==
Remarks:

    This command can only be applied to files, not directories.
    The specified type must be a system supported one: '^bin' or '^txt' (binary
    or text).

Examples:

    cm ^changerevisiontype c:\workspace\file.txt --^type=^txt
    (Changes 'file.txt' revision type to text.)

    cm ^chgrevtype comp.zip "image file.jpg" --^type=^bin
    (Changes 'comp.zip' and "image file.jpg" revision type to binary.)

    cm ^crt *.* --^type=^txt
    (Changes revision type of all files to text.)

== CMD_DESCRIPTION_TRIGGER_EDIT ==
Edits a trigger.

== CMD_USAGE_TRIGGER_EDIT ==
Usage:

    cm ^trigger | ^tr ^edit <subtype_type> <position_number>
                         [--^position=<new_position>]
                         [--^name=<new_name>] [--^script=<script_path>]
                         [--^filter=<str_filter>] [--^server=<repserverspec>]

Options:

    --^position          New position of the specified trigger.
                        This position must not be in use by another
                        trigger of the same type.
    --^name              New name of the specified trigger.
    --^script            New execution path of the specified trigger script.
                        If the script starts with "^webtrigger ", it will be
                        considered as a web trigger. See Remarks for more 
                        further details.
    --^filter            Checks only items that match the specified filter.
    --^server            Modifies the trigger on the specified server.
                        If no server is specified, executes the command on the
                        one configured on the client.
                        (Use 'cm ^help ^objectspec' to learn more about server
                        specs.)
    subtype_type        Trigger execution and trigger operation.
                        (Use 'cm ^showtriggertypes' to see a list of trigger
                        types.)
    position_number     Position occupied by the trigger to be modified.

== CMD_HELP_TRIGGER_EDIT ==
Remarks:

    Web triggers: A web trigger is created by typing "^webtrigger <target-uri>"
    as the trigger command. In this case, the trigger will execute a POST query
    against the specified URI, where the request body contains a JSON
    dictionary with the trigger environment variables, and a fixed INPUT key
    pointing to an array of strings.

Examples:

    cm ^trigger ^edit ^after-setselector 6 --^name="Backup2 manager" --^script="/new/path/al/script"
    cm ^tr ^edit ^before-mklabel 7 --^position=4 --^server=myserver:8084
    cm ^trigger ^edit ^after-add 2 --^script="^webtrigger http://myserver.org/api"

== CMD_DESCRIPTION_CODEREVIEW ==
Creates, edits, or deletes code reviews.

== CMD_USAGE_CODEREVIEW ==
Usage:

    cm ^codereview <spec> <title> [--^status=<status_name>]
                [--^assignee=<user_name>] [--^format=<str_format>]
                [--^repository=<rep_spec>]
    (Creates a code review.)

    cm ^codereview -^e <id> [--^status=<status_name>] [--^assignee=<user_name>]
                [--^repository=<rep_spec>]
    (Edits a code review.)

    cm ^codereview -^d <id> [ ...] [--^repository=<rep_spec>]
    (Deletes one or more code reviews.)


Options:

    -^e              Edits the parameters of an existing code review.
    -^d              Deletes one or more existing code reviews. Use a 
                    whitespace to separate the code reviews IDs.
    --^status        Sets the new status of a code review. See Remarks for
                    additional information.
    --^assignee      Sets the new assignee of a code review.
    --^format        Retrieves the output message in a specific format. See
                    Remarks for additional information.
    --^repository    Sets the repository to be used as default. (Use 
                    'cm ^help ^objectspec' to learn more about repository specs.)
    spec           It can be either a changeset spec, a shelve spec or a branch spec. 
                    It will be the target of the new code review. (Use 'cm ^help 
                    ^objectspec' to learn more about changeset or branch specs.)
    title          A text string to be used as title of the new code review.
    id              The code review identification number. A GUID can be used as
                   well.

== CMD_HELP_CODEREVIEW ==
Remarks:

    This command allows users to manage code reviews: create, edit, and delete
    code reviews for changesets or branches.

    To create a new code review, a changeset/branch spec and a title are
    required. The initial status and assignee can be set, too. An ID (or GUID
    if requested) will be returned as a result.

    To edit or delete an existing code review, the target code review ID
    (or GUID) is required. No messages are displayed if there are no errors.

    The 'status parameter' must only be one of the following: "^Under ^review"
    (default), "^Reviewed", or "^Rework ^required".

    The 'repository' parameter is available to set the default working
    repository. This is useful when the user wants to manage reviews on
    a server different than the one associated to the current workspace, or
    when there is no current workspace at all.

    Output format parameters (--^format option):

    This command accepts a format string to show the output.

    The output parameters of this command are the following:

    {0}  id
    {1}  guid

    Please note that the '--^format' parameter only takes effect when creating
    a new code review.

Examples:

    cm ^codereview ^cs:1856@myrepo@myserver:8084 "My code review" --^assignee=dummy
    cm ^codereview ^br:/main/task001@myrepo@myserver:8084 "My code review" \
        --^status=^"Rework required" --^assignee=newbie --^format="{^id} -> {^guid}"

    cm ^codereview 1367 -^e --^assignee=new_assignee
    cm ^codereview -^e 27658884-5dcc-49b7-b0ef-a5760ae740a3 --^status=Reviewed

    cm ^codereview -^d 1367 --^repository=myremoterepo@myremoteserver:18084
    cm ^codereview 27658884-5dcc-49b7-b0ef-a5760ae740a3 -^d

== CMD_DESCRIPTION_CRYPT ==
Encrypts a password.

== CMD_USAGE_CRYPT ==
Usage:

    cm ^crypt <mypassword>

    mypassword          Password to be encrypted.

== CMD_HELP_CRYPT ==
Remarks:

    This command encrypts a given password passed as argument.
    It is designed to encrypt passwords in configuration files and increase 
    safety.

Examples:

    cm ^crypt dbconfpassword -> ENCRYPTED: encrypteddbconfpassword
    (Encrypts the password in the database configuration file: 'db.conf'.)

== CMD_DESCRIPTION_DEACTIVATEUSER ==
Deactivates a licensed user.

== CMD_USAGE_DEACTIVATEUSER ==
Usage:

    cm ^deactivateuser | ^du <usr_name>[ ...] [--^server=<name:port>]
                           [--^nosolveuser]

Options:

    --^server            Deactivates the user on the specified server.
                        If no server is specified, executes the command on the
                        one configured on the client.
    --^nosolveuser       With this option, the command will not check whether
                        the user name exists on the authentication system. The
                        <usr_name> must be a user SID.
    usr_name             The user name(s) to deactivate. Use a whitespace to
                        separate user names. If SID, then '--^nosolveuser' is required.

== CMD_HELP_DEACTIVATEUSER ==
Remarks:

    This command sets a user to inactive, disabling the usage of Unity VCS
    for that user.

    See the 'cm ^activateuser' command for more information about activating
    Unity VCS users.

    This command checks whether the user exists on the underlying authentication
    system (e.g. ActiveDirectory, LDAP, User/Password...).
    To force the deactivation of a user that no longer exists on the
    authentication system, you can use the '--^nosolveuser' option.

Examples:

    cm ^deactivateuser john
    cm ^du peter "mary collins"
    cm ^deactivateuser john --^server=myserver:8084
    cm ^deactivateuser S-1-5-21-3631250224-3045023395-1892523819-1107 --^nosolveuser

== CMD_DESCRIPTION_DIFF ==
Shows differences between files, changesets, and labels.

== CMD_USAGE_DIFF ==
Usage:

    cm ^diff <csetspec> | <lbspec> | <shspec> [<csetspec> | <lbspec> | <shspec>]
            [<path>]
            [--^added] [--^changed] [--^moved] [--^deleted]
            [--^repositorypaths] [--^download=<download_path>]
            [--^encoding=<name>] 
            [--^comparisonmethod=(^ignoreeol | ^ignorewhitespaces | 
                ^ignoreeolandwhitespaces | ^recognizeall)] 
            [--^clean]
            [--^integration]
            [--^format=<str_format>] [--^dateformat=<str_format>]
    (Shows differences between a 'source' changeset or shelveset, and a
    'destination' changeset or shelveset. The changesets can be specified
    using either a changeset or label specification.
    Where two specifications are given, the first will be the 'source' of
    the diff; the second, the 'destination'.
    If only one specification is given, the 'source' will be the parent
    changeset of the specified 'destination'.
    If an optional path is specified, the Diff window will launch to show
    differences between the two revisions of that file.)

    cm ^diff <revspec1> <revspec2>
    (Shows differences between a pair of revisions. The differences are
    shown in the Diff window. The first revision specified will appear on
    the left.)

    cm ^diff <brspec> [--^added] [--^changed] [--^moved] [--^deleted]
            [--^repositorypaths] [--^download=<download_path>]
            [--^encoding=<name>] 
            [--^comparisonmethod=(^ignoreeol | ^ignorewhitespaces | 
                ^ignoreeolandwhitespaces | ^recognizeall)] 
            [--^clean]
            [--^integration]
            [--^format=<str_format>] [--^dateformat=<str_format>]
            [--^fullpaths | --^fp]
    (Shows the branch differences. Use 'cm ^help ^objectspec' to learn more 
    about specs.)

Options:

    --^added             Prints only differences consisting of items added to
                        the repository.
    --^changed           Prints only differences consisting of items that
                        changed.
    --^moved             Prints only differences consisting of moved or renamed
                        items.
    --^deleted           Prints only differences consisting of items that were
                        deleted.
    --^repositorypaths   Prints repository paths instead of workspace paths.
                        (This option overrides the '--^fullpaths' option.)
    --^download          Stores the differences content in the specified output
                        path.
    --^encoding          Specifies the output encoding, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^comparisonmethod  Sets the specified comparison method. See Remarks for more info.
    --^clean             Does not take into account the differences generated
                        because of a merge, but only the differences created by
                        simple checkins.
    --^integration       Shows the branch changes that are pending to be merged into 
                        its parent branch. It takes into account any rebase or 
                        prior merge already done from/to its parent branch.
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^dateformat        Format used to output dates.
    --^fullpaths, --^fp   Forces printing full workspace paths for files and 
                        directories when possible.

== CMD_HELP_DIFF ==
Remarks:
    File status:
       If '--^added', '--^changed', '--^moved' or '--^deleted' are
       not specified, then the command prints all differences.
       '^A'  means added items.
       '^C'  means changed items.
       '^D'  means deleted items.
       '^M'  means moved items. The left item is the original, the right is the destination.

    Comparison methods (--^comparisonmethod option):
        ^ignoreeol                Ignores the end of line differences.
        ^ignorewhitespaces        Ignores the whitespace differences.
        ^ignoreeolandwhitespaces  Ignores the end of line and whitespace differences.
        ^recognizeall             Detects the end of line and whitespace differences.

        This command accepts a format string to show the output.
        The parameters of this command are the following:
        {^path}              Item path.
        {^date}              Change date/time.
        {^owner}             Change author.
        {^revid}             Revision id of the revision considered as the
                            destination in the diff.
        {^parentrevid}       Revision id of the parent of the revision considered
                            as the destination of the diff.
        {^baserevid}         Revision id of the revision considered as the source
                            in the diff.
        {^srccmpath}         Server path before moving the item (move operation).
        {^dstcmpath}         Server path after moving the item (move operation).
        {^type}              Item type: ^D (directory), ^B (binary file), ^F (text
                            file), ^S (symlink), ^X (Xlink)
        {^repository}        Repository of the item.
        {^status}            Item status: ^A (added), ^D (deleted), ^M (moved), ^C
                            (changed)
        {^fsprotection}      Shows item permissions (Linux/Mac chmod).
        {^srcfsprotection}   Shows parent revision item permissions.
        {^newline}           Inserts a new line.

Notes on '^revid':
    For added items, the '^baserevid' and '^parentrevid' will be -1, as no
    previous revision exists in this case.
    For deleted items, the '^revid' is the id of the source revision, and the
    '^baserevid' will be -1, as there is no destination revision.
    For Xlinks, both '^baserevid' and '^parentrevid' are always -1.

Examples:

  Comparing branches:

    cm ^diff ^br:/main/task001
    cm ^diff ^br:/main/task001 \doc\readme.txt
    cm ^diff ^br:/main/task001 --^integration

  Comparing changeset trees:

    cm ^diff 19
    cm ^diff 19 25
    cm ^diff ^cs:19 ^cs:25 --^format="{^path} {^parentrevid}"
    cm ^diff ^cs:19 ^cs:23 --^format="{^date} {^path}" --^dateformat="yy/dd/MM HH:mm:ss"
    cm ^diff ^cs:19 ^cs:23 --^changed
    cm ^diff ^cs:19 ^cs:23 --^repositorypaths
    cm ^diff ^cs:19 ^cs:23 --^download="D:\temp"
    cm ^diff ^cs:19 ^cs:23 --^clean
    cm ^diff ^cs:19 ^cs:23 \doc\readme.txt

  Comparing label trees:

    cm ^diff ^lb:FirstReleaseLabel ^lb:SecondReleaseLabel
    cm ^diff ^lb:tag_193.2 ^cs:34214
    cm ^diff ^cs:31492 ^lb:tag_193.2

  Comparing shelve trees:

    cm ^diff ^sh:2
    cm ^diff ^sh:2 ^sh:4

  Comparing revspecs:

    cm ^diff ^rev:readme.txt#^cs:19 ^rev:readme.txt#^cs:20
    cm ^diff ^serverpath:/doc/readme.txt#^cs:19@myrepo \
        ^serverpath:/doc/readme.txt#^br:/main@myrepo@localhost:8084
    cm ^diff ^rev:foo.c#^cs:1 ^rev:foo.c#^cs:2 --^comparisonmethod=^ignoreeol

== CMD_DESCRIPTION_DIFFMETRICS ==
Shows diff metrics between two revs.

== CMD_USAGE_DIFFMETRICS ==
Usage:

    cm ^diffmetrics | ^dm <revspec1> <revspec2> [--^format=<str_format>]
                        [--^encoding=<name>]
                        [--^comparisonmethod=(^ignoreeol | ^ignorewhitespaces |
                            ^ignoreeolandwhitespaces | ^recognizeall)]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^encoding          Specifies the output encoding, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^comparisonmethod  Sets the specified comparison method.
                        See Remarks for more info.
    revspec              Revisions used to compare.
                        (Use 'cm ^help ^objectspec' to learn more about rev specs.)

== CMD_HELP_DIFFMETRICS ==
Remarks:

    The metrics are: number of changed, added, and deleted lines.

    Output format parameters (--^format option):
    This command accepts a format string to show the output.
    The output parameters of this command are the following:
    {0}  Number of changed lines.
    {1}  Number of added lines.
    {2}  Number of deleted lines.

    Comparison methods (--^comparisonmethod option):
    ^ignoreeol                Ignores the end of line differences.
    ^ignorewhitespaces        Ignores the whitespace differences.
    ^ignoreeolandwhitespaces  Ignores the end of line and whitespace differences.
    ^recognizeall             Detects the end of line and whitespace differences.

Examples:

    cm ^diffmetrics file.txt#^cs:2 file.txt#^br:/main/scm0211 \
        --^format="There are {0} changed, {1} added and {2} deleted lines."
    (Retrieves diffmetrics results formatted.)

    cm ^dm file.txt#^cs:2 file.txt#^cs:3 --^encoding=utf-8 --^comparisonmethod=^ignorewhitespaces

== CMD_DESCRIPTION_FASTEXPORT ==
Exports a repository in fast-export format.

== CMD_USAGE_FASTEXPORT ==
Usage:

    cm ^fast-export | ^fe <repspec> <fast-export-file>
                        [--^import-marks=<marks_file>]
                        [--^export-marks=<marks_file>]
                        [--^branchseparator=<chr_separator>]
                        [--^nodata] [--^from=<changesetid>] [--^to=<changesetid>]

Options:

    repspec             The repository which the data will be exported from. 
                        (Use 'cm ^help ^objectspec' to learn more about rep specs.)
    fast-export-file    The file with the repository data in Git fast-export
                        format.
    --^import-marks      The marks file used for incremental imports. This file
                        has been previously exported by '--^export-marks'. The
                        changesets described in this file will not be imported
                        because they were already in a previous import.
    --^export-marks      The file where the imported changesets will be saved.
                        This file is used in a later fast-import to signal the
                        changesets that have been already imported.
    --^branchseparator   Unity VCS uses "/" as default separator in the branch
                        hierarchy. This option allows using char as a hierarchy
                        separator, so main-task-sub would be mapped in Unity VCS
                        as /main/task/sub.
    --^nodata            Exports the repository without including the data. This
                        is useful to check if the export will run correctly.
    --^from              Exports from a particular changeset.
    --^to                Exports to a particular changeset.

== CMD_HELP_FASTEXPORT ==
Remarks:

    - To import a Unity VCS repository to Git, use a command such as:

      ^cat repo.fe.00 | ^git ^fast-import --^export-marks=marks.git --^import-marks=marks.git

    - Incremental export is supported using a marks file that contains the
      changesets previously imported ('--^import-marks' and '--^export-marks'
      files).
      This means that only the new changesets that were not exported in the
      previous fast-export will be exported.

Examples:

    cm ^fast-export repo@localhost:8087 repo.fe.00 --^import-marks=marks.cm \
        --^export-marks=marks.cm
    (Exports the repository 'repo' in the local server into the 'repo.fe.00'
    file in Git fast-export format and creates the marks files to perform
    incremental exports later.)

    cm ^fast-export repo@localhost:8087 repo.fe.00 --^from=20
    (Exports the repository 'repo' in the local server into the 'repo.fe.00'
    file in Git fast-export format from changeset '20'.)

== CMD_DESCRIPTION_FASTIMPORT ==
Imports Git fast-export data into a repository.

== CMD_USAGE_FASTIMPORT ==
Usage:

    cm ^fast-import | ^fi <repspec> <fast-export-file>
                        [--^import-marks=<marks_file>]
                        [--^export-marks=<marks_file>]
                        [--^stats] [--^branchseparator=<chr_separator>]
                        [--^nodata] [--^ignoremissingchangesets] [--^mastertomain]

Options:

    repspec                     The repository into which the data will be
                                imported. It is created if it did not previously
                                exist. (Use 'cm ^help ^objectspec' to learn more
                                about rep specs.)
    fast-export-file            The file with the repository data in Git
                                fast-export format.
    --^import-marks              The marks file used for incremental imports.
                                This file has been previously exported by 
                                '--^export-marks'. The changesets described in
                                this file wont be imported because they
                                were already in a previous import.
    --^export-marks              The file where the imported changesets will
                                be saved. This file is used in a later
                                fast-import to signal the changesets that have
                                been already imported.
    --^stats                     Prints some statistics about the import process.
    --^branchseparator           Unity VCS uses "/" as default separator in
                                the branch hierarchy. This option allows using
                                char as a hierarchy separator, so main-task-sub
                                would be mapped in Unity VCS as /main/task/sub.
    --^nodata                    Imports Git fast-export without including the
                                data. This is useful to check if the import will
                                run correctly.
    --^ignoremissingchangesets   Any changesets that cannot be imported are 
                                discarded and the fast-import operation
                                continues without them.
    --^mastertomain              Imports using "^main" instead of "^master".

== CMD_HELP_FASTIMPORT ==
Remarks:

    - To export a Git repository, use a command such as:
      ^git ^fast-export --^all -^M --^signed-tags=^strip --^tag-of-filtered-object=^drop> ..\git-fast-export.dat
      The -^M option is important to detect moved items.

    - The specified repository is created in case it did not exist.

    - Incremental import is supported using a marks file that contains the
      changesets previously imported ('--^import-marks' and '--^export-marks'
      files).
      This means that only the new changesets that were not imported in the
      previous fast-import will be imported.

Examples:

    cm ^fast-import mynewrepo@atenea:8084 repo.fast-export
    (Imports the contents exported in the 'repo.fast-export' file into
    'mynewrepo' repository on server 'atenea:8084'.)

    cm ^fast-import repo@atenea:8084 repo.fast-export --^export-marks=rep.marks
    (Imports the contents exported in the 'repo.fast-export' file into
    'repo' repository on server 'atenea:8084' and creates a marks file
    to perform incremental imports later.)

    cm ^fast-import repo@server:8084 repo.fast-export --^import-marks=repo.marks \
        --^export-marks=repo.marks
    (Imports the contents of the 'repo.fast-export' file. Only the new
    changesets that were not in the marks file are imported. The same marks
    file is used to save the list of changesets again for the next
    incremental import.)

== CMD_DESCRIPTION_FILEINFO ==
Retrieves detailed information about the items in the workspace.

== CMD_USAGE_FILEINFO ==
Usage:

    cm ^fileinfo <item_path>[ ...] [--^fields=<field_value>[,...]]
                [[--^xml | -^x [=<output_file>]] | [--^format=<str_format>]]
                [--^symlink] [--^encoding=<name>]

Options:

    --^fields        A string of comma-separated values. This selects which
                    fields will be printed for each item. See Remarks for
                    more information.
    --^xml | -^x      Prints the output in XML format to the standard output.
                    It is possible to specify an output file. This option
                    cannot be combined with '--^format'.
    --^format        Retrieves the output message in a specific format. See
                    Remarks for more info. This option cannot be combined 
                    with '--^xml'.
                    This '--^format' option prevails over '--^fields' if both
                    are specified.
    --^symlink       Applies the fileinfo operation to the symlink and not
                    to the target.
    --^encoding      Specifies the output encoding, i.e.: utf-8.
                    See the MSDN documentation at
                    http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                    to get the table of supported encodings and its format, 
                    (at the end of the page, in the "Name" column).
    item_path       Items to display. Use a whitespace to separate the items.
                    Use double quotes (" ") to specify paths containing spaces.


== CMD_HELP_FILEINFO ==
Remarks:

    This command prints a detailed list of attributes for each selected item.
    Each attribute is printed on a new line by default.

    The attribute list can be modified to display only the attributes the user
    needs. This can be achieved using the '--^fields=<field_list>' which accepts
    a string of comma-separated attribute names. This way, only those arguments
    whose name has been indicated are shown.

    Revision head changeset:

    This option is disabled by default. Please note that retrieving this
    attribute is significantly slower than the rest of them, so we advise users
    to group together as many items as possible. This will improve execution
    times by avoiding many separate 'cm ^fileinfo' executions.
    Also, this feature is not currently available for controlled directories.

    You can find below the complete list of available attribute names.
    Names marked with an asterisk ('*') will not be shown by default:
        ^ClientPath              The local path on disk for the item.
        ^RelativePath            The workspace-relative path.
        ^ServerPath              The repository path for the item.
        ^Size                    Item size.
        ^Hash                    Item hash sum.
        ^Owner                   The user the item belongs to.
        ^RevisionHeadChangeset   (*) The changeset of the revision loaded in the
                                   head changeset of the branch.
                                   (Please see note above.)
        ^RevisionChangeset       The changeset of the revision currently loaded
                                   in the workspace.
        ^RepSpec                 The repository specification for the item.
                                   (Use 'cm ^help ^objectspec' to learn more about
                                   rep specs.)
        ^Status                  The workspace item status: added, checked out,
                                   deleted, etc.
        ^Type                    Revision type (text, binary, directory, symlink,
                                   or unknown).
        ^Changelist              The changelist the item belongs to (if any).
        ^IsLocked                  (*) Whether the item is locked by exclusive
                                   checkout or not.
        ^LockedBy                (*) The user who exclusively checked out the item.
        ^LockedWhere             (*) The location where the item was exclusively
                                   checked out.
        ^IsUnderXlink            Whether the item is located under an Xlink
                                   or not.
        ^UnderXlinkTarget        The target of the Xlink the item is under
                                   (if any).
        ^UnderXlinkPath          The item server path in the Xlinked repository
                                   (if any).
        ^UnderXlinkWritable      Whether the Xlink the item belongs to is
                                   writable or not.
        ^UnderXlinkRelative      Whether the Xlink the items belongs to is
                                   relative or not.
        ^IsXlink                 Whether the item itself is a Xlink or not.
        ^XlinkTarget             The target repository the item points to, if it
                                   is a Xlink.
        ^XlinkName               The Xlink name of the item, if it is
                                   actually one.
        ^XlinkWritable           Whether the Xlink item is a writable Xlink
                                   or not.
        ^XlinkRelative           Whether the Xlink item is a relative Xlink
                                   or not.


    Output format parameters (--^format option):

    This command accepts a format string to show the output.

    The output parameters of this command are the following:

    - {^ClientPath}
    - {^RelativePath}
    - {^ServerPath}
    - {^Size}
    - {^Hash}
    - {^Owner}
    - {^RevisionHeadChangeset}
    - {^RevisionChangeset}
    - {^Status}
    - {^Type}
    - {^Changelist}
    - {^IsLocked}
    - {^LockedBy}
    - {^LockedWhere}
    - {^IsUnderXlink}
    - {^UnderXlinkTarget}
    - {^UnderXlinkPath}
    - {^UnderXlinkWritable}
    - {^UnderXlinkRelative}
    - {^IsXlink}
    - {^XlinkTarget}
    - {^XlinkName}
    - {^XlinkWritable}
    - {^XlinkRelative}
    - {^RepSpec}

    Please note that '--^format' and '--^xml' options are mutually exclusive, so
    they can't be used at the same time.

Examples:

    cm ^fileinfo file1.txt file2.txt dir/
    cm ^fileinfo "New Project.csproj" --^xml
    cm ^fileinfo assets.art --^fields=^ServerPath,^Size,^IsLocked,^LockedBy
    cm ^fileinfo proj_specs.docx --^fields=^ServerPath,^RevisionChangeset --^xml
    cm ^fileinfo samples.ogg --^format="{^ServerPath}[{^Owner}] -> {^Size}"

== CMD_DESCRIPTION_FIND ==
Runs SQL-like queries to find Unity VCS objects.

== CMD_USAGE_FIND ==
Usage:

    cm ^find <object_type> 
            [^where <str_conditions>]
            [^on ^repository '<repspec>' | ^on ^repositories '<repspec1>','<repspec2>'[,...]]
            [^order ^by <sort_field> ['^asc' | '^desc']]
            [[^limit <maxresults>] [^offset <offset>]]
            [--^format=<str_format>] [--^dateformat=<date_format>]
            [--^nototal] [--^file=<dump_file>] [--^xml] 
            [--^encoding=<name>]

Options:

    --^format            Retrieves the output message in a specific format.
                        Read the 'cm ^find' guide to see all the object
                        attributes that can be used as output format strings:
                        https://www.plasticscm.com/download/help/cmfind
    --^dateformat        Format used to output dates.
    --^nototal           Does not output record count at the end.
    --^file              File to dump results.
    --^xml               Prints the output in XML format to the standard output.
    --^encoding          Specifies the output encoding, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    str_conditions      Searches conditions on an object attributes.
    repspec             Searches repositories alias or specification.
                        In the case of '^on ^repositories', use a comma to
                        separate the repspec fields.
                        (Use 'cm ^help ^objectspec' to learn more about repository
                        specifications.)
    sort_field          The name of the field to use as sorting field. Mind there is only
                        a subset of field possibilities. Use 'cm ^help ^showfindobjects' 
                        to find what objects are allowed to be ordered and by what fields.
    maxresults          The maximun number of results returned by the query.
    offset              The number of rows to skip before starting to return results
                        from the query.
    object_type         Object type to find. 
                        Some of these objects are implementing the '^order ^by' clause.
                        Use 'cm ^help ^showfindobjects' to learn how to specify these
                        objects, the ones allowing '^order ^by' and by what fields.
                        You can also read the 'cm ^find' guide:
                        https://www.plasticscm.com/download/help/cmfind

== CMD_HELP_FIND ==
Remarks:

    If no repository is specified, the search is made on the repository
    configured in the workspace.

    When you run queries using comparison operators (>, <, >=, <=) from the 
    command line, remember that the shell considers these operators as IO 
    redirections. So you will need to enclose the queries in double quotation
    marks.

    The 'cm ^find' command accepts a format string to show the output.
    Each output parameter is identified by a string and the user can refer it
    by typing the parameter number between '{' and '}' brackets.
    Output parameters usually correspond to the attributes of the object.

    These are some valid output format strings:
    - --^format={^id}{^date}{^name}
    - --^format="{^item}#{^branch} ^with ^date {^date}"

    XML and encoding considerations:

    When the '--^xml' option is specified, the command shows the command result
    as an XML text in the standard output. The operating system default encoding
    is used to show the text, so it is possible that not-ANSI characters are
    incorrectly visualized in the console. If you redirect the command output to
    a file, it will be correctly visualized. When both '--^xml' and '--^file'
    options are specified, the default encoding will be utf-8.

Examples:

    cm ^find ^revision
    cm ^find ^revision "^where ^changeset=23 ^and ^owner='maria'"
    cm ^find ^branch "^on ^repository 'rep1'"
    cm ^find ^label "^on ^repositories 'rep1', '^rep:default@localhost:8084'"
    cm ^find ^branch "^where ^parent='^br:/main' ^on ^repository 'rep1'"
    cm ^find ^revision "^where ^item='^item:.'" --^format="{^item}#{^branch}"
    cm ^find ^revision "^where ^item='^item:.'" --^xml --^file=c:\queryresults\revs.xml
    cm ^find ^label "^where ^owner='^me' ^limit 10 ^offset 20"
    cm ^find ^branches "^where ^owner='^me' ^order ^by ^branchname ^desc ^limit 10"

== CMD_DESCRIPTION_FINDCHANGED ==
> **This command is deprecated.** Use cm ^status instead. 

Gets a list of changed files.

== CMD_USAGE_FINDCHANGED ==
Usage:

    cm ^findchanged | ^fc [-^R | -^r | --^recursive] [--^checkcontent]
                        [--^onlychanged] [<path>]

Options:

    -^R | -^r | --^recursive  Recursively finds in directories.
    --^checkcontent           Compares files by content.
    --^onlychanged            Finds only changed files; checkouts will not be
                             obtained.
    path                      (Default: current directory.)
                             Initial path to find changed files.

== CMD_HELP_FINDCHANGED ==
Remarks:

    If no '--^checkcontent' option is given, Unity VCS finds changes based on
    the file timestamp.
    When '--^checkcontent' option is specified, the file or folder contents are
    compared, instead of using the timestamp.

    This command is useful to detect changed files while disconnected from
    the Unity VCS server. The output can be piped to the ^checkout command,
    to check the changes later (see examples).

Examples:

    cm ^findchanged .
    (Finds changed files in the current directory.)

    cm ^findchanged -^R . | cm ^checkout -
    (Checkouts changed elements.)

== CMD_DESCRIPTION_FINDCHECKEDOUT ==
> **This command is deprecated.** Use cm ^status instead.

Gets a list of checked out items. 

== CMD_USAGE_FINDCHECKEDOUT ==
Usage:

    cm ^findcheckouts | ^fco [--^format=<str_format>] [--^basepath]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^basepath          The path to start searching checkouts from. If not
                        specified, the current path is used.

== CMD_HELP_FINDCHECKEDOUT ==
Remarks:

    This command is useful to checkin or undocheckout all checked out items in
    one single step, redirecting the standard output to other command.
    See examples.

    Output format parameters (--^format option):
        This command accepts a format string to show the output.
        The output parameters of this command are the following:
        {0}             Date.
        {1}             Owner.
        {2}             Workspace info.
        {3}             Client machine name.
        {4}             Item path.
        {5}             Branch and repository info.

Examples:

    cm ^findcheckouts --^format="File {4} changed on branch {5}"
    (Finds checked out items and formats the output with file path and branch
    and repository info.)

    cm ^findcheckouts --^format={4} | cm ^checkin -
    (Checkins all checked out items.)

    cm ^findcheckouts --^format={4} | cm ^undocheckout -
    (Undocheckouts of all checked out items.)

== CMD_DESCRIPTION_FINDPRIVATE ==
> **This command is deprecated.** Use cm ^status instead.

Gets a list of private items. 

== CMD_USAGE_FINDPRIVATE ==
Usage:
    cm ^findprivate | ^fp [-^R | -^r | --^recursive] [--^exclusions] [<path>]

Options:

    -^R | -^r | --^recursive  Recursively finds in directories.
    --^exclusions             This option allows cutting the search inside the ignored
                             paths, defined by the file ignore.conf.
    path                      (Default: current directory.)
                             Initial path to find private files.

== CMD_HELP_FINDPRIVATE ==
Remarks:

    If any path is specified, Unity VCS will begin searching from the
    current directory.

    This command is useful to add private items on a folder, piping the output
    to the '^add' command. See examples.

Examples:

    cm ^findprivate .

    cm ^findprivate -^R | cm ^add -
    (Recursively searches private items and add them.)

== CMD_DESCRIPTION_GETCONFIG ==
Obtains configuration info.

== CMD_USAGE_GETCONFIG ==
Usage:

    cm ^getconfig [^setfileasreadonly] [^location] [^extensionworkingmode]
                 [^extensionprefix] [^defaultrepserver]

Options:

    ^setfileasreadonly       Returns whether the protected files are left as
                            read-only or not.
    ^location                Returns the client config path.
    ^extensionworkingmode    Returns the extension working mode.
    ^extensionprefix         Returns the configured extension prefix.
    ^defaultrepserver        Returns the location of the default repository
                            server.

== CMD_HELP_GETCONFIG ==
Examples:

    cm ^getconfig ^setfileasreadonly

== CMD_DESCRIPTION_GETFILE ==
Downloads the content of a given revision.

== CMD_USAGE_GETFILE ==
Usage:

    cm ^getfile | ^cat <revspec>[[;<output_file>] | [--^file=<output_file>]]
                     [--^debug] [--^symlink] [--^raw]

Options:

    --^file            File to save the output. By default, it is printed on the
                      standard output. Only usable when a single revision is
                      required. If more than one ^revspec is provided, this 
                      option should be avoided and use '^revspec;^outputfile'
                      instead, adding as much pairs as needed separated by
                      whitespaces.
    --^debug           When a directory specification is used, the command
                      shows all the items in the directory, its revision id
                      and file system protection.
    --^symlink         Applies the operation to the symlink and not to the
                      target.
    --^raw             Displays the raw data of the file.
    revspec            Object specification. (Use 'cm ^help ^objectspec' to learn
                      more about specs.)

== CMD_HELP_GETFILE ==
Examples:

    cm ^cat myfile.txt#^br:/main
    (Obtains the last revision in branch '^br:/main' of 'myfile.txt'.)

    cm ^getfile myfile.txt#^cs:3 --^file=tmp.txt
    (Obtains the changeset 3 of 'myfile.txt' and write it to file 'tmp.txt'.)

    cm ^cat ^serverpath:/src/foo.c#^br:/main/task003@myrepo
    (Obtains the contents of '/src/foo.c' at the last changeset of branch
    '/main/task003' in repository 'myrepo'.)

    cm ^cat ^revid:1230@^rep:myrep@^repserver:myserver:8084
    (Obtains the revision with id 1230.)

    cm ^getfile ^rev:info\ --^debug
    (Obtains all revisions in the 'info' directory.)

    cm ^getfile "^revid:25@^rep:^default@^repserver:^localhost:8084;file_revid25.txt" 
        "^revid:16@^rep:^default@^repserver:^localhost:8084;file_revid_16.txt"
    (Obtains two different revisions and stores each one on different files.
    To list ^revid it is possible to use the command '^cm ^find ^revision'.
    Mind that, to specify 'spec;dest_file' collections, it might be needed to
    quote the pairs individually, then separate them using whitespaces).

== CMD_DESCRIPTION_GETREVISION ==
Loads a revision in the workspace.

== CMD_USAGE_GETREVISION ==
This command modifies the revision loaded in the workspace, so it can affect 
future merges.
It is an advanced command inherited from old versions, so use it with care.

Usage:
    cm ^getrevision <revspec>

Options:

    revspec           Object specification. (Use 'cm ^help ^objectspec' to learn
                      more about rev specs.)

== CMD_HELP_GETREVISION ==
Examples:

    cm ^getrevision file.txt#^cs:3
    (Gets changeset 3 revision of 'file.txt'.)

== CMD_DESCRIPTION_GETSTATUS ==
Gets the status of an item.

== CMD_USAGE_GETSTATUS ==
Usage:

    cm ^getstatus | ^gs <item_path>[ ...] [--^format=<str_format>] [--^stats]
                      [-^R | -^r | --^recursive]

Options:

    --^format                 Retrieves the output message in a specific format. See
                             Remarks for more info.
    --^stats                  Prints some statistics about the get status process.
    -^R | -^r | --^recursive  Shows recursively the status in directories.
    item_path                 Item or items to get status from. Use double quotes
                             (" ") to specify paths containing spaces. Use a
                             whitespace to separate paths.

== CMD_HELP_GETSTATUS ==
Remarks:

    Output format parameters (--^format option):
    This command accepts a format string to show the output.

    The output parameters of this command are the following:

    {0}  Item path.
    {1}  Item status:

    Where status can take values among the following:

    0  private
    1  checked in
    2  checked out

Reading input from stdin:

    The '^getstatus' command can read paths from stdin. To do this, pass a
    single dash "-".
    Example: 
    cm ^getstatus -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which paths to get the status for.
    Example:
    dir /S /B *.c | cm ^getstatus --^format="Path {0} Status {1}" -
    (In Windows, gets the status of all .c files in the workspace.)

Examples:

    cm ^getstatus file1.txt file2.txt
    (Gets the status of the files.)

    cm ^gs info\ -^R --^format="The item {0} has the status {1}"
    (Gets the status of the directory and all of its items and shows a
    formatted output.)

== CMD_DESCRIPTION_GETTASKBRANCHES ==
Gets branches linked with a task.

== CMD_USAGE_GETTASKBRANCHES ==
Usage:

    cm ^gettaskbranches | ^gtb <task_name> [--^format=<str_format>] 
                             [--^dateformat=<date_format>]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^dateformat        Format used to output dates.
    task_name            The task identifier.

== CMD_HELP_GETTASKBRANCHES ==
Remarks:

    Output format parameters (--^format option):
    
    This command accepts a format string to show the output.
    
    The output parameters of this command are the following:
    {^tab}        Inserts a tab space.
    {^newline}    Inserts a new line.
    {^name}       Branch name.
    {^owner}      Owner of the branch.
    {^date}       Date when the branch was created.
    {^parent}     Parent branch.
    {^comment}    Comment of the branch.
    {^repname}    Repository where the branch exists.
    {^repserver}  Server name.

Examples:

    cm ^gettaskbranches 4311
    cm ^gtb 4311 --^format="^br:{^name}"
    cm ^gtb 4311 --^format="^br:{^name} {^date}" --^dateformat="yyyy/MM/dd HH:mm:ss"

== CMD_DESCRIPTION_GETWORKSPACEINFO ==
Shows info about the workspace selector.

== CMD_USAGE_GETWORKSPACEINFO ==
Usage:

    cm ^workspaceinfo | ^wi [<wk_path>]

Options:

    wk_path             Path of a workspace on the machine.

== CMD_HELP_GETWORKSPACEINFO ==
Remarks:
    The '^wi' command shows the working configuration of a workspace (repository,
    branch, and/or label).

Examples:
    cm ^wi c:\mywk

== CMD_DESCRIPTION_GETWORKSPACEFROMPATH ==
Gets workspace info from a path.

== CMD_USAGE_GETWORKSPACEFROMPATH ==
Usage:

    cm ^getworkspacefrompath | ^gwp <item_path> [--^format=<str_format>] [--^extended]

Options:
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^extended          Displays more information regarding the workspace: ^type
                        and ^dynamic on top of the others. See Remarks for more info.
    item_path           File or folder on disk.

== CMD_HELP_GETWORKSPACEFROMPATH ==
Remarks:

    This command shows information about the workspace that is located in path.

    Output format parameters (--^format option):
    This command accepts a format string to show the output.

    The output parameters of this command are the following:
    {0} | {^wkname}          Workspace name.
    {1} | {^wkpath}          Workspace path.
    {2} | {^machine}         Client machine name.
    {3} | {^owner}           Workspace owner.
    {4} | {^guid}            Workspace GUID.
    {5} | {^type}            Workspace type: partial (gluon) or regular.
    {6} | {^dynamic}         A workspace can be dynamic (plasticfs) or static.
    {^tab}                   Inserts a tab space.
    {^newline}               Inserts a new line.

Examples:

    cm ^getworkspacefrompath c:\myworkspace\code\file1.cpp --^format="Workspace name: {^wkname}"
    cm ^gwp . --^format="Name: {^wkname} | Type: {^type}, {^dynamic}"

== CMD_DESCRIPTION_HELP ==
Gets help for a Unity VCS command.

== CMD_USAGE_HELP ==
Usage:

    cm ^help <command>

== CMD_HELP_HELP ==
Mind this command is partially overlapped with the --^help option at each command.

== CMD_DESCRIPTION_IOSTATS ==
Shows statistics about the hardware.

== CMD_USAGE_IOSTATS ==
Usage:

    cm ^iostats [<repserverspec>] [<list_of_tests>[ ...]]
               [--^nettotalmb=<value_mb>] [--^networkiterations=<value_iter>]
               [--^diskdatasize=<value_size>] [--^disktestpath=<value_path>]
               [--^systemdisplaytime=<value_time>]
               [--^systemdisplaytimeinterval=<value_interval>]

Options:

    --^nettotalmb                  Indicates the amount of user data (in 
                                  MegaBytes) transmitted on a network test, 
                                  such as "^serverDownloadTest" or 
                                  "^serverUploadTest".
                                  It must be a value between "4" and "512".
                                  (Default: 16)
    --^networkiterations           Indicates the number of iterations of
                                  "^serverDownloadTest" and/or "^serverUploadTest" 
                                  that will be run.
                                  It must be a value between "1" and "100".
                                  (Default: 1)
    --^diskdatasize                Indicates the amount of data (in MegaBytes) 
                                  that will be written and then read on the 
                                  "^diskTest".
                                  It must be a value between "100" and "4096".
                                  (Default: 512)
    --^disktestpath                Path where the "^diskTest" writes the test 
                                  files. If this parameter is not provided, 
                                  the command will try to use the system temp 
                                  path.
    --^systemdisplaytime           Time interval (in seconds) showing the usage
                                  of system resources. This option is available
                                  for the following tests: "^systemNetworkUsage"
                                  and "^systemDiskUsage".
                                  It must be a value between "1" and "3600".
                                  (Default: 5 seconds).
    --^systemdisplaytimeinterval  Time interval (in seconds) between the
                                  system performance samples. This option is 
                                  available for the following tests: 
                                  "^systemNetworkUsage" and "^systemDiskUsage".
                                  It must be a value between "1" and "60".
                                  (Default: 1 second).
    repserverspec                 An available Unity VCS server to perform 
                                  the network tests, such as "serverUploadTest"
                                  and/ or "serverDownloadTest".
                                  If no server is provided, the command tries 
                                  to communicate with the server configured by
                                  default.
                                  (Use 'cm ^help ^objectspec' to learn more about
                                  server specs.)
    list_of_tests                 Available tests. Use a whitespace to separate
                                  test fields. See Remarks for more info.

== CMD_HELP_IOSTATS ==
Remarks:

    This command requires an available server be used during the network
    speed tests ("^serverUploadTest" and/or "^serverDownloadTest").

    The '--^diskTestPath' must point to a path that belongs to the physical
    disk drive about to be tested. If no path is specified, the command tries
    to use the system default temp path.
    The disk drive of the specified path must have enough free space to execute
    the test.

    During the command execution, the system can experience a degraded
    performance caused by the tests performed.

    Available tests:
    --^serveruploadtest    (Default) Measures the data upload speed from
                           Unity VCS client to the server.
    --^serverdownloadtest  (Default) Measures the data download speed from
                           Unity VCS server to the client.
    --^disktest            (Default) Measures the disk read speed and disk
                           write speed.
    --^systemnetworkusage  Shows the current usage of system network
                           resources.
                           (It shows Network Interface performance counters
                           provided by Microsoft Windows).
                           Available in Microsoft Windows only.
    --^systemdiskusage     Shows the current usage of system physical
                           disks.
                           (It shows Network Interface performance counters
                           provided by Microsoft Windows).
                           Available in Microsoft Windows only.

Examples:

    cm ^iostats MYSERVER:8087 --^serveruploadtest --^serverdownloadtest --^nettotalmb=32

== CMD_DESCRIPTION_ISSUETRACKER ==
Gets, updates, or finds the issue status in the specified issue tracker.

== CMD_USAGE_ISSUETRACKER ==
Usage:

    cm ^issuetracker <name> ^status ^get <task_id> <parameter>[ ...]
    cm ^issuetracker <name> ^status ^update <task_id> <status> <parameter>[ ...]
    cm ^issuetracker <name> ^status ^find <status> <parameter>[ ...]
    cm ^issuetracker <name> ^connection ^check <parameter>[ ...]
    
    name                Name of the issue tracker to connect with.
                        Only Jira is supported at the moment.
    task_id             Number of the issue to query or update.
    ^status              A valid status for an issue in the issue tracker.

Jira parameters (all are mandatory):

    --^user=<user>          The user to authenticate.
    --^password=<password>  The password to authenticate.
    --^host=<url>           The target url of the issue tracker.
    --^projectkey=<key>     The project key of Jira project.
    
== CMD_HELP_ISSUETRACKER ==
Examples:

    cm ^issuetracker jira ^status ^get 11 --^user=<EMAIL> --^password=pwd \
        --^host=https://user.atlassian.net --^projectkey=PRJ
    (Gets the status of the issue 11 for the 'PRJ' project.)

    cm ^issuetracker jira ^status ^update 11 "Done" --^user=<EMAIL> \
        --^password=pwd --^host=https://user.atlassian.net --^projectkey=PRJ
    (Updates the status to 'Done' of the issue 11 for the 'PRJ' project.)
    
    cm ^issuetracker jira ^status ^find "Done" --^user=<EMAIL> --^password=pwd \
        --^host=https://user.atlassian.net --^projectkey=PRJ
    (Gets the task ids whose status is set to 'Done' for the 'PRJ' project.)
    
    cm ^issuetracker jira ^connection ^check --^user=<EMAIL> --^password=pwd \
        --^host=https://user.atlassian.net --^projectkey=PRJ
    (Checks whether the configuration parameters are valid or not.)

== CMD_DESCRIPTION_LICENSEINFO ==
Displays license information and license usage.

== CMD_USAGE_LICENSEINFO ==
Usage:

    cm ^licenseinfo | ^li [--^server=<repserverspec>] [--^inactive] [--^active]
                        [--^sort=(^name|^status)]

Options:

    --^server            Gets the license info from the specified server.
                        If no server is specified, executes the command on the
                        one configured on the client.
                        (Use 'cm ^help ^objectspec' to learn more about repserver
                        specs.)
    --^inactive          Shows only inactive users in the "license usage" section.
    --^active            Shows only active users in the "license usage" section.
    --^sort              Sorts users by one of the specified sort options:
                        '^name' or '^status'.

== CMD_HELP_LICENSEINFO ==
Remarks:

    The information displayed consists of expiration date, activated and
    deactivated users, etc.

Examples:

    cm ^licenseinfo
    cm ^licenseinfo --^server=myserver:8084
    cm ^licenseinfo --^sort=^name

== CMD_DESCRIPTION_LINKTASK ==
Links a changeset to a task.

== CMD_USAGE_LINKTASK ==
Usage:

    cm ^linktask | ^lt <csetspec> <ext_prefix> <task_name>

Options:

    csetspec            The full changeset specification to link to a task.
                        (Use 'cm ^help ^objectspec' to learn more about changeset
                        specs.)
    ext_prefix          The extension prefix of the configured issue tracking
                        system to work with.
    task_name           The task identifier on the issue tracking system.

== CMD_HELP_LINKTASK ==
Examples:

    cm ^lt ^cs:8@^rep:default@^repserver:localhost:8084 jira PRJ-1

== CMD_DESCRIPTION_LOCK_LIST ==
Shows locks on a server.

== CMD_USAGE_LOCK_LIST ==
Usage:

    cm ^lock ^list | ^ls [<revspec> [ ...]] [--^server=<server>]
                      [--^repository] [--^workingbranch=<brname>] [--^anystatus]
                      [--^onlycurrentuser] [--^onlycurrentworkspace]
                      [--^ignorecase] 
                      [--^machinereadable [--^startlineseparator=<sep>]
                        [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]
                        [--^smartlocks]]

Options:

    --^server                Repository server specification.
                            This option will override the default server which
                            is retrieved from the current workspace or the
                            client.conf file.
                            (Use 'cm ^help ^objectspec' to learn more about
                            server specs.)
    --^repository            Repository specification.
                            This option will only list the locks of the specified
                            repository.
                            (Use 'cm ^help ^objectspec' to learn more about
                            repository specs.)
    --^workingbranch         Branch name.
                            This option will only list the locks that apply to
                            the destination branch of the specified branch.
    --^anystatus             Shows locks in any status (Locked or Retained).
                            By default, only locks in Locked status are shown.
    --^onlycurrentuser       Filters the results showing only the locks performed
                            by the current user.
    --^onlycurrentworkspace  Filters the results showing only the locks performed
                            on the current workspace (matching them by name).
    --^ignorecase            Ignores casing on the paths when a serverpath spec
                            is used. With this flag, the command will work for
                            "/src/foo.c" even if the user writes "/sRc/fOO.c".
    --^dateformat            Format used to output dates.
    --^machinereadable       Outputs the result in an easy-to-parse format.
    --^startlineseparator    Used with the '--^machinereadable' flag, specifies how
                            the lines should start.
    --^endlineseparator      Used with the '--^machinereadable' flag, specifies how
                            the lines should end.
    --^fieldseparator        Used with the '--^machinereadable' flag, specifies how
                            the fields should be separated.
    --^smartlocks            Used with the '--^machinereadable' flag, shows all the
                            smart lock fields. Otherwise, it only shows the legacy locks
                            fields. This parameter is needed to avoid breaking
                            compatibility with older integrations / plugins that expect
                            the old format.                            
    revspec                 If one or more are present, this command will display
                            one lock line for each specified revision if its
                            associated item is locked in the server. Otherwise,
                            this command will list all locked items in the default
                            server (or the one set with the '--^server' option).
                            Use a whitespace to separate the rev specs when using
                            more than one.
                            (Use 'cm ^help ^objectspec' to learn more about rev specs.)

== CMD_HELP_LOCK_LIST ==
Remarks:

    The command will display a list of the currently locked items in the
    default server. It also accepts a list of revision specifications. In this
    case, only the locks belonging to the selected items will be displayed.
    A '--^server=<server>' can be used to set the default server to query.

    The command shows a line for every lock in the specified server:
    - Repository of the locked item.
    - Item id of the locked item.
    - GUID of the locked item (this is only printed with the --^machinereadable flag).
    - Date of the lock.
    - Destination branch where the lock will be released.
    - Revision id of the item loaded in the destination branch.
    - Holder branch where the lock was performed.
    - Revision id of the item that currently holds the lock.
    - Status of the lock (Locked or Retained)
    - User name who performed the lock.
    - Workspace name where the lock was performed.
    - Path of the locked item (server path format).
    
    For the --^machinereadable without the --^smartlocks option, the
    printed fields are:
    - GUID of the locked item.
    - User name who performed the lock.
    - Workspace name where the lock was performed.
    - Path of the locked item (server path format).

Examples:

    cm ^lock ^list
    cm ^lock ^list --^server=myserver:8084
    cm ^lock ^list --^repository=repo@myserver:8084 --^anystatus
    cm ^lock ^list --^repository=repo@myserver:8084 --^workingbranch=/main/scm21345
    cm ^lock ^ls ^serverpath:/src/foo.c#^cs:99@default@localhost:8084
    cm ^lock ^list ^revid:3521@default ^itemid:2381@secondary --^onlycurrentuser
    cm ^lock ^ls --^onlycurrentuser --^dateformat="yy/dd/MM HH:mm:ss"
    cm ^lock ^ls --^onlycurrentuser --^onlycurrentworkspace
    cm ^lock ^list --^machinereadable --^startlineseparator=">" \
        --^endlineseparator="<" --^fieldseparator=","
    cm ^lock ^list --^machinereadable --^smartlocks --^startlineseparator=">" \
        --^endlineseparator="<" --^fieldseparator=","        

== CMD_DESCRIPTION_LISTUSERS ==
Lists users and groups.

== CMD_USAGE_LISTUSERS ==
Usage:

    cm ^listusers | ^lu <repserverspec> [--^onlyusers] [--^onlygroups]
                      [--^filter=<str_filter>]
    cm ^listusers | ^lu <repserverspec> --^group=<group_name>

Options:

    --^onlyusers         Lists only users.
    --^onlygroups        Lists only groups.
    --^filter            Lists only users and/or groups that matches the
                        specified filter.
    --^group             Lists only users from a certain group. This option is
                        not compatible with ^onlyusers, ^onlygroups, nor ^filter.
    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_LISTUSERS ==
Examples:

    cm ^lu localhost:8084
    (Lists all users in the server.)

    cm ^listusers localhost:8084 --^onlyusers --^filter=m
    (Lists only the users in the server that contains "m".)

    cm ^listusers codice@cloud --^group=Administrators
    (Lists only the users in the group Administrators in the 'codice@cloud' org.)

== CMD_DESCRIPTION_LOCATION ==
Returns the path of 'cm'.

== CMD_USAGE_LOCATION ==
Usage:

    cm ^location

== CMD_HELP_LOCATION ==
Bear in mind this is related to your environment variables.
If you have several installations of the client, it will return
the first one in the path.

== CMD_DESCRIPTION_LOCK ==
This command allows the user to manage locks.

== CMD_USAGE_LOCK ==
Usage:

    cm ^lock <command> [options]

Commands:

    - ^list | ^ls
    - ^unlock
    - ^create | ^mk

    To get more information about each command run:
    cm ^lock <command> --^usage
    cm ^lock <command> --^help

== CMD_HELP_LOCK ==
Examples:

    cm ^lock
    (The '^list' subcommand is the default.)
    cm ^lock ^list --^anystatus
    cm ^lock ^unlock ^itemid:56@myrep@localhost:8084
    cm ^lock ^create /main/task@myrep ^itemid:56@myrep

== CMD_DESCRIPTION_LOG ==
Gets info about revisions in changesets.

== CMD_USAGE_LOG ==
Usage:

    cm ^log [<csetspec> | <repspec>] [--^from=<csetspec_from>] [--^allbranches]
           [--^ancestors] [--^csformat=<str_format>] [--^itemformat=<str_format>]
           [--^dateformat=<str_date_format>]
           [--^xml[=<output_file>]] [--^encoding=<name>]
           [--^repositorypaths | --^fullpaths | --^fp]

Options:

    --^from              Lists all the changes made in every changeset from the
                        changeset specification [csetspec_from] to the
                        changeset specification [csetspec].
                        The [csetspec_from] changeset is not included in the
                        output.
                        Ignored when a repository spec is provided.
    --^allbranches       Shows information about the changesets created in a
                        specified interval, for all the branches where those
                        changesets were created.
    --^ancestors         Shows information about the reachable changesets by
                        following the parent and merge links for the given
                        changeset ([csetspec]). If the from changeset
                        ([csetspec_from]) is provided too, it will be used as
                        lower limit for all the paths. Remarks: The changeset
                        changes will not be shown when this option is used.
    --^csformat          Retrieves the changeset info in a specific format. See
                        Remarks for more info. This option cannot be combined
                        with '--^xml'.
    --^itemformat        Retrieves the item info in a specific format. See
                        Remarks for more info. This option cannot be combined
                        with '--^xml'.
    --^dateformat        Sets the output format to print dates.
    --^xml               Prints the output in XML format to the standard output.
                        It is possible to specify an output file. This option
                        cannot be combined with '--^csformat' and '--^itemformat'.
    --^encoding          Used with the '--^xml' option, specifies the encoding to
                        use in the XML output, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^fullpaths, --^fp   Force printing full workspace paths for files and
                        directories when possible.
    --^repositorypaths   Prints repository paths (server paths) instead of
                        workspace paths. (This option overrides the
                        '--^fullpaths' option).
    csetspec            Returns all changes made in the specified changeset.
                        (Use 'cm ^help ^objectspec' to learn more about changeset
                        specs.)
    repspec             Returns all changes made in the specified repository.
                        (Use 'cm ^help ^objectspec' to learn more about repository
                        specs.)

== CMD_HELP_LOG ==
Remarks:

    - If neither 'csetspec' nor option is specified, the command shows
      information about every changeset created within the last month in every
      branch.
    - If only the option '--^from' is included, the command shows the
      information about every changeset from that specified changeset to the
      last changeset in the branch where the changeset was created.
    - If the option '--^allbranches' appears without an interval, the command
      retrieves the same information as it would do if only 'csetspec' was
      specified.
    - If the '--^from' is used, the output contains information from the
      'csetspec_from'+1 on.
    - The repository used to show the changeset information is the one loaded
      in the path where the command executes on.

    This command accepts a format string for the items ('--^itemformat') and a
    format string for the changesets ('--^csformat').

    The output parameters of '--^csformat' are the following:
        {^tab}           Inserts a tab space.
        {^newline}       Inserts a new line.
        {^changesetid}   Changeset number.
        {^branch}        Branch where the changeset was created.
        {^date}          Date of the changeset.
        {^owner}         Owner of the changeset.
        {^comment}       Comment of the changeset.
        {^items}         Items involved in the changeset.
        {^repository}    Repository where the changeset exists.
        {^repserver}     Server name.

    The output parameters of '--^itemformat' are the following:
        {^tab}           Inserts a tab space.
        {^newline}       Inserts a new line.
        {^path}          Item path.
        {^branch}        Branch where the changeset was created.
        {^date}          Date of the changeset.
        {^owner}         Owner of the changeset.
        {^shortstatus}   Prints the short format for the status. See below.
        {^fullstatus}    Prints the long format for the status. See below.

        Short format and its corresponding long format:
            '^A'   ^Added
            '^D'   ^Deleted
            '^M'   ^Moved
            '^C'   ^Changed

    These are valid output strings:
    - --^csformat="{^newline}Changeset {^changesetid} created on {^date};{^tab} changed items: {^items}."
    - --^itemformat="{^newline}The item {^path} was changed in the branch {^branch}."

    Date format parameters (--^dateformat):
        To specify the output format in which dates will be printed.
        See the supported formats specified at:
        https://docs.microsoft.com/en-us/dotnet/standard/base-types/custom-date-and-time-format-strings

Examples:

    cm ^log
    (Shows information about every changeset created in the last month in every
    branch.)

    cm ^log ^cs:16
    (Shows information about the changes done in the changeset 16 in the branch
    where the changeset was created.)

    cm ^log ^cs:16 --^csformat="{^newline}Changeset {^changesetid} created on \
        {^date};{^tab} changed items: {^items}."
    (Shows the information in the specified format.)

    cm ^log --^from=^cs:20 ^cs:50
    (Shows the information about every revision contained in every changeset
    from the changeset 21 to the changeset 50.)

    cm ^log --^from=^cs:20 ^cs:50 --^allbranches
    (Shows the information about every revision contained in every changeset
    from the changeset 21 to the changeset 50 in every branch of the
    repository.)

    cm ^log ^rep:myrep@localhost:8084
    (Shows information about the changes done in the specified repository.
    No workspace is required to run the command.)

    cm ^log --^from=^cs:20@^rep:mainRep@localhost:8084
    (Shows the information about every revision contained in every changeset
    from the changeset 21. No workspace is required to run the command, because
    the full changeset spec was specified.)

== CMD_DESCRIPTION_LIST ==
Lists the contents of a tree.

== CMD_USAGE_LIST ==
Usage:

    cm ^ls | ^dir [<paths>[ ...]] [--^format=<str_format>] [--^symlink]
                [--^selector[=<selector_format>]] [--^tree=<obj_spec>]
                [-^R | -^r | --^recursive]
                [--^xml[=<output_file>]] [--^encoding=<name>]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info. This option cannot be combined
                        with '--^xml'.
    --^symlink           Applies the operation to the symlink and not to the
                        target.
    --^selector          Gets the content from the active workspace selector.
                        If selector_format is specified, then lists the
                        specified selector.
                        This is mostly deprecated since selectors are
                        no longer a central part of Unity VCS since 4.x.
    --^tree              Lists the tree in the specified changeset or branch.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)
    -^R | -^r | --^recursive  Lists recursively.
    --^xml               Prints the output in XML format to the standard output.
                        It is possible to specify an output file. This option
                        cannot be combined with '--^format'.
    --^encoding          Used with the '--^xml' option, specifies the encoding to
                        use in the XML output, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    paths               List of paths to show. Use a whitespace to separate
                        paths. Use double quotes (" ") to specify paths 
                        containing spaces.

== CMD_HELP_LIST ==
Remarks:

    - Path can be typed with meta-characters.
    - The list depends on the workspace selector.
    - The output of the command can be formatted specifying a format string.
    - If '--^tree' or '--^selector' options are specified, the given
      path must be a server path (a.k.a.: 'cm path'): /dir/file.txt, not a
      workspace path: C:\Users\<USER>\mywk\dir\file.txt
    - If no path is provided, the workspace path assumed is the current
      directory. If '--^tree' or '--^selector' options are used, then
      the root path ("/") is assumed.

    The default format string is:

    "{^size,10} {^date:dd/MM/yyyy} {^date:HH:mm}\
    {^type,-6} {^location,-12} {^checkout,-5} {^name}\
    {^symlinktarget}"

    Output format parameters (--^format option):

    This command accepts a format string to show the output.
    
    The output parameters of this command are the following:
    - {^size}
    - {^formattedsize}
    - {^date}
    - {^type}
        - ^dir: directory,
        - ^txt: text file,
        - ^File: file.
    - {^location} (Example: ^br:branch#cset)
    - {^checkout}
    - {^name}
    - {^changeset}
    - {^path}
    - {^repspec}
    - {^owner}
    - {^revid}
    - {^parentrevid}
    - {^itemid}
    - {^brid}
    - {^repid}
    - {^server}
    - {^symlinktarget}
    - {^hash}
    - {^chmod}
    - {^wkpath} (Path relative to workspace root)
    - {^branch}
    - {^newlocation} (cset@branch)
    - {^guid} (Will take longer to resolve)
    - {^itemguid}

    You can customize the '^ls' format setting the PLASTIC_LS_FORMAT environment
    variable.

Examples:

    cm ^ls
    cm ^ls c:\workspace\src

    cm ^ls --^format={^name}
    (Only file names.)

    cm ^ls --^symlink
    (Displays information about the symlink instead of the target file or
    directory.)

    cm ^ls code --^selector
    (Shows the content of the 'code' subdirectory from the current workspace
    selector.)

    cm ^ls /code --^selector="^rep 'myrep' ^path '/' ^branch '/^main'"
    (Shows the content of the '/code' subdirectory on the specified selector.
    Note that the path is specified in server format.)

    cm ^ls /code --^tree=44@myrep@denver:7070
    (Lists the '/code' subdirectory at changeset 44 at repo 'myrep' at server
    'denver:7070'.)

    cm ^ls /code --^tree=^br:/main/scm13596@myrep@denver:7070
    (Lists the '/code' subdirectory at the latest changeset in branch
    '/main/scm13596' at repo 'myrep' at server 'denver:7070'.)

    cm ^ls /code --^tree=ae1390ed-7ce9-4ec3-a155-e5a61de0dc77@myrep@denver:7070
    (Lists the '/code' subdirectory at changeset
    ae1390ed-7ce9-4ec3-a155-e5a61de0dc77 at repo 'myrep' at server
    'denver:7070'.)

== CMD_DESCRIPTION_TRIGGER_LIST ==
Lists the triggers of a given type on a server.

== CMD_USAGE_TRIGGER_LIST ==
Usage:

    cm ^trigger | ^tr ^list | ^ls [<subtype-type>] [--^server=<repserverspec>]
                          [--^format=<str_format>]

Options:

    --^server            Lists the triggers on the specified server.
                        If no server is specified, executes the command on the
                        one configured on the client.
                        (Use 'cm ^help ^objectspec' to learn more about server
                        specs.)
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    subtype-type        Trigger execution and trigger operation.
                        (Use 'cm ^showtriggertypes' to see a list of trigger
                        types.)

== CMD_HELP_TRIGGER_LIST ==
Remarks:

    If the type is not specified, lists all the triggers on the server.

    Output format parameters (--^format option):

    This command accepts a format string to show the output.

    The output parameters of this command are the following:
    {0}  Trigger position.
    {1}  Trigger name.
    {2}  Trigger path.
    {3}  Trigger owner.
    {4}  Trigger type.
    {5}  Trigger filter.

Examples:
    cm ^trigger list after-mklabel
    (Lists all triggers of type 'after-mklabel' on the server configured on the
    client.)

    cm ^tr ^ls ^before-mkbranch --^server=myserver:8084
    (Lists all triggers of type 'before-mkbranch' on server 'myserver:8084'.)

== CMD_DESCRIPTION_MANIPULATESELECTOR ==
> **This command is deprecated.** 

== CMD_USAGE_MANIPULATESELECTOR ==
Usage:

    cm ^manipulateselector | ^ms [<wk_path> | <wk_spec>] --^atdate=<sel_date>

    wk_path             Path of the workspace.
    wk_spec             Workspace specification. (Use 'cm ^help ^objectspec' to
                        learn more about specs.)
Options:

    --^atdate            Returns a selector that will recreate the workspace as
                        it would have looked at the specified date.

== CMD_HELP_MANIPULATESELECTOR ==
Remarks:

    If neither path nor workspace spec is specified, the command will take the
    current directory as workspace path.

Examples:

    cm ^manipulateselector c:\workspace --^atdate=yyyy-MM-ddTHH:mm:ss
    cm ^manipulateselector --^atdate=yyyy-MM-ddTHH:mm:ss
    cm ^manipulateselector > mySelector.txt --^atdate=yyyy-MM-ddTHH:mm:ss
    cm ^manipulateselector ^wk:build_wk@BUILDER --^atdate=yyyy-MM-ddTHH:mm:ss

== CMD_DESCRIPTION_MERGE ==
Merges a branch with another branch.

== CMD_USAGE_MERGE ==
Usage:

    cm ^merge <source_spec> [--^merge] [--^cherrypicking] [--^forced]
                           [--^mergetype=(^onlyone|^onlysrc|^onlydst|^try|^forced)]
                           [--^interval-origin=<csetspec> | --^ancestor=<csetspec>]
                           [--^keepsource | --^ks] [--^keepdestination | --^kd]
                           [--^automaticresolution=<conflict-types>[;...]]
                           [--^subtractive] [--^mount] [--^printcontributors]
                           [--^noprintoperations] [--^silent]
                           [(--^to=<brspec> | --^destination=<brspec>)[--^shelve]]
                           [--^no-dst-changes]
                           [-^c=<str_comment> | --^commentsfile=<comments_file>]
                           [--^resolveconflict --^conflict=<index>
                           --^resolutionoption=(^src|^dst|(^rename --^resolutioninfo=<strname>))
                           --^mergeresultfile=<path> --^solvedconflictsfile=<path>]
                           [--^nointeractiveresolution]
                           [--^xml[=<output_file>]] [--^encoding=<name>]
                           [--^machinereadable [--^startlineseparator=<sep>]
                             [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    --^merge                   Performs the merge. Otherwise, prints the
                              conflicts found.
    --^cherrypicking           Merges the changes included on the source 
                              changesets. This option is not used if the merge
                              source specification is a label.
    --^forced                  Does not check if the source and destination are
                              already connected.
                              This option is only available for interval merge 
                              and cherry picking.
    --^mergetype               Type of the merge. See Remarks for more info.
    --^interval-origin         Specifies which changeset is chosen as the 
                              interval origin, so the merge will only take the
                              differences between the source changeset and the 
                              specified interval origin.
    --^ancestor                This is an alias of '--^interval-origin'.
    --^keepsource              Accepts all changes from source contributor for
                              items with conflicts.
    --^keepdestination         Preserves changes from destination contributor
                              for items with conflicts.
    --^automaticresolution     Used to resolve directory conflicts. This option
                              lets you choose whether the source or the
                              destination contributor should be automatically
                              selected to resolve the conflict.
                              Use a semicolon to separate conflict types.
                              See Remarks for more info.
    --^subtractive             Deletes changes introduced by a merge. The
                              parameter passed to the command (source_spec) is
                              used to specify which is the source to delete 
                              changes. It must be a changeset. In the case of a
                              changeset interval, the '--^interval-origin' must
                              be used to define the interval origin. To remove
                              a change, the system creates a new checked out
                              revision which will have the content of the
                              previous one except for the deleted changes.
    --^mount                   The mount point for the given repository.
    --^printcontributors       Prints the contributors (base, source, and 
                              destination).
    --^noprintoperations       Silently resolves merges without showing
                              information about the resolution.
    --^silent                  No output is shown unless an error happens.
    --^to | --^destination      Performs a merge-to operation to the specified
                              branch (by entering a branch spec or brspec)
                              with full conflict resolution. 
                              (Use 'cm ^help ^objectspec' to learn more about
                              branch specs.)
                              A "merge-to" (or workspace-less merge) is a merge
                              done in the server side. While normal merges
                              happen on a workspace merging "from" a branch,
                              label or changeset, a merge-to happens entirely
                              on the server. While in normal merges the
                              "destination" is the workspace, in "merge-to" a
                              destination must be always specified (that's why
                              we call it "to").
                              Check the following link for more information
                              about the "merge to" feature:
                              https://www.plasticscm.com/download/help/mergeto
    --^shelve                  Creates a shelve with the changes of the merge
                              result (plus merge traceability info) instead of
                              creating a new changeset. This option is not
                              available when the merge source is a shelve. This
                              option is only available for server-side-merge
                              (a.k.a. "merge-to"). Hence, the '--^to' and
                              '--^merge' options are required.
    --^no-dst-changes          Ensures that the destination contributor doesn't
                              have changes (the destination changeset is also
                              the common ancestor). When there are changes on
                              the destination, the merge is not allowed.
    -^c                        Applies the specified comment to the changeset
                              created in the merge operation.
    --^commentsfile            Applies the comment in the specified file to the
                              changeset created in the merge operation.
    --^resolveconflict         (Mainly used by plugins. See Remarks for more info.)
                              Used to solve a directory conflict.
    --^conflict                Used with the '--^resolveconflict' flag, specifies
                              the index of the conflict to solve starting at 1.
    --^resolutionoption        Used with the '--^resolveconflict' flag, indicates
                              the type of the conflict resolution. Use one of
                              the following options: '^src', '^dst', '^rename'.
                              See Remarks for more info.
    --^resolutioninfo          Used with the '--^resolveconflict' flag, provides
                              the name to use when the '--^resolutionoption'
                              option is 'rename'.
    --^mergeresultfile         Used with the '--^resolveconflict' flag, outputs
                              into a file the information of the merge result
                              between different calls .The specified path will
                              be created during the first call and updated on 
                              each next call.
    --^solvedconflictsfile     Used with the '--^resolveconflict' flag, outputs
                              into a file the information of the conflicts
                              solved between different calls. The specified
                              path will be created during the first call and
                              updated on each next call.
    --^nointeractiveresolution (Mainly used by plugins. See Remarks for more info.)
                              Avoids prompting the user for manual conflict.
                              This way, a directory conflict won't be solved.
    --^machinereadable         (Mainly used by plugins. See Remarks for more info.)
                              Outputs the result in an easy-to-parse format.
    --^xml                     Prints the output in XML format to the standard output.
                              It is possible to specify an output file. This option
                              cannot be combined with '--^format' nor '--^resolveconflict'.
    --^encoding                Used with the '--^xml' option, specifies the encoding to
                              use in the XML output, i.e.: utf-8.
                              See the MSDN documentation at
                              http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                              to get the table of supported encodings and its format, 
                              (at the end of the page, in the "Name" column).
    --^startlineseparator      Used with the '--^machinereadable' flag, specifies
                              how the lines should start. (Default: empty string.)
    --^endlineseparator        Used with the '--^machinereadable' flag, specifies
                              how the lines should end. (Default: empty string.)
    --^fieldseparator          Used with the '--^machinereadable' flag, specifies
                              how the fields should be separated. (Default:
                              whitespace.)
    source_spec               Specification of the source object to merge from
                              (See Remarks for more info.)

== CMD_HELP_MERGE ==
Remarks:

    This command is used to merge changes between two branches or between a
    label and a branch. The destination of the merge must be always a branch.
    The merge source is specified as an argument.
    Destination is the current content of the workspace.
    For example, to display the elements that will be merged from branch
    task001 to the main branch, the selector must point to the main branch,
    the workspace must be updated, and then:

        cm ^merge ^br:/task001

    To really perform the merge, '--^merge' option is added:

        cm ^merge ^br:/task001 --^merge

    To define the merge source, the following specs can be used:

    - Branch specification (brspec):
        - cm merge [^br:/]br_name
        - Example: cm merge ^br:/main/task001
          (Merges from the last changeset on this branch.)

    - Label specification (lbspec):
        - cm merge ^lb:lb_name
        - Example: cm merge ^lb:BL001
          (Merges from the labeled changeset.)

    - Changeset specification (csetspec):
        - cm merge ^cs:cs_number
        - Example: cm merge ^cs:25
          (Merges from the given changeset content.)

    - Shelve specification (shspec):
        - cm merge ^sh:shelve_number
        - Example: cm merge ^sh:2
          (Merges from the given shelve content.)

    To automatically resolve directory conflicts, use '--^automaticresolution'
    option and specify the type of conflict followed by the contributor
    (source or destination) that must be selected during the merge operation.
    (Separate each "type of conflict"-"contributor" pair by a semicolon (;).)
    For example:
        cm ^merge ^cs:2634 --^merge --^automaticresolution=^eviltwin-src;^changedelete-src
        (The merge operation from changeset 2634 resolves the "^eviltwin" and 
        "^changedelete" conflicts by keeping the source ("-^src") contributor in
        both cases.)
    - A "-^src" suffix after a conflict type tells the merge command to keep the
      source contributor changes.
    - A "-^dst" suffix will keep the destination contributor changes.

    This is the list of conflict types the merge command supports: 
    - ^movedeviltwin
    - ^eviltwin
    - ^changedelete
    - ^deletechange
    - ^movedelete"
    - ^deletemove
    - ^loadedtwice
    - ^addmove
    - ^moveadd
    - ^divergentmove
    - ^cyclemove
    - ^all 

    The "^all" value overrides the other options. In the following example,
    "^eviltwin-dst" will be ignored:

        cm ^merge ^br:/main/task062 --^merge --^automaticresolution=^all-src;^eviltwin-dst

    Check the following link to learn more about merge conflicts:
    https://www.plasticscm.com/download/help/directorymerges

    These are the options for '--^mergetype':
    ^onlyone  Automatic merge if only one contributor modified the item.
    ^onlysrc  Automatic merge if only source contributor modified the item.
    ^onlydst  Automatic merge if only destination contributor modified the item.
    ^try      Automatic merge if only one contributor has modified the
              conflictive piece of code (each conflict).
    ^forced   Always try to solve all non-automatic conflicts.

    These are the options that are mainly used by plugins and integrations:

    - '--^resolveconflict' to solve a directory conflict. You also have to
      use the following options:
        - '--^conflict' is the index of the conflict that you want to 
          solve, starting at 1.
        - '--^resolutionoption' indicates the conflict resolution to 
           use. This can be:
            - '^src' to keep the source change and discard the 
              destination change
            - '^dst' to keep the destination change and discard the 
              source change
            - '^rename' (only if the conflict type supports this 
              resolution), to rename the destination to the given name
              provided with the '--^resolutioninfo' option.
                - '--^resolutioninfo' to provide the name to use on a 
                  '^rename' resolution
        - '--^mergeresultfile' and '--^solvedconflictsfile', both used to 
          store the merge info between different calls.
    - '--^nointeractiveresolution' indicates the merge to not ask the user for
      manual conflict resolution.
    - '--^machinereadable' and '--^startlineseparator', '--^endlineseparator',
      '--^fieldseparator' options to print the output on a machine-readable 
      way (easier-to-parse).

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

    Specification of the source:
    Branch specification     '[^br:/]br_name'
    Label specification      '^lb:lb_name'
    Changeset specification  '^cs:cs_number'
    Shelve specification     '^sh:shelve_number'
    
    (Use 'cm ^help ^objectspec' to learn more about specs.)

Examples:

    cm ^merge ^br:/task001
    (Does not merge, just prints items to be merged.)

    cm ^merge ^br:/task001 --^merge
    (Does merge from branch 'task001'.)

    cm ^merge ^cs:5 --^merge --^cherrypicking --^interval-origin=^cs:2
    (Cherry pick from the changeset interval (2,5].)

    cm ^merge ^cs:8 --^merge --^subtractive --^keepdestination
    (Subtractive merge from changeset 8, keeping destination changes for those
    elements with conflicts.)

    cm ^merge ^br:/main/task001 --^to=^br:/main --^merge -^c="Integrated new UI"
    (Does server-side merge, a.k.a. merge-to, from branch 'task001' to branch
    'main' and sets a comment.)

    cm ^merge ^br:/main/task001 --^to=^br:/main --^merge --^shelve
    (Does server-side merge from branch 'task001' to branch 'main' and leaves
    the result on a shelve.)

    cm ^merge ^sh:2 --^to=^br:/main --^merge --^no-dst-changes
    (Applies the shelve 2 into 'main' only if it was created from the current
    'main' head')

    cm ^merge --^machinereadable --^startlineseparator=start@_@line \
        --^endlineseparator=new@_@line --^fieldseparator=def#_#sep \
        --^mergeresultfile=C:\Users\<USER>\AppData\Local\Temp\2tmp4D6C.tmp \
        --^solvedconflictsfile=C:\Users\<USER>\AppData\Local\Temp\2tmp4D6D.tmp \
        --^resolveconflict --^conflict=1 --^resolutionoption=rename \
        --^resolutioninfo=bin_dst ^br:/main/task --^merge
    (Example for plugins and integrations)

== CMD_DESCRIPTION_ATTRIBUTE_CREATE ==
Creates a new attribute.

== CMD_USAGE_ATTRIBUTE_CREATE ==
Usage:

    cm ^attribute | ^att ^create | ^mk <att_name>

Options:
    att_name            Attribute name

== CMD_HELP_ATTRIBUTE_CREATE ==
Remarks:

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Examples:

    cm ^attribute ^create status
    (Creates the attribute 'status'.)

    cm ^att ^mk integrated
    (Creates the attribute 'integrated'.)

== CMD_DESCRIPTION_BRANCH ==
Allows the user to manage branches.

== CMD_USAGE_BRANCH ==
Usage:

    cm ^branch | ^br <command> [options]

Commands:

    - ^create | ^mk
    - ^delete | ^rm
    - ^rename
    - ^hide
    - ^history
    - ^showmain
    - ^showmerges
    - ^unhide

    To get more information about each command run:
    cm ^branch <command> --^usage
    cm ^branch <command> --^help

== CMD_HELP_BRANCH ==
Examples:

    cm ^branch /main/scm21345
    cm ^branch ^create /main/scm21345
    cm ^branch ^delete /main/scm21345
    cm ^branch ^rename /main/scm21345 scm21346
    cm ^branch ^hide /main/scm21345
    cm ^branch ^history /main/scm21345
    cm ^branch ^showmain
    cm ^branch ^showmerges file.txt
    cm ^branch ^unhide /main/scm21345

== CMD_DESCRIPTION_BRANCH_CREATE ==
Creates a new branch.

== CMD_USAGE_BRANCH_CREATE ==
Usage:

    cm ^branch | ^br [^create | ^mk] <brspec> 
                   [--^changeset=<csetspec> | --^label=<lbspec>]
                   [-^c=<str_comment> | -^commentsfile=<comments_file>]

Options:

    --^changeset     Changeset used as starting point for the new branch.
                    (Use 'cm ^help ^objectspec' to learn more about cset specs.)
    --^label         Label used as starting point for the new branch.
                    (Use 'cm ^help ^objectspec' to learn more about label specs.)
    -^c              Fills in the comment field of the new branch with the
                    specified text.
    -^commentsfile   Fills in the comment field of the new branch with the
                    contents of the specified file.
    brspec           The new branch name or spec.
                    (Use 'cm ^help ^objectspec' to learn more about branch specs.)

== CMD_HELP_BRANCH_CREATE ==
Remarks:

    To create a top-level branch, specify the name without any hierarchy.
    For example:

        cm ^br /dev

    If no optional parameter '--^changeset' is specified, the base of the new
    branch will be the last changeset on the parent branch. If the new branch
    is a top-level branch, the base changeset used will be cset 0.

    You can specify a comment using either the '-^c' or the '-^m' switches:

        cm ^branch /main/task001 -^c="This is the comment"
        cm ^branch /main/task001 -^m "This is the comment"

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Examples:

    cm ^branch task001
    cm ^branch ^create task001
    cm ^branch ^mk task001
    cm ^br ^mk task001
    (All of the examples above, create a top-level 'task001' branch in the
    repository of the current workspace.)

    cm ^branch ^br:/task001/task002@
    (Creates 'task002' branch as child of 'task001'.)

    cm ^br /main/task001@myrep@myserver:8084 -^c="my comment"
    (Creates 'task001' branch as child of 'main' in repository 
    'myrep@myserver:8084' with comment 'my comment'.)

    cm ^branch ^br:/main/task001 --^changeset=2837 -^commentsfile=commenttask001.txt
    (Creates the 'task001' branch as child of 'main' with base 'changeset=2837',
    and applies the comment in 'commenttask001.txt' file.)

== CMD_DESCRIPTION_BRANCH_DELETE ==
Deletes one or more branches.

== CMD_USAGE_BRANCH_DELETE ==
Usage:

    cm ^branch | ^br ^delete | ^rm <brspec>[ ...]
                            [--^delete-changesets]

    brspec              Branch to delete. Use a whitespace to separate branches.
                        (Use 'cm ^help ^objectspec' to learn more about branch
                        specs.)

Options:

    --^delete-changesets     For non-emtpy branches, first deletes the changesets
                            within the branch, then deletes the branch.
                            The operation will fail in the following scenarios:
                            - The branch includes the changeset zero (the main
                            branch in your repository).
                            - One or more changesets in the branch have a label
                            pointing to them.
                            - One or more shelvesets where created from changesets
                            in the branch.
                            - The branch has child branches (even if the child
                            branch is empty).
                            - One or more changesets in the branch are the source
                            of a merge, and the destination is not included in
                            the branch being deleted.
                            The operation is atomic. Either all or none of the
                            changesets in the branch are removed.

== CMD_HELP_BRANCH_DELETE ==
Remarks:

    This command deletes one or more branches.

Examples:

    cm ^branch ^delete /main/task001
    (Deletes the branch with name 'task001' that is a child of 'main' in the
    repository of the current workspace.)

    cm ^br ^rm main/task002 /main/task012@reptest@myserver:8084
    (Deletes branches '/main/task002' in the repository of the current workspace
    and '/main/task012' in the repository 'reptest@myserver:8084'.)

    cm ^br ^rm main/task002 --delete-changesets
    (Deletes branch '/main/task002' and all of its changesets at once).

== CMD_DESCRIPTION_BRANCH_HIDE ==
Hides a branch.

== CMD_USAGE_BRANCH_HIDE ==
Usage:

    cm ^branch | ^br ^hide <brspec>[ ...] 

    brspec              Branch to hide. Use a whitespace to separate branches.
                        (Use 'cm ^help ^objectspec' to learn more about branch
                        specs.)

== CMD_HELP_BRANCH_HIDE ==
Remarks:

    This command hides one or more branches.

Examples:

    cm ^branch ^hide /main/task0 /main/task1
    (Hides branches '/main/task0' and '/main/task1'.)

    cm ^br ^hide ^br:/main@reptest@server2:8084
    (Hides the 'main' branch of repository 'reptest@server2:8084'.)

== CMD_DESCRIPTION_BRANCH_RENAME ==
Renames a branch.

== CMD_USAGE_BRANCH_RENAME ==
Usage:

    cm ^branch | ^br ^rename <brspec> <new_name>

    brspec          Branch to rename.
                    (Use 'cm ^help ^objectspec' to learn more about branch specs.)
    new_name        New name for the branch.

== CMD_HELP_BRANCH_RENAME ==
Remarks:

    This command renames a branch.

Examples:

    cm ^branch ^rename /main/task0 task1
    (Renames branch '/main/task0' to '/main/task1'.)

    cm ^br ^rename ^br:/main@reptest@server2:8084 secondary
    (Renames the 'main' branch of repository 'reptest' to 'secondary'.)

== CMD_DESCRIPTION_BRANCH_HISTORY ==
Shows the history of a branch.

== CMD_USAGE_BRANCH_HISTORY ==
Usage:

    cm ^branch | ^br ^history <brspec> [--^dateformat=<date_format>]
                           [--^machinereadable]

    brspec          The branch specification to obtain the history.
                    (Use 'cm ^help ^objectspec' to learn more about branch specs.)

Options:

    --^dateformat            Format used to output dates.
    --^machinereadable       Outputs the result in an easy-to-parse format.

== CMD_HELP_BRANCH_HISTORY ==
Examples:

    cm ^branch ^history ^br:/main/scm001@myrepository@myserver:8084
    (Displays the history of '/main/scm001' branch of 'myrepository' repository
    on 'myserver' server.)

    cm ^br ^history main --^dateformat="yyyy, dd MMMM" --^machinereadable
    (Displays the history of the 'main' branch of the current repository,
    with a given date format, and in an easy-to-parse format.)

== CMD_DESCRIPTION_BRANCH_SHOWMAIN ==
Shows the main branch of a repository. The main branch of your repository is '/main' by default.

== CMD_USAGE_BRANCH_SHOWMAIN ==
Usage:

    cm ^branch | ^br ^showmain [<repspec>] [--^encoding=<name>]
                            [--^format=<format_str>] [--^dateformat=<date_format>]

Options:

    --^encoding          Specifies the encoding to use in the output,
                        i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^dateformat        Format used to output dates.
    repspec             The repository specification where to show the main
                        branch. If no repspec is specified, the command will show
                        the main branch for the repository of the current
                        workspace.
                        (Use 'cm ^help ^objectspec' to learn more about rep specs.)

== CMD_HELP_BRANCH_SHOWMAIN ==
Remarks:

    This command shows the main branch of a repository.

    Output format parameters (--^format option):
        This command accepts a format string to show the output.
        The output parameters of this command are the following:
        {^id}                Branch id.
        {^comment}           Comment.
        {^date}              Date.
        {^name}              Name.
        {^owner}             Owner.
        {^parent}            Parent branch name.
        {^repository}        Repository.
        {^repname}           Repository name.
        {^repserver}         Server.
        {^changeset}         Head changeset of the branch.

Examples:

    cm ^branch ^showmain
    (Displays the main branch for the repository of the current workspace.)

    cm ^branch ^showmain repo@server:8084
    (Displays the main branch for the repository 'repo' in server
    'server:8084'.)

    cm ^br ^showmain --^dateformat="yyyy, dd MMMM" --^encoding=utf-8
    (Displays the main branch of the repository with a given date format,
    and the output is in utf-8.)

    cm ^br ^showmain --^format="{^id} - {^name}"
    (Displays the main branch of the repository, printing only its id and name.)

== CMD_DESCRIPTION_BRANCH_SHOWMERGES ==
Shows branches pending to be merged.

== CMD_USAGE_BRANCH_SHOWMERGES ==
Usage:

    cm ^branch | ^br ^showmerges <item_path>[ ...]
                              [--^format=<format_str>] 
                              [--^dateformat=<date_format>]

Options:
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^dateformat        Format used to output dates.

== CMD_HELP_BRANCH_SHOWMERGES ==
Remarks:

    Output format parameters (--^format option):
    This command accepts a format string to show the output.
    The output parameters of this command are the following:
    - {^id}                Branch id.
    - {^comment}           Comment.
    - {^date}              Date.
    - {^name}              Name.
    - {^owner}             Owner.
    - {^parent}            Parent branch name.
    - {^parentid}          Parent branch id.
    - {^repid}             Repository id.
    - {^repository}        Repository.
    - {^repname}           Repository name.
    - {^repserver}         Repository server.

Examples:

    cm ^branch ^showmerges file.txt
    (Displays branches involved in the pending merge of 'file.txt'.)

    cm ^branch ^showmerges file.txt --^format="{^date} {^name}" --^dateformat="ddMMyy"
    (Displays branches involved in the merge, printing only the date and the
    name, with a given date format.)

== CMD_DESCRIPTION_BRANCH_UNHIDE ==
Unhides a branch.

== CMD_USAGE_BRANCH_UNHIDE ==
Usage:

    cm ^branch | ^br ^unhide <brspec>[ ...] 

    brspec              Branch to unhide. Use a whitespace to separate branches.
                        (Use 'cm ^help ^objectspec' to learn more about branch
                        specs.)

== CMD_HELP_BRANCH_UNHIDE ==
Remarks:

    This command unhides one or more branches.

Examples:

    cm ^branch ^unhide /main/task0 /main/task1
    (Unhides branches '/main/task0' and '/main/task1'.)

    cm ^br ^unhide ^br:/main@reptest@server2:8084
    (Unhides the 'main' branch of repository 'reptest@server2:8084'.)

== CMD_DESCRIPTION_REPOSITORY ==
Allows the user to manage repositories.

== CMD_USAGE_REPOSITORY ==
Usage:

    cm ^repository | ^repo <command> [options]

Commands:

    - ^create | ^mk
    - ^delete | ^rm
    - ^list | ^ls
    - ^rename
    - ^add

    To get more information about each command run:
    cm ^repository <command> --^usage
    cm ^repository <command> --^help

== CMD_HELP_REPOSITORY ==
Examples:

    cm ^repository
    cm ^repository ^list
    cm ^repository newrepo
    cm ^repository ^create newrepo
    cm ^repository ^rename oldname newname
    cm ^repository ^add C:\repo\

== CMD_DESCRIPTION_REPOSITORY_CREATE ==
Creates a repository on a server.

== CMD_USAGE_REPOSITORY_CREATE ==
Usage:

    cm ^repository | ^repo <rep_name>
    cm ^repository | ^repo <repserverspec> <rep_name>[ ...]
    cm ^repository | ^repo [^create | ^mk] <rep_name>

Options:
    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about rep server
                        specs.)
    rep_name            Name or names of the new repository or repositories.
                        Use a whitespace to separate repository names.

== CMD_HELP_REPOSITORY_CREATE ==
Examples:

    cm ^repository MyRep
    cm ^repo *************:8087 Rep01 Rep01/ModuleA Rep01/ModuleB
    cm ^repo ^create Rep01
    cm ^repo ^mk list

== CMD_DESCRIPTION_REPOSITORY_DELETE ==
Deletes a repository from a server.

== CMD_USAGE_REPOSITORY_DELETE ==
Usage:

    cm ^repository | ^repo ^delete | ^rm <repspec>

Options:

    repspec  Repository specification.
             (Use 'cm ^help ^objectspec' to learn more about rep specs.)

== CMD_HELP_REPOSITORY_DELETE ==
Remarks:

    Deletes a repository from the repository server.
    The data is not removed from the database backend, but unplugged
    so that it will not be accessible anymore.
    (Data can be reconnected afterwards, see 'cm ^repository ^add'.)

Examples:

    cm ^repository ^delete myrepository@^repserver:myserver:8084
    cm ^repository ^rm myrepository@myserver:8084
    cm ^repo ^rm myrepository

== CMD_DESCRIPTION_REPOSITORY_LIST ==
Lists the repositories on a server.

== CMD_USAGE_REPOSITORY_LIST ==
Usage:

    cm ^repository | ^repo [^list | ^ls] [<repserverspec>] [--^format=<str_format>]

Options:

    repserverspec       Repository server specification.
                        (Use 'cm ^help ^objectspec' to learn more about rep server
                        specs.)
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.

== CMD_HELP_REPOSITORY_LIST ==
Remarks:

    Output format parameters (--^format option):

    This command accepts a format string to show the output.
    
    The output parameters of this command are the following:
    {^repid} | {0}      Repository identifier.
    {^repname} | {1}    Repository name.
    {^repserver} | {2}  Server name.
    {^repowner} | {3}   Repository owner.
    {^repguid} | {4}    Unique identifier of the repository.
    {^tab}              Inserts a tab space.
    {^newline}          Inserts a new line.

    If the format parameter value is '^TABLE', the output will be printed
    using a table format with the {^repid}, {^repname} and {^repserver} fields.

Examples:

    cm ^repository
    (Lists all repositories.)

    cm ^repository ^list localhost:8084 --^format="{1, -20} {3}"
    (Writes the repository name in 20 spaces, aligned to left, then a blank,
    and then the repository owner.)

    cm ^repository ^ls localhost:8084 --^format="{^repname, -20} {^repowner}"
    (Writes the same as the previous example.)

    cm ^repo ^ls localhost:8084 --^format=^TABLE
    (Writes the list of repositories using a table format with the following
    fields: repository id, repository name, and repository server name.)

== CMD_DESCRIPTION_REPOSITORY_RENAME ==
Renames a repository.

== CMD_USAGE_REPOSITORY_RENAME ==
Usage:

    cm ^repository | ^repo ^rename [<repspec>] <new_name>

Options:

    repspec    Repository to be renamed.
               (Use 'cm ^help ^objectspec' to learn more about repository
               specifications.)
    new_name   New name for the repository.

== CMD_HELP_REPOSITORY_RENAME ==
Remarks:

    This command renames a repository.
    If no repspec is specified, current repository will be assumed.

Examples:

    cm ^repository ^rename development
    (The current repository will be renamed to 'development'.)

    cm ^repo ^rename ^rep:default@SERVER:8084 development
    (The 'default' repository on 'SERVER' will be renamed to 'development'.)

== CMD_DESCRIPTION_REPOSITORY_ADD ==
Connects an existing repository by adding its database.

== CMD_USAGE_REPOSITORY_ADD ==
Usage:

    cm ^repository | ^repo ^add <db_file> <rep_name> <repserverspec>

Options:

    db_file        The name of the database file on the database backend.
    rep_name       The name of the repository.
    repserverspec  The repository server specification.
                   (Use 'cm ^help ^objectspec' to learn more about repository
                   server specifications.)

== CMD_HELP_REPOSITORY_ADD ==
Remarks:

    Reconnects an existing repository database to the server. This command is
    useful to move a repository from one server to another, or to restore an
    archived repository after using the 'cm ^repository ^delete' command.

Examples:

    cm ^repository ^add rep_27 myrepository myserver:8084

== CMD_DESCRIPTION_TRIGGER_CREATE ==
Creates a new trigger on a server.

== CMD_USAGE_TRIGGER_CREATE ==
Usage:

    cm ^trigger | ^tr ^create | ^mk <subtype-type> <new_name> <script_path>
                                [--^position=<new_position>]
                                [--^filter=<str_filter>]
                                [--^server=<repserverspec>]

Options:

    --^position    New position of the specified trigger.
                  This position must not be in use by another trigger of
                  the same type.
    --^filter      Checks only items that matches the specified filter.
    --^server      Creates the trigger on the specified server.
                  If no server is specified, executes the command on the
                  one configured on the client. (Use 'cm ^help ^objectspec'
                  to learn more about repository server specifications.)
    subtype-type   Trigger execution and trigger operation.
                  (Use 'cm ^showtriggertypes' to see a list of trigger types.)
    new_name      Name of the new trigger.
    script_path    Disk path on the server where the script to execute is
                  located. If the command line starts with "^webtrigger ",
                  the trigger will be considered as a web trigger. See
                  Remarks for more information.

== CMD_HELP_TRIGGER_CREATE ==
Remarks:

    Web triggers: A web trigger is created by typing "^webtrigger <target-uri>"
    as the trigger command. In this case, the trigger will execute a POST query
    against the specified URI -where the request body contains a JSON
    dictionary with the trigger environment variables- and a fixed INPUT key
    pointing to an array of strings.

Examples:

    cm ^trigger ^create ^after-setselector "BackupMgr" "/path/to/script" --^position=4

    cm ^tr ^mk ^before-mklabel new "/path/to/script" --^server=myserver:8084

    cm ^tr ^mk ^after-mklabel Log "/path/to/script" --^filter="^rep:myRep,LB*"
    (This trigger will be executed only if the label name starts with 'LB'
    and it is being created in a repository called 'myRep'.)

    cm ^tr ^mk ^after-checkin NotifyTeam "^webtrigger http://myserver.org/api"

== CMD_DESCRIPTION_MOVE ==
Moves or renames a file or directory.

== CMD_USAGE_MOVE ==
Usage:

    cm ^move | ^mv <src_path> <dst_path> [--^format=<str_format>]
                 [--^errorformat=<str_format>]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    --^errorformat       Retrieves the error message in a specific format. See
                        Remarks for more info.
    src_path            Source item path.
    dst_path            Destination item path.

== CMD_HELP_MOVE ==
Remarks:

    This command moves or renames an item in the repository.
    Changes are done in the local filesystem too.

    If the source path is a file, the destination path can be a file or a
    directory. In the first case, the file is renamed; otherwise, the item
    is moved.

    If source path is a directory, the destination path must be a directory.

    The item to move or rename must exist.

    Format:
    {0}  Source path (both for '--^format' and '--^errorformat')
    {1}  Destination path (both for '--^format' and '--^errorformat')

Examples:

    cm ^move file.txt file.old
    (Renames the item.)

    cm ^mv .\file.old .\oldFiles
    (Moves 'file.old' to 'oldFiles'.)

    cm ^move .\src .\src2
    (Renames a directory.)

== CMD_DESCRIPTION_LABEL ==
Allows the user to manage labels.

== CMD_USAGE_LABEL ==
Usage:

    cm ^label | ^lb <command> [options]

Commands:

    - ^create | ^mk
    - ^delete | ^rm
    - ^rename

    To get more information about each command run:
    cm ^label <command> --^usage
    cm ^label <command> --^help

== CMD_HELP_LABEL ==
Examples:

    cm ^label myNewLabel ^cs:42
    ('^create' command is optional.)
    
    cm ^label ^rename myNewLabel newLabelName
    cm ^label ^delete newLabelName

== CMD_DESCRIPTION_LABEL_CREATE ==
Applies a label to a changeset and creates the label if required.

== CMD_USAGE_LABEL_CREATE ==
Usage:

    cm ^label [^create] <lbspec> [<csetspec> | <wk_path>]
                        [--^allxlinkedrepositories]
                        [-^c=<str_comment> | -^commentsfile=<comments_file>]

Options:

    --^allxlinkedrepositories  Creates the new label in all Xlinked repositories.
    -^c                        Applies the specified comment to the new label.
    -^commentsfile             Applies the comment in the specified file to the
                              new label.
    lbspec                     The new label name.
                              (Use 'cm ^help ^objectspec' to learn more about label
                              specs.)
    csetspec                   Name or full specification of the changeset to label.
                              (Use 'cm ^help ^objectspec' to learn more about changeset
                              specs.)
    wk_path                    Path of the workspace to label. (The changeset that the
                              workspace is pointing will be labeled.)

== CMD_HELP_LABEL_CREATE ==
Remarks:

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Examples:

    cm ^label ^create ^lb:BL001 ^cs:1203 -^commentsfile=commentlb001.txt
    (Creates label 'BL001' attached to changeset 1203, and applies the comment
    in the 'commentlb001.txt' file.)

    cm ^label BL002 ^cs:1203 -^c="first release"
    (Creates label 'BL002', with a comment, and attached to changeset 1203.)

== CMD_DESCRIPTION_LABEL_DELETE ==
Deletes one or more labels.

== CMD_USAGE_LABEL_DELETE ==
Usage:

    cm ^label ^delete <lbspec>[ ...]

Options:

    lbspec          Label to delete. Use a whitespace to separate labels.
                    (Use 'cm ^help ^objectspec' to learn more about label
                    specs.)

== CMD_HELP_LABEL_DELETE ==
Remarks:

    This command deletes one or more labels.

Examples:

    cm ^label ^delete ^lb:BL001
    (Deletes the label 'BL001'.)

    cm ^label ^delete ^lb:BL001 ^lb:BL002@reptest@server2:8084
    (Deletes the labels 'BL001' and 'BL002'.)

== CMD_DESCRIPTION_LABEL_RENAME ==
Renames a label.

== CMD_USAGE_LABEL_RENAME ==
Usage:

    cm ^label ^rename <lbspec> <new_name>

Options:

    lbspec          Label to rename.
                    (Use 'cm ^help ^objectspec' to learn more about label specs.)
    new_name        New name for the label.

== CMD_HELP_LABEL_RENAME ==
Remarks:

    This command renames a label.

Examples:

    cm ^label ^rename ^lb:BL001 BL002
    (Renames the label 'BL001' to 'BL002'.)

== CMD_DESCRIPTION_OBJECTSPEC ==
Describes how to write object specs.

== CMD_USAGE_OBJECTSPEC ==
Usage:

    cm ^objectspec
    (To get all the information about how to build object specs.)

== CMD_HELP_OBJECTSPEC ==

Several Unity VCS commands expect 'object specs' as input to refer to a
given object (typically a branch, changeset, repository, etc).

This documentation describes the different "specs" available and how to
build them.

Each spec type begins with a unique tag, for example "^rep:" or "^cs:". The tag
must be specified for commands that take a general object spec, for example 
"cm ^setowner object_spec", but can often be omitted for commands that take only
a single type of spec, for example, "cm ^getfile revision_spec".

Repository server spec (repserverspec):

    ^repserver:name:port

    Examples:
        cm ^repo ^list ^repserver:skull:8084
        cm ^repo ^list skull:8084

    Side note:
        We call it 'repository server spec', instead of just 'server spec' for
        historical reasons. Long ago, we had separate workspace and repository
        servers, and the naming survived.

Repository spec (repspec):

    ^rep:rep_name@[repserverspec]

    Examples:
        cm ^showowner ^rep:codice@localhost:6060
        (Here the "^rep:" is required because ^showowner admits not only repos
        but also other types of objects. So it needs the user to indicate the
        object type.)

Branch spec (brspec):

    There are different types of branch specs:

    - ^br:[/]br_name[@repspec]
    - ^br:^brguid:branch_guid[@repspec]

    Examples:
        cm ^switch ^br:/main@^rep:plastic@^repserver:skull:9095
        (In this case "^br:", "^rep" and "^repserver" are not needed, so the
         command admits a much shorter form:
        "cm ^switch main@plastic@skull:9095".)

        cm ^find ^revisions "^where ^branch='^br:/main/task001'

        cm ^switch ^br:^brguid:68846cdd-6a46-458c-a47f-52454cc150d9@plastic@skull:9095

        cm ^find ^branch "^where ^parent='^brguid:68846cdd-6a46-458c-a47f-52454cc150d9'"

    Remark:
        The initial '/' on the branch is not mandatory. We used to specify all
        our branches as /main, /main/task001, and so on. But now, we prefer the
        shorter form main, main/task001 which makes commands more compact.

        The guid spec is not valid for commands that needs the full branch name
        (such as "cm ^create ^branch").

Changeset spec (csetspec):

    ^cs:cs_number|cs_guid[@repspec]

    The number or GUID of the changeset can be specified.

    Examples:
        cm ^ls /code --^tree=ae1390ed-7ce9-4ec3-a155-e5a61de0dc77@code@skull:7070

Label spec (labelspec):

    ^lb:lb_name[@repspec]

    Examples:
        cm ^switch ^lb:RELEASE2.0
        cm ^switch ^lb:RELEASE1.4@myrep@MYSERVER:8084

Revision spec:

    There are different types of rev specs:

    - ^rev: item_path[#(brspec|csetspec|labelspec)]
    - ^rev: ^serverpath:item_path#(brspec|cset_spec|lb_spec)
    - ^rev: ^revid:rev_id[@rep_spec]
    - ^rev: ^itemid:item_id#(br_spec|cset_spec|lb_spec)

    Examples:
        cm ^diff ^rev:readme.txt#^cs:19 ^rev:readme.txt#^cs:20

        cm ^diff ^serverpath:/doc/readme.txt#^cs:19@myrepo \
            ^serverpath:/doc/readme.txt#^br:/main@myrepo@localhost:8084

        cm ^cat ^revid:1230@^rep:myrep@^repserver:myserver:8084

Item spec:

    There are different types of item specs:

    - ^item: item_path
    - ^item: ^itemid:item_id[@rep_spec]

    Examples:
        cm ^lock ^unlock ^item:audio.wav
        cm ^lock ^unlock ^itemid:1234@^rep:myrep@^repserver:myserver:8084

Attribute spec:

    ^att:att_name[@repspec]

    Example:
        cm ^attribute ^set ^att:merged@code@doe:8084 ^cs:25@code@doe:8084 done

Shelve spec:

    ^sh:sh_number[@repspec]

    Example:
        cm ^diff ^sh:2 ^sh:4

Workspace specs:

    ^wk:name@clientmachine

    Rarely used, since they only apply to workspace related commands. Useful to
    specify the workspace by name and machine instead of path.

    Examples:
        cm ^showselector ^wk:codebase@modok

    Side note:
        These specs come from the old days of Plastic SCM 2.x where 'workspace
        servers' existed as a way to store workspace metadata in a centralized
        way. Were deprecated due to performance issues.

== CMD_DESCRIPTION_PARTIAL ==
Runs commands in a partial workspace.

== CMD_USAGE_PARTIAL ==
Usage:

    cm ^partial <command> [options]

Commands:

    - ^configure
    - ^add
    - ^undo
    - ^checkout | ^co
    - ^undocheckout | ^unco
    - ^checkin | ^ci
    - ^move | ^mv
    - ^remove | ^rm
    - ^switch | ^stb
    - ^update | ^upd
    - ^shelveset | ^shelve

    To get more information about each command run:
    cm ^partial <command> --^usage
    cm ^partial <command> --^help

== CMD_HELP_PARTIAL ==
Examples:

    cm ^partial ^configure +/background-blue.png
    cm ^partial ^update landscape-1024.png
    cm ^partial ^checkin eyes-green.png eyes-black.png

== CMD_DESCRIPTION_PARTIAL_ADD ==
Adds an item to version control.

== CMD_USAGE_PARTIAL_ADD ==
Usage: 

    cm ^partial ^add [-^R | -^r | --^recursive] [--^silent] [--^parents]
                   [--^ignorefailed] [--^skipcontentcheck] <item_path>[ ...]
                   [--^format=<str-format>] [--^errorformat=<str-format>]

Options:

    -^R | -^r | --^recursive  Adds items recursively.
    --^silent            Does not show any output.
    --^parents           Includes the parent directories of the items specified
                        in the operation.
    --^ignorefailed      If an item cannot be added, the add operation will
                        continue without it. Note: If a directory cannot be
                        added, its content is not added.
    --^skipcontentcheck  When the extension is not enough to set the file as
                        text or binary, it will be set as binary by default
                        instead of checking the content to detect the type.
    --^format            Retrieves the output message in a specific format. Check
                        the examples for more information.
    --^errorformat       Retrieves the error message (if any) in a specific
                        format. Check the examples for more information.
    item_path           Items to add. Use double quotes (" ") to specify paths
                        containing spaces. Use a whitespace to separate paths.
                        Use * to add all the contents of the current directory.

== CMD_HELP_PARTIAL_ADD ==
Remarks:

    Requirements to add items:
    - The parent directory of the item to add must be previously added.

Examples:

    cm ^partial ^add pic1.png pic2.png
    (Adds 'pic1.png' and 'pic2.png' items.)

    cm ^partial ^add c:\workspace\picture.png
    (Adds 'picture.png' item in path 'c:\workspace'.)

    cm ^partial ^add -^R c:\workspace\src
    (Recursively adds 'src'.)
    
    cm ^partial ^add --^parents samples\design01.png
    (Adds 'design01.png' file and 'samples' parent folder.)
    
    cm ^partial ^add -^R *
    (Recursively adds all the contents of the current directory.)

    cm ^partial ^add -^R * --^format="ADD {0}" --^errorformat="ERR {0}"
    (Recursively adds all the contents of the current directory, printing
    '^ADD <item>' for successfully added files, and '^ERR <item>' for items that
    could not be added.)

== CMD_DESCRIPTION_PARTIAL_CHECKIN ==
Stores changes in the repository.

== CMD_USAGE_PARTIAL_CHECKIN ==
Usage:

    cm ^partial ^checkin | ^ci [<item_path>[ ...]]
                            [-^c=<str_comment> | -^commentsfile=<comments_file>]
                            [--^all | -^a] [--^applychanged] [--^keeplock]
                            [--^symlink] [--^ignorefailed]
                            [--^machinereadable [--^startlineseparator=<sep>]
                              [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    -^c                    Specifies a comment to the changeset created in the
                          checkin operation.
    -^commentsfile         Applies the comment from the specified file to the
                          changeset created in the checkin operation.
    --^all | -^a           Includes also the items changed, moved, and deleted
                          locally on the specified paths.
    --^applychanged        Applies the checkin operation to the changed items
                          detected in the workspace along with the checked out
                          items.
    --^private             Private items detected in the workspace are also
                          included.
    --^keeplock            Keeps the lock of the locked items after the checkin
                          operation.
    --^symlink             Applies the checkin operation to the symlink and not to
                          the target.
    --^ignorefailed        Any changes that cannot be applied (because the lock,
                          a.k.a. exclusive checkout, cannot be adquired or 
                          because local changes are in conflict with the server
                          changes) are discarded and the checkin operation
                          continues without them.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag, specifies how
                          the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag, specifies how
                          the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag, specifies how
                          the fields should be separated.
    item_path             Items to checkin. Use double quotes (" ") to specify
                          paths containing spaces. Use a whitespace to separate
                          paths. Use . to apply checkin to current directory.

== CMD_HELP_PARTIAL_CHECKIN ==
Remarks:

    - If <item_path> is not specified, the checkin will involve all the
      pending changes in the workspace.
    - The checkin operation always applies recursively from the given path.
    - To checkin an item:
    - The item must be under source code control.
    - The item must be checked out.
    - If the item is changed but not checked out, the '--^applychanged' flag
      is not necessary unless <item_path> is a directory or it contains
      wildcards ('*').

    Revision content should be different from previous revision in order to be
    checked in.

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Reading input from stdin:

    The '^partial ^checkin' command can read paths from stdin. To do this, pass a
    single dash "-".
    Example: 
    cm ^partial ^checkin -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which files to checkin.
    Example:

      dir /S /B *.c | cm ^partial ^checkin --^all -
      (In Windows, checkins all .c files in the workspace.)

Examples:

    cm ^partial ^checkin figure.png landscape.png
    (Applies the checkin to 'figure.png' and 'landscape.png' checked-out files.)

    cm ^partial ^checkin . -^commentsfile=mycomment.txt
    (Applies checkin to current directory and sets the comment from the 
    'mycomment.txt' file.)

    cm ^partial ^ci background.png -^c="my comment" --^keeplock
    (Applies the checkin to 'background.png', includes a comment, and keeps the
    lock.)

    cm ^partial ^checkin --^applychanged
    (Applies the checkin to all pending changes in the workspace.)
    
    cm ^partial ^checkin link --^symlink
    (Applies the checkin to the symlink file and not to the target.)

    cm ^partial ^checkin . --^ignorefailed
    (Applies checkin to current directory, ignoring the changes that cannot be 
    applied.)

== CMD_DESCRIPTION_PARTIAL_CHECKOUT ==
Marks files as ready to modify.

== CMD_USAGE_PARTIAL_CHECKOUT ==
Usage: 

    cm ^partial ^checkout | ^co [<item_path>[ ...]] [--^resultformat=<str_format>]
                             [--^silent] [--^ignorefailed]

Options:

    item_path           Items to checkout. Use double quotes (" ") to specify
                        paths containing spaces. Use a whitespace to separate
                        paths.
                        Use . to apply checkout to current directory.
    --^resultformat      Retrieves the output result message in a specific 
                        format.
    --^silent            Does not show any output.
    --^ignorefailed      If an item cannot be locked (the exclusive checkout
                        cannot be performed), the checkout operation will
                        continue without it.

== CMD_HELP_PARTIAL_CHECKOUT ==
Remarks: 

    To checkout an item:
    - The item must be under source code control.
    - The item must be checked in.
        
    If locks are configured on the server (lock.conf exists), then each time
    a checkout on a path happens, Unity VCS checks if it meets any of the rules
    and if so, the path will be in exclusive checkout (locked) so that none can
    simultaneously checkout.
    You can get all the locks in the server by using 'cm ^lock ^list'.
    Check the Administrator Guide to learn how locking works:
    https://www.plasticscm.com/download/help/locking

Examples:

    cm ^partial ^checkout pic1.png pic2.png
    (Checkouts 'pic1.png' and 'pic2.png' files.)
    
    cm ^partial ^co *.png
    (Checkouts all png files.)

    cm ^partial ^checkout . 
    (Checkouts current directory.)
    
    cm ^partial ^checkout -^R c:\workspace\src
    (Recursively checkouts 'src' folder.)

== CMD_DESCRIPTION_PARTIAL_CONFIGURE ==
Allows you to configure your workspace by loading or unloading items from it.

== CMD_USAGE_PARTIAL_CONFIGURE ==
Usage:

    cm ^partial ^configure <+|-path>[ ...] [--^silent] [--^ignorefailed]
                         [--^ignorecase] [--^restorefulldirs]
                         [--^machinereadable [--^startlineseparator=<sep>]
                           [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    --^silent              Does not show any output.
    --^ignorefailed        Skips all errors during the process. Incorrect paths
                          will not cause the command to stop.
    --^ignorecase          Ignores casing on the paths. With this flag, '^configure'
                          will work for "/Data/Textures" even if the user writes
                          "/data/teXtures".
    --^restorefulldirs     Resets an invalid directory configuration (happens when
                          a non-partial operation is run on a partial workspace).
                          The directories in this list will be fully configured
                          (full check) which means they will automatically
                          download new content during the update.
                          This operation does not download any files, just
                          restores the directory configuration on partial
                          workspaces.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag, specifies how
                          the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag, specifies how
                          the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag, specifies how
                          the fields should be separated.
    path                   Paths to be loaded or unloaded. Use double quotes (" ") to
                         specify paths containing spaces. Use a whitespace to separate
                         paths. Paths have to start with "/".

== CMD_HELP_PARTIAL_CONFIGURE ==
Remarks:

    The command assumes recursive operation.

Examples:

    cm ^partial ^configure +/landscape_grey.png
    (Loads 'landscape_grey.png' item.)

    cm ^partial ^configure -/landscape_black.png
    (Unloads 'landscape_black.png' item.)

    cm ^partial ^configure +/soft -/soft/soft-black.png
    (Loads all 'soft' directory children items except 'soft-black.png'.)

    cm ^partial ^configure -/
    (Unloads the whole workspace.)

    cm ^partial ^configure -/ +/
    (Loads the whole workspace.)

    cm ^partial ^configure -/figure-64.png --^ignorefailed
    (Unloads 'figure-64.png' item even if it was already unloaded.)
    
    cm ^partial ^configure +/ --^restorefulldirs
    (Sets all directories to automatically download the new content.)
    
    cm ^partial ^configure +/src/lib --^restorefulldirs
    (Sets only '/src/lib' and its subdirectories to automatically download the
    new content.)

== CMD_DESCRIPTION_PARTIAL_MOVE ==
Moves or renames a file or directory.

== CMD_USAGE_PARTIAL_MOVE ==
Usage:

    cm ^partial ^move | ^mv <src_path> <dst_path> [--^format=<str_format>]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.
    src_path             Source item path.
    dst_path             Destination item path.

== CMD_HELP_PARTIAL_MOVE ==
Remarks:

    This command moves or renames an item in the repository. 
    Changes are done in the local filesystem too.
    
    If the source path is a file, the destination path can be a file or a 
    directory. In the first case, the file will be renamed; otherwise, the item 
    will be moved.

    If source path is a directory, the destination path must be a directory.

    The item to move or rename must exist.

    Format:
    {0}   Source path.
    {1}   Destination path.

Examples:

    cm ^partial ^move file.png file-blue.png
    (Renames the item.)

    cm ^partial ^mv .\file-blue.png .\blueFiles
    (Moves 'file-blue.png' to 'blueFiles'.)

    cm ^partial ^move .\design .\marketing
    (Renames a directory.)

== CMD_DESCRIPTION_PARTIAL_REMOVE ==
Deletes a file or directory from version control.

== CMD_USAGE_PARTIAL_REMOVE ==
Usage:

    cm ^partial ^remove | ^rm <item_path>[ ...] [--^nodisk]

Options:

    --^nodisk        Removes from version control, but keeps the item on disk.
    item_path        Items path to remove. Use double quotes (" ") to
                    specify paths containing spaces. Use a whitespace to separate
                    paths.

== CMD_HELP_PARTIAL_REMOVE ==
Remarks:

    Items are deleted from disk. Removed items are removed from the parent
    directory in the source code control.

    Requirements:
    - The item must be under source code control.

Examples:

    cm ^partial ^remove src
    (Removes 'src'. If 'src' is a directory, this is the same that: 
    cm ^partial ^remove -^R src.)

    cm ^partial ^remove c:\workspace\pic01.png --^nodisk
    (Removes 'pic01.png' from version control, but keeps it on disk.)


== CMD_DESCRIPTION_PARTIAL_SHELVESET ==
Allows the user to manage partial shelvesets.

== CMD_USAGE_PARTIAL_SHELVESET ==
Usage:

    cm ^partial ^shelveset | ^shelve <command> [options]

Commands:

    - ^create | ^mk
    - ^apply
    - ^delete | ^rm

    To get more information about each command run:
    cm ^partial ^shelveset <command> --^usage
    cm ^partial ^shelveset <command> --^help

== CMD_HELP_PARTIAL_SHELVESET ==
Examples:

    cm ^partial ^shelveset ^create -^c="my comment"
    cm ^partial ^shelveset ^apply sh:3
    cm ^partial ^shelveset ^delete sh:5

== CMD_DESCRIPTION_PARTIAL_SHELVESET_CREATE ==
Shelves chosen pending changes.

== CMD_USAGE_PARTIAL_SHELVESET_CREATE ==
Usage:

    cm ^partial ^shelveset | ^shelve ^create | ^mk [<item_path>[ ...]]
                            [-^c=<str_comment> | -^commentsfile=<comments_file>]
                            [--^applychanged] [--^symlink] [--^ignorefailed]
                            [--^machinereadable [--^startlineseparator=<sep>]
                              [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    -^c                    Specifies a comment to the changeset created in the
                          shelve operation.
    -^commentsfile         Applies the comment from the specified file to the
                          changeset created in the shelve operation.
    --^applychanged        Applies the shelve operation to the changed items
                          detected in the workspace along with the checked out
                          items.
    --^symlink             Applies the shelve operation to the symlink and not to
                          the target.
    --^ignorefailed        Any changes that cannot be applied (because the lock
                          (a.k.a. exclusive checkout) cannot be adquired or 
                          because local changes are in conflict with the server
                          changes) are discarded and the shelve operation
                          continues without them.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag, specifies how
                          the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag, specifies how
                          the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag, specifies how
                          the fields should be separated.
    item_path              Items to shelve. Use double quotes (" ") to specify
                          paths containing spaces. Use a whitespace to separate
                          paths. Use . to apply shelve to current directory.

== CMD_HELP_PARTIAL_SHELVESET_CREATE ==
The '^partial ^shelveset ^create' command stores the contents of checked out items
    inside the repository. This way the contents are protected without the need to
    checkin the files.

Remarks:

    If neither <item_path> nor any option is specified, the shelveset will
    include all the pending changes in the workspace.

    The '^partial ^shelveset ^create' operation is always applied recursively from 
    the given path.
    
    The '^partial ^shelveset ^create' operation is the default, which means that, 
    if no other operation is defined, the command will try to perform a creation.  

    Requirements to shelve an item:
    - The item must be under source code control.
    - The item must be checked out or changed.

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Examples:

    cm ^partial ^shelveset figure.png landscape.png
    (Creates a new shelveset with 'figure.png' and 'landscape.png' checked-out files.)

    cm ^partial ^shelveset . -^commentsfile=mycomment.txt
    (Creates a new shelveset with every checked-out file in current directory 
    and sets the comment from the 'mycomment.txt' file.)

    cm ^partial ^shelve background.png -^c="my comment"
    (Creates a new shelveset with 'background.png', includes a comment.)

    cm ^partial ^shelveset --^applychanged
    (Creates a new shelveset all pending changes in the workspace.)
    
    cm ^partial ^shelveset link --^symlink
    (Creates a new shelveset with the symlink file and not the target.)

    cm ^partial ^shelveset . --^ignorefailed
    (Creates a new shelveset with every checked-out file in current directory, 
    ignoring (skipping) the changes that cannot be applied.)

== CMD_DESCRIPTION_PARTIAL_SHELVESET_APPLY ==
Applies a stored shelveset.

== CMD_USAGE_PARTIAL_SHELVESET_APPLY ==
Usage:

    cm ^partial ^shelveset ^apply <sh_spec> [--^encoding=<name>]
                       [--^comparisonmethod=(^ignoreeol | ^ignorewhitespaces| \
                                            ^ignoreeolandwhitespaces | ^recognizeall)]

Options:

    --^encoding          Specifies the output encoding, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^comparisonmethod  Sets the comparison method. See Remarks for more info.
    sh_spec              Shelveset specification. (Use 'cm ^help ^objectspec' to
                        learn more about shelveset specs.)

== CMD_HELP_PARTIAL_SHELVESET_APPLY ==
The '^partial ^shelveset ^apply' command restores the contents of a stored shelveset.

Remarks:

    Comparison methods:
    ^ignoreeol                Ignores end of line differences.
    ^ignorewhitespaces        Ignores whitespace differences.
    ^ignoreeolandwhitespaces  Ignores end of line and whitespace differences.
    ^recognizeall             Detects end of line and whitespace differences.

Examples:

    cm ^partial ^shelveset ^apply ^sh:3
    (Applies a stored shelve.)

== CMD_DESCRIPTION_PARTIAL_SHELVESET_DELETE ==
Deletes a shelveset.

== CMD_USAGE_PARTIAL_SHELVESET_DELETE ==
Usage:

    cm ^partial ^shelveset ^delete | ^rm <sh_spec>
    
Options:

    sh_spec             Shelveset specification. (Use 'cm ^help ^objectspec' to
                        learn more about shelveset specs.)

== CMD_HELP_PARTIAL_SHELVESET_DELETE ==
The '^partial ^shelveset ^delete' command deletes a shelveset.

Examples:

    cm ^partial ^shelveset ^delete ^sh:3
    (Removes a stored shelveset.)


== CMD_DESCRIPTION_PARTIAL_SWITCH ==
Sets a branch as the working branch.

== CMD_USAGE_PARTIAL_SWITCH ==
Usage:

    cm ^switch <branch_spec> [--^report | --^silent] [--^workspace=<path>]
                             [--^noinput]
    (Sets the working branch and updates the workspace.)

    cm ^switch <branch_spec> --^configure <+|-path>[ ...] [--^silent]
                            [--^ignorefailed] [--^ignorecase] [--^workspace=<path>]
    (Sets the working branch and runs a workspace configuration like the 'cm
    ^partial ^configure' command does.)

Options:

    --^silent            Does not show any output.
    --^report            Prints a list of the applied changes when the command
                        is finished. Using '--^silent' will override this setting.
                        This option only works when the '--^configure' option
                        is not specified.
    --^configure         Configures (loads / unloads items) the workspace
                        after updating the working branch. Check 'cm ^partial
                        ^configure --^help' to learn how to specify the paths
                        to configure.
    --^ignorefailed      Skips all errors during the configuration process.
                        Incorrect paths will not cause the command to stop.
    --^ignorecase        Ignores casing on the paths. With this flag, option
                        '--^configure' works for "/Data/Textures" even if the user
                        writes "/data/teXtures".
    --^noinput           Skips the interactive question to continue the
                        operation shelving the pending changes.                        
    --^workspace=path    Path where the workspace is located.
    branch_spec          Branch specification. (Use 'cm ^help ^objectspec' to learn
                        more about branch specs.)
    path                 Paths to be loaded or unloaded. Use double quotes (" ")
                        to specify paths containing spaces. Use a whitespace to
                        separate paths. Paths must start with "/".

== CMD_HELP_PARTIAL_SWITCH ==
Remarks:

    This command allows users to update the working branch. After updating the
    branch, the command updates the workspace to the new branch as the
    'cm ^partial ^update' command would do. However, if the '--^configure' option is
    specified, the command allows to configure the workspace using the new
    branch configuration as the 'cm ^partial ^configure' command would do.

Examples:

    cm ^switch ^br:/main/task
    (Sets /main/task as working branch and updates the workspace.)

    cm ^switch ^br:/main/task --^configure +/art/images
    (Sets /main/task as working branch and configures the workspace to
    load the /art/images folder.)

== CMD_DESCRIPTION_PARTIAL_UNDOCHECKOUT ==
Undoes the checkout on an item.

== CMD_USAGE_PARTIAL_UNDOCHECKOUT ==
Usage:

    cm ^partial ^undocheckout | ^unco <item_path>[ ...] [--^silent]
        [--^keepchanges | -^k]

Options:

    --^silent            Does not show any output.
    --^keepchanges (-^k)  Undoes the checkout and preserves the local changes.
                        Sample: undo the checkout of a file leave it as locally
                        changed with the same content on disk that it was.
                        This option cannot be used with dynamic workspaces.
    item_path            Items to apply the operation. Use double quotes (" ")
                        to specify paths containing spaces. Use a whitespace to
                        separate paths. Use . to apply the operation to current
                        directory.

== CMD_HELP_PARTIAL_UNDOCHECKOUT ==
Remarks:

    If an item is checked-out and you do not want to checkin it, you can undo
    the checkout using this command. Both files and folders can be unchecked 
    out. The item will be updated to the state it had before checking it out.

    Requirements: 
    - The item must be under source code control.
    - The item must be checked out.

Examples:

    cm ^partial ^undocheckout . 
    (Undoes checkouts in the current directory.)

    cm ^partial ^undocheckout pic1.png pic2.png
    cm ^unco c:\workspace\design01.png
    (Undoes checkouts of the selected files.)

== CMD_DESCRIPTION_PARTIAL_UNDO ==
Undoes changes in a workspace.

== CMD_USAGE_PARTIAL_UNDO ==
Usage:

    cm ^partial ^undo [<path>[ ...]] [--^symlink] [-^r | --^recursive] 
                    [<filter>[ ...]]
                    [--^silent | --^machinereadable [--^startlineseparator=<sep>]
                                [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    --^symlink               Applies the undo operation to the symlink and not
                            to the target.
    -^r | --^recursive       Executes the undo recursively.
    --^silent                Does not show any output.
    --^machinereadable       Outputs the result in an easy-to-parse format.
    --^startlineseparator    Used with the '--^machinereadable' flag, specifies
                            how the lines should start.
    --^endlineseparator      Used with the '--^machinereadable' flag, specifies
                            how the lines should end.
    --^fieldseparator        Used with the '--^machinereadable' flag, specifies
                            how the fields should be separated.
    --^checkedout            Select checked-out files and directories.
    --^unchanged             Select files whose content is unchanged.
    --^changed               Select locally changed or checked-out files and
                            directories.
    --^deleted               Select deleted files and directories.
    --^moved                 Select moved files and directories.
    --^added                 Select added files and directories.
    path                     Path of the files or directories to apply the
                            operation to. Use double quotes (" ") to specify
                            paths containing spaces. Use a whitespace to
                            separate paths.
                            If no path is specified, by default the undo
                            operation will take all of the files in the current
                            directory.
    filter                   Applies the specified filter or filters to the given
                            paths. Use a whitespace to separate filters. See the
                            Filters section for more information.

== CMD_HELP_PARTIAL_UNDO ==
Remarks:

    The ^undo command is dangerous - it undoes work in an irreversible way.
    Once the ^undo has finished, it is not possible to recover the previous state
    of the files and directories affected by it. If no path is specified
    in the arguments, by default it will undo every change in the current
    directory, but not recursively.
    
    These are equivalent when executed from the /src directory:

- src
    - file.txt
    - code.cs
    - /test
        - test_a.py
        - test_b.py

    cm ^partial ^undo
    cm ^partial ^undo *
    cm ^partial ^undo file.txt code.cs /test
    cm ^partial ^undo .
    cm ^partial ^undo /src file.txt code.cs

    If you want the operation to be recursive, you must specify the '-^r' flag.

    To undo all of the changes below a directory (including changes affecting
    the directory itself):

    cm ^partial ^undo dirpath -^r

    If dirpath is a workspace path, every change in the workspace will be
    undone.

Filters:

    If no flag is specified, by default, all changes are undone, but the
    paths can be filtered using one or more of the flags below.
    If a file or directory matches one or more of the specified kinds of change,
    all of the changes on said file or directory will be undone.
    For example, if you specify both '--^checkedout' and '--^moved', if a file is
    both checkedout and moved, both changes will be undone.

Examples:

    cm ^partial ^undo . -^r
    (Undoes all changes in the current directory recursively. If executed
    from the workspace's root, undoes all changes in the entire workspace.)

    cm ^partial ^co file.txt
    cm ^partial ^undo file.txt
    (Undoes the checkout on file.txt.)

    ^echo ^content >> file.txt
    cm ^partial ^undo file.txt
    (Undoes the local change to file.txt.)

    cm ^partial ^undo src
    (Undoes changes to the src directory and its files.)

    cm ^partial ^undo src/*
    (Undo changes in every file and directory contained in src, without
    affecting src.)

    cm ^partial ^undo *.cs
    (Undo changes to every file or directory that matches *.cs in the current
    directory.)

    cm ^partial ^undo *.cs -^r
    (Undoes changes on every file or directory that matches *.cs in the current
    directory and every directory below it.)

    cm ^partial ^co file1.txt file2.txt
    ^echo ^content >> file1.txt
    cm ^partial ^undo --^unchanged
    (Undoes the checkout of unchanged file2.txt, ignoring locally changed
    file1.txt.)

    ^echo ^content >> file1.txt
    ^echo ^content >> file2.txt
    cm ^partial ^co file1.txt
    cm ^partial ^undo --^checkedout
    (Undoes the changes in checked-out file file1.txt, ignoring file2.txt as it is
    not checked-out.)

    cm ^partial ^add file.txt
    cm ^partial ^undo file.txt
    (Undoes the add of file.txt, making it once again a private file.)

    ^rm file1.txt
    ^echo ^content >> file2.txt
    cm ^partial ^add file3.txt
    cm ^partial ^undo --^deleted --^added *
    (Undoes the file1.txt delete and file3.txt add, ignoring the file2.txt
    change.)

== CMD_DESCRIPTION_PARTIAL_UPDATE ==
Updates the partial workspace and downloads latest changes.

== CMD_USAGE_PARTIAL_UPDATE ==
Usage:

    cm ^partial ^update [<item_path>[ ...]] [--^changeset=<number>]
                      [--^silent | --^report] [--^dontmerge]
                      [--^xml[=<output_file>]] [--^encoding=<name>]
                      [--^machinereadable [--^startlineseparator=<sep>]
                        [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    --^changeset          Updates the partial workspace to a specific changeset.
                         Using '--^incoming' will override this setting.
    --^silent             No output is shown unless an error happens.
    --^xml                Prints the output in XML format to the standard 
                         output. It is possible to specify an output file.
    --^encoding           Used with the '--xml' option, specifies the encoding to
                         use in the XML output, i.e.: utf-8.
                         See the MSDN documentation at
                         http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                         to get the table of supported encodings and its format,
                         (at the end of the page, in the "Name" column). 
                         By default "utf-8" will be assigned.
    --^report             Prints a list of the applied changes when the command
                         is finished. Using '--^silent' will override this setting.
    --^dontmerge          Does not merge the file conflicts, it just skips them.
                         The other changes are properly applied. This option can
                         be useful for automation to avoid user interaction.
    --^incoming           Runs the Incoming Changes operation applying only the
                         local changes involved in the operation.
    --^machinereadable    Outputs the result in an easy-to-parse format.
    --^startlineseparator Used with the '--^machinereadable' flag,
                         specifies how the lines should start.
    --^endlineseparator   Used with the '--^machinereadable' flag,
                         specifies how the lines should end.
    --^fieldseparator     Used with the '--^machinereadable' flag,
                         specifies how the fields should be separated.
    item_path             Items to be updated. Use double quotes (" ") to specify
                         paths containing spaces. Use a whitespace to separate
                         paths. Use . to apply update to current directory.
                         If no path is specified, then the current partial
                         workspace is fully updated.

== CMD_HELP_PARTIAL_UPDATE ==
Remarks:

    The '^partial ^update' command updates the out-of-date files.

    The command assumes recursive operation.

    If all the specified paths are files inside the same Xlink when using the
    '--^changeset' option, then the versions to download are searched in the
    specified changeset of the Xlinked repository.

Examples:

    cm ^partial ^update
    (Updates all in the current partial workspace.)

    cm ^partial ^update .
    (Updates all current directory children items.)

    cm ^partial ^update backgroud-blue.png
    (Updates 'backgroud-blue.png' item.)

    cm ^partial ^update soft_black.png soft-grey.png
    (Updates 'soft_black.png' and 'soft-grey.png' items.)

    cm ^partial ^update src --^report
    (Updates all 'src' directory children items, printing the applied changes
    list at the end.)

    cm ^partial ^update src --^changeset=4
    (Updates all 'src' directory children items to the content they loaded
    in the changeset 4.)

    cm ^partial ^update xlink/first.png --^changeset=4
    (Updates 'xlink/first.png' item to the content it loaded in the changeset 4
    of the Xlinked repository.)

    cm ^partial ^update . --^changeset=2 --^xml=output.xml 2>errors.txt 
    (Updates all current directory children items to the content they loaded 
    in the changeset 2, reporting the result in XML format. The output is stored
    in a file named 'output.xml' and errors are redirected to the file 'errors.txt'.
    NOTE: redirection syntax rely on the shell.)

== CMD_DESCRIPTION_PATCH ==
Generates a patch file from a spec or applies a generated patch to the current
workspace.

== CMD_USAGE_PATCH ==
Usage:

    cm ^patch <source_spec> [<source_spec>] [--^output=<output_file>]
             [--^tool=<path_to_diff>]
    (Generates a patch file that contains the differences of a branch,
    a changeset, or the differences between changesets. It also tracks
    differences of text and binary files.)

    cm ^patch --^apply <patch_file> [--^tool=<path_to_patch>]
    (Allows to apply the contents of a generated patch file in the current
    workspace.)

Options:

    --^output        Sets the output file of the patch command.
    --^tool          Sets the application to use (diff or patch).
    source_spec     Full spec of a changeset or a branch. (Use 
                    'cm ^help ^objectspec' to learn more about specs.)
    output_file     File to save the patch content. It no file is specified,
                    the patch content will be printed on standard output.
    patch_file      Patch file to apply in the current workspace.

== CMD_HELP_PATCH ==
Limitations:

    If the output patch file already exists, the command will not overwrite it.

    When applying a patch, the command will not apply changes to modified files
    if they are not present on disk.

Important: 

    This command requires Diff and Patch tools, publicly available at
    http://gnuwin32.sourceforge.net/packages/patch.htm and
    http://gnuwin32.sourceforge.net/packages/diffutils.htm

    Once installed, it's recommended to add their location to the PATH
    environment variable.

Examples:

    cm ^patch ^cs:4@default@localhost:8084
    (Prints on console the differences of cset 4 in unified format.)

    cm ^patch ^br:/main --^output=file.patch
    (Generates a patch file with the differences of branch "main".)

    cm ^patch ^br:/main --^output=file.patch --^tool=C:\gnu\diff.exe
    (Same as above, using a custom exe.)

    cm ^patch ^cs:2@default ^cs:4@default
    (Prints on console the differences between csets 2 and 4 in unified format.)

    cm ^patch --^apply file.patch --^tool=C:\gnu\patch.exe
    (Applies the patch in 'file.patch' to the local workspace with a custom exe.)

== CMD_DESCRIPTION_PROFILE ==
Allows the user to manage server connection profiles.

== CMD_USAGE_PROFILE ==
Usage:

    cm ^profile <command> [options]

Commands:

    - ^list | ^ls
    - ^create | ^mk
    - ^delete | ^rm

    To get more information about each command run:
    cm ^profile <command> --^usage
    cm ^profile <command> --^help

== CMD_HELP_PROFILE ==
Examples:

    cm ^profile
    cm ^profile ^list
    cm ^profile ^create
    cm ^profile ^delete 1

== CMD_DESCRIPTION_PROFILE_LIST ==
Lists the server connection profiles configured on the client.

== CMD_USAGE_PROFILE_LIST ==
Usage:

    cm ^profile [^list | ^ls] [--^format=<str_format>]

Options:

    --^format        Retrieves the output message in a specific format.
                    See Remarks for more info.

== CMD_HELP_PROFILE_LIST ==
Remarks:

    Output format parameters (--^format option):

    This command accepts a format string to show the output.

    The output parameters of this command are the following:
    {^index} | {0}           Profile index in the profiles list
    {^name} | {1}            Profile name
    {^server} | {2}          Profile server
    {^user} | {3}            Profile user
    {^workingmode} | {4}     Profile working mode
    {^securityconfig} | {5}  Profile security config
    {^tab}                   Inserts a tab space
    {^newline}               Inserts a new line

Examples:

    cm ^profile
    (Lists all the profiles using the default format)

    cm ^profile --^format="{index,2} {server,-20}"
    (Writes the profile index in 2 spaces, aligned to the right, then two blanks,
    and then the repository server in 20 spaces, aligned to the left.)

    cm ^profile --^format="{0,2}  {2,-20}"
    (Writes the same output as the previous example.)

== CMD_DESCRIPTION_PROFILE_CREATE ==
Creates a new server connection profile.

== CMD_USAGE_PROFILE_CREATE ==
Usage:

    cm ^profile [^create | ^mk]
    (Creates a new profile interactively.)

    cm ^profile [^create | ^mk] --^server=<server_addr> --^username=<username>
                     --^password=<password> --^workingmode=<workingmode>
    (Creates a new server connection profile using a user/password
    authentication mode.)

    cm ^profile [^create | ^mk] --^server=<server_addr> --^username=<username>
                     --^token=<token> --^workingmode=SSOWorkingMode
    (Creates a new server connection profile using Single Sign On authentication
    mode.)

Options:

    --^server       Creates the connection profile for the specified server.
    --^username     The username that should be used in the connection profile
    --^password     The plain-text password that should be used in the connection
                   profile. This option is only valid for authentication modes
                   that are based on a user and password.
    --^token        The plain-text token that should be used in the connection
                   profile. This option is only valid for authentication modes
                   that are based on a token (SSOWorkingMode for now).
    --^workingmode  The target server's authentication mode.
                   Available users/security working modes:
                   ^LDAPWorkingMode (LDAP)
                   ^UPWorkingMode (User and password)
                   ^SSOWorkingMode (Single Sign On)

== CMD_HELP_PROFILE_CREATE ==
Remarks:

    When using this command interactively (without options), the client will try
    to connect to the server to obtain the working mode and check the credentials.
    This guarantees that the resulting profile is correct.

    When specifying the options, the client will generate the connection profile
    without connecting to the server. This is useful when creating connection
    profiles for automation purposes.

Examples:

    cm ^profile ^create
    (Creates a new connection profile interactively.)

    cm ^profile ^create --^server=plastic.domain.com:8087 --^username=sergio
        --^password=thisissupersecret --^workingmode=LDAPWorkingMode
    (Creates a new connection profile to connect to 'plastic.domain.com:8087'
    using user 'sergio' and password 'thisissupersecret' through LDAP working mode.)

    cm ^profile ^create --^server=plastic.domain.com:8087 --^username=sergio
        --^token="TOKENAMoKJ9iAA(...)12fssoprov:unityid" --workingmode=^SSOWorkingMode
    (Creates a new connection profile to connect to 'plastic.domain.com:8087'
    using user 'sergio' and the specified token through Single Sign On working mode.)

== CMD_DESCRIPTION_PROFILE_DELETE ==
Deletes a server connection profile from the client's configuration.

== CMD_USAGE_PROFILE_DELETE ==
Usage:

    cm ^profile ^delete | ^rm <index | name>
    cm ^profile ^delete | ^rm --^index=<index>
    cm ^profile ^delete | ^rm --^name=<name>

Options:

    --^index     Used to disambiguate in case a profile has a name that is a number
    --^name      Used to disambiguate in case a profile has a name that is a number
    index       Profile index in the profiles list.
    name        Profile name.

== CMD_HELP_PROFILE_DELETE ==
Remarks:

    Deletes a server connection profile from the client's configuration.
    It works both with the profile index and the profile name.
    The 'cm ^profile ^list' command does not show profile names by default,
    check 'cm ^profile ^list --help' to check how to output profile's name.

Example:

    cm ^profile ^delete 1
    (Removes the profile at index 1.)

    cm ^profile ^delete ***********:8087_UPWorkingMode
    (Removes the profile with name '***********:8087_UPWorkingMode'.)

    cm ^profile ^delete --name=12
    (Removes the profile with name '12'.)

== CMD_DESCRIPTION_QUERY ==
Executes SQL queries. Requires SQL storage.

== CMD_USAGE_QUERY ==
Usage:

    cm ^query <sql_command> [--^outputfile=<output_file>]
                           [--^solveuser=<column_name>[,...]]
                           [--^solvepath=<column_name>[,...]]
                           [--^columnwidth=<value>] [--^nocolumnname]
                           [--^columnseparator=<sep>] [--^repository=<name>]

Options:

    --^outputfile        Writes the result in an output file.
    --^solveuser         Sets the specified columns as username columns. The
                        query interpreter will assume that data of these columns
                        will be users, and will try to solve them.
    --^solvepath         Sets the specified columns as itemid column. The query
                        interpreter will try to solve item id to filesystem
                        paths.
    --^columnwidth       Specifies the width of each column to format the output.
    --^nocolumnname      Does not print column name.
    --^columnseparator   Uses char as column separator instead of a tab (\t).
    --^repository        Repository to query.
    sql_command          The sql query to be executed.

== CMD_HELP_QUERY ==
Remarks:

    This command allows users to execute SQL queries in the server database.

    In order to write SQL queries, use these two pre-defined functions to manage
    users and paths:
    - '^SolveUser(<username>)' that resolves a username into Unity VCS format.
    - '^SolvePath(<path>)' that resolves a disk path into an item id.

    Also, you can use options to show query results in a human readable form.

    You can use the options '--^solveuser=<column_name>' and 
    '--^solvepath=<column_name>' to specify columns that query interpreter
    must convert to a legible text. You can specify more than one column name,
    comma separated.

Examples:

    cm ^query "^SELECT * ^FROM ^revision" --^columnwidth=25 --^repository=reptest
    (Retrieves data from 'revision' table from repository 'reptest'.)

    cm ^query "^SELECT b.^sname ^as br_name, o.^dtimestamp ^as date ^from ^branch b, \
        ^object o, ^seid s ^where b.^iobjid=o.^iobjid ^and o.^fidowner=s.^iseidid ^and \
        s.^scode='^SolveUser(john)'" --^outputfile=query.txt
    (Outputs into a file the branches with owner 'john'.)

    cm ^query "^select r.^iobjid, r.^fiditem ^as path, s.^scode ^as username ^FROM \
        ^revision r, ^object o, ^seid s ^WHERE r.^iobjid=o.^iobjid ^and \
        o.^fidowner=s.^iseidid ^and o.^dtimestamp>04/25/2014" \
    --^solveuser=username --^solvepath=path --^repository=reptest@server2:9095
    (Retrieves selected data from selected repository.)

    cm ^query "^SELECT * ^FROM ^revision ^WHERE ^fiditem=^SolvePath(c:\mywkpath\info)"
    (Retrieves all revision data of path 'info'.)

== CMD_DESCRIPTION_ATTRIBUTE_DELETE ==
Deletes one or more attributes.

== CMD_USAGE_ATTRIBUTE_DELETE ==
Usage:

    cm ^attribute | ^att ^delete | ^rm <att_spec>[ ...]

Options:
    att_spec            Attributes to delete. Use a whitespace to separate
                        attributes.
                        (Use 'cm ^help ^objectspec' to learn more about attribute
                        specs.)

== CMD_HELP_ATTRIBUTE_DELETE ==
Remarks:

    This command removes one or more attributes.

Examples:

    cm ^attribute ^delete ^att:status
    (Deletes the attribute 'status'.)

    cm ^att ^rm status ^att:integrated@reptest@server2:8084
    (Deletes the attributes 'status' and 'integrated'.)

== CMD_DESCRIPTION_ATTRIBUTE_UNSET ==
Unsets an object's attribute.

== CMD_USAGE_ATTRIBUTE_UNSET ==
Usage:

    cm ^attribute | ^att ^unset <att_spec> <object_spec>

    att_spec            Attribute specification. (Use 'cm ^help ^objectspec' to
                        learn more about attribute specs.)
    object_spec         Specification of the object to remove the attribute
                        from. Attributes can be set on: branches, changesets,
                        shelvesets, labels, items, and revisions.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_ATTRIBUTE_UNSET ==
Remarks:

    The command unsets an attribute that was previously set on an object. It 
    does not delete the attribute object itself.

Examples:

    cm ^attribute ^unset ^att:status ^br:/main/SCM105
    (Removes attribute realization 'status' from branch 'main/SCM105'.)

    cm ^att ^unset ^att:integrated@reptest@localhost:8084 ^cs:25@reptest@localhost:8084
    (Removes attribute realization 'integrated' from changeset 25, all in
    repository 'reptest'.)

== CMD_DESCRIPTION_ATTRIBUTE_RENAME ==
Renames an attribute.

== CMD_USAGE_ATTRIBUTE_RENAME ==
Usage:

    cm ^attribute | ^att ^rename <att_spec> <new_name>

    att_spec            Attribute to rename. (Use 'cm ^help ^objectspec' to learn
                        more about attribute specs.)
    new_name            New name for the attribute.

== CMD_HELP_ATTRIBUTE_RENAME ==
Remarks:

    This command renames an attribute.

Examples:

    cm ^attribute ^rename ^att:status state
    (Renames the attribute 'status' to 'state'.)

== CMD_DESCRIPTION_ATTRIBUTE_EDIT ==
Edits the comment of an attribute.

== CMD_USAGE_ATTRIBUTE_EDIT ==
Usage:

    cm ^attribute | ^att ^edit <att_spec> <new_comment>

Options:
    att_spec        Attribute to change its comment. (Use 'cm ^help ^objectspec'
                    to learn more about attribute specs.)
    new_comment     New comment for the attribute. You can also specify a
                    default list of values for the attribute. See Remarks for
                    more info.

== CMD_HELP_ATTRIBUTE_EDIT ==
Remarks:

    This command changes the comment of an attribute.

    To specify a default list of values for the attribute, you just need to 
    include a line like the following in the attribute comment:
    'default: value_one, "value two", value3, "Final value"'.

Examples:

    cm ^attribute ^edit ^att:status "The status of a branch in the CI pipeline."
    (Edits the comment of the attribute 'status'.)

    cm ^attribute ^edit ^att:status "Status of a branch. default: open, resolved, reviewed"
    (Edits the comment of the attribute 'status'. And also specifies a list of
    values. So when you set the attribute 'status' to an object, you can select
    one of the following values: "open", "resolved", or "reviewed".)

== CMD_DESCRIPTION_PULL ==
Pulls a branch from another repo.

== CMD_USAGE_PULL ==
Usage:

    cm ^pull <src_br_spec> <dst_rep_spec>
            [--^preview] [--^nodata] [TranslateOptions]
            [--^user=<usr_name> [--^password=<pwd>] | AuthOptions]
    (Direct server-to-server replication. Pulls a branch from a repository.)

    cm ^pull <dst_rep_spec> --^package=<pack_file> [AuthOptions]
    (Package based replication. Imports the package in the destination repository.)

    cm ^pull ^hydrate <dst_br_spec> [<src_rep_spec>]
                    [--^user=<usr_name> [--^password=<pwd>] | AuthOptions]
    (Introduces the missing data for all the changesets of a branch previously
    replicated with '--^nodata'. If a repo to obtain the data is not specified,
    Unity VCS tries to use the "replication source" (origin of the replicated
    branch)).

    cm ^pull ^hydrate <dst_cs_spec> [<src_rep_spec>]
        [--^user=<usr_name> [--^password=<pwd>] | AuthOptions]
    (Introduces the missing data for a changeset previously replicated with
    '--^nodata'. If a repo to obtain the data is not specified, Unity VCS tries
    to use the "replication source").

Options:

    --^preview      Gives information about what changes will be pulled but
                   no changes are actually performed. This option is useful
                   to check the data that will be transferred before
                   replicating changes.
    --^nodata       Replicates the branch changes without replicating the
                   data. This option is not allowed with package
                   replication.
    --^user, --^password  Credentials to use if the authentication mode is
                   different in source and destination and there is not a
                   profile to authenticate to destination.
    --^package      Specifies the previously created package file to import
                   for package based replication. Useful to move data between
                   servers when there is no direct network connection. Refer to
                   'cm ^push' to create a package file.
    src_br_spec     The branch to pull from a remote repository.
                   (Use 'cm ^help ^objectspec' to learn more about branch specs.)
    dst_br_spec     The branch to hydrate.
                   (Use 'cm ^help ^objectspec' to learn more about branch specs.)
    dst_cs_spec     The changeset to hydrate.
                   (Use 'cm ^help ^objectspec' to learn more about changeset specs.)
    dst_rep_spec    The destination repository.
                   (Use 'cm ^help ^objectspec' to learn more about repository specs.)
    TranslateOptions  See the Translate options section for more information.
    AuthOptions       See the Authentication options section for more information.

Translate options:
    Mode:

    --^trmode=(^copy|^name|^table --^trtable=<translation_table_file>)
      The source and destination repositories may use different authentication
      modes. The '--^trmode' option specifies how to translate the user names from
      the source to the destination. The '--^trmode' must be one of the following
      values:
          ^copy    (Default). Means that the user identifiers will be just copied.
          ^name    The user identifiers will be matched by name.
          ^table   Uses a translation table specified in the option '--^trtable'
                  (see below).

    Specifics if mode is set to "table":

    --^trtable=<translation_table_file>
        If the translation mode is 'table', a translation table is a file
        containing lines in the form <oldname;newname> (one per line). When the
        branch is written to the destination repository, the objects created by
        a user identified by "oldname" in the source repository will be set
        to the user with "newname" on the destination.

Authentication options:

    Authentication data can be specified using one of the two following modes:
    - Using authentication parameters.
    - Using an authentication file.


    Using authentication parameters: 

        --^authmode=<mode> --^authdata=<data>

    Where --^authmode can take one of the following values 

    - ^NameWorkingMode
    - ^LDAPWorkingMode
    - ^ADWorkingMode
    - ^UPWorkingMode

        Examples:
        (^LDAPWorkingMode)  --^authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
        (^UPWorkingMode)    --^authdata=dave:fPBea2rPsQaagEW3pKNveA==

        The '--^authdata' line is the content of the <^SecurityConfig> entry
        in the client.conf file and the profiles.conf file. The profiles.conf
        file can be easily generated from the Unity VCS GUI in the replication
        profiles tab under Preferences.

        If you are using ^UPWorkingMode, you can simply specify:

        --^authmode=^UPWorkingMode --^user=<user> --^password=<psw>

    Using an authentication file:

    It's possible to have a different file for each server you connect to,
    with the credentials for that server and --^authfile=<authentication_file>
    where the authentication_file must be formed by the following 2 lines:

    1) mode, as described in '--^authmode'
    2) authentication data, as described in '--^authdata'

== CMD_HELP_PULL ==
Remarks:

    The '^pull' command is able to replicate branches (along with their
    changesets) between a source repository and a destination repository.
    The repositories can be located at different servers.

    There are two replication operations: '^push' and '^pull'.

    A '^pull' operation means that the replication operation will demand data
    from the source repository to be stored into the destination repository.
    The client will connect to the destination repository and, from that host,
    it will establish a connection to the source repository to retrieve the
    targeted data. During pull it is the destination server which will be
    connected to the source.

    Although in a typical distributed scenario a developer pushes data from his
    local server to the main server, the developer might want to pull the latest
    repository updates from the main server, too.

    Replication can resolve situations where concurrent changes have been made
    on the same branch on two replicated repositories:

    - Push: If you try to push your data to a repository having newer changes
      than those you are sending, the system will ask you to pull the latest
      changes, resolve the merge operation and, finally, try to push again.
    - Pull: Whenever you pull changesets from a remote branch, they will be
      correctly linked to their parent changesets. If the changeset you pulled
      is not a child of the last changeset in the branch, then a multi-headed
      scenario will appear. The branch will have more than one 'head', or last
      changeset on the branch. You will need to merge the two 'heads' before
      being able to push again.

    Pull can work in two modes:

    1) Direct communication between servers: The destination server will fetch
       the data from the source server, automatically synchronizing data for
       the specified branch. It requires the user running the command to be 
       authenticated by the remote server, either using the default authentication
       in the client.conf file, or specifiying the '--^authmode' and '--^authdata'
       modifiers, or '--^authmode' and '--^user' and '--^password' if the authentication
       mode is ^UPWorkingMode.
    2) Import a previously generated package with push and the '--^package' option.
       This mode requires using a package file previously generated with the push
       command.

    Keep in mind that pull replication works in an indirect way. When executed,
    the command asks the destination repository to connect to the source and
    obtain the selected branch.

    However, this can be done directly by using the push command. This will make 
    the command replicate the selected branch from source to destination.

Examples:

    cm ^pull ^br:/main@project1@remoteserver:8084 projectx@myserver:8084
    (Pulls the 'main' branch from 'remoteserver' to 'myserver'. In this case,
    both servers are configured with the same authentication mode.)

    cm ^pull ^br:/main@project1@remoteserver:8084 projectx@myserver:8084 \
        --^authmode=^LDAPWorkingMode --^authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
    (Pulls the same branch as before, but now the remote server is configured
    to authenticate users with Active Directory. For instance, I am connecting
    from a Linux machine to a Windows server configured to use Active Directory
    integrated mode. I will specify my Active Directory user and cyphered
    password and pass it as LDAP to the server.)

    cm ^pull ^br:/main@project1@remoteserver:8084 projectx@myserver:8084 \
        --^authmode=^UPWorkingMode --^user=dave --^password=mysecret
    (Pulls the same branch, but now users are authenticated on the remote
    server, taking advantage of the user/password database included in
    Unity VCS.)

    cm ^pull ^br:/main@project1@remoteserver:8084 projectx@myserver:8084 --^nodata
    (Replicates the 'main' branch from 'remoteserver' to 'myserver' without data.)

    cm ^pull ^hydrate ^br:/main@projectx@myserver:8084 projectx@remoteserver:8084
    (Hydrates all the changesets in the 'main' branch obtaining the data from
    the remote server.)

    cm ^pull ^hydrate ^cs:122169@projectx@myserver:8084 projectx@remoteserver:8084
    (Hydrates changeset 122169 in 'myserver' obtaining the data from the remote 
    server.)

== CMD_DESCRIPTION_PUSH ==
Pushes a branch to another repo.

== CMD_USAGE_PUSH ==
Usage:

    cm ^push <src_br_spec> <dst_rep_spec>
            [--^preview] [TranslateOptions]
            [--^user=<usr_name> [--^password=<pwd>] | AuthOptions]
     (Direct server-to-server replication. Pushes a branch from a repository.)

    cm ^push <src_br_spec> --^package=<pack_file> [AuthOptions]
     (Package based replication. Creates a replication package in the source
     server with the selected branch.)

Options:

    --^preview       Gives information about what changes will be pushed,
                    but no changes are actually performed. This option is
                    useful to check the data that will be transferred before
                    replicating changes.
    --^package       Specifies path for exporting replication package for
                    package based replication. 
                    Useful to move data between servers when there is no 
                    direct network connection.
    --^user, --^password  Credentials to use if the authentication mode is
                    different in source and destination and there is not a
                    profile to authenticate to destination.
    src_br_spec      The branch to push to a remote repository.
                    (Use 'cm ^help ^objectspec' to learn more about branch specs.)
    dst_rep_spec     The destination repository.
                    (Use 'cm ^help ^objectspec' to learn more about repository
                    specs.)
    TranslateOptions  See the Translate options section for more information.
    AuthOptions       See the Authentication options section for more information.

Translate options:
    Mode:

    --^trmode=(^copy|^name|^table --^trtable=<translation_table_file>)
      The source and destination repositories may use different authentication
      modes. The '--^trmode' option specifies how to translate the user names from
      the source to the destination. The '--^trmode' must be one of the following
      values:
          ^copy    (Default). Means that the user identifiers will be just copied.
          ^name    The user identifiers will be matched by name.
          ^table   Uses a translation table specified in the option '--^trtable'
                  (see below).

    Specifics if mode is set to "table":

    --^trtable=<translation_table_file>
        If the translation mode is 'table', a translation table is a file
        containing lines in the form <oldname;newname> (one per line). When the
        branch is written to the destination repository, the objects created by
        a user identified by "oldname" in the source repository will be set
        to the user with "newname" on the destination.

Authentication options:

    Authentication data can be specified using one of the two following modes:
    - Using authentication parameters.
    - Using an authentication file.


    Using authentication parameters: 

        --^authmode=<mode> --^authdata=<data>

    Where --^authmode can take one of the following values 

    - ^NameWorkingMode
    - ^LDAPWorkingMode
    - ^ADWorkingMode
    - ^UPWorkingMode

        Examples:
        (^LDAPWorkingMode)  --^authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
        (^UPWorkingMode)    --^authdata=dave:fPBea2rPsQaagEW3pKNveA==

        The '--^authdata' line is the content of the <^SecurityConfig> entry
        in the client.conf file and the profiles.conf file. The profiles.conf
        file can be easily generated from the Unity VCS GUI in the replication
        profiles tab under Preferences.

        If you are using ^UPWorkingMode, you can simply specify:

        --^authmode=^UPWorkingMode --^user=<user> --^password=<psw>

    Using an authentication file:

    It's possible to have a different file for each server you connect to,
    with the credentials for that server and --^authfile=<authentication_file>
    where the authentication_file must be formed by the following 2 lines:

    1) mode, as described in '--^authmode'
    2) authentication data, as described in '--^authdata'

== CMD_HELP_PUSH ==
Remarks:

    The '^push' command is able to replicate branches (along with their
    changesets) between a source repository and a destination repository.
    The repositories can be located at different servers.

    There are two replication operations: '^push' and '^pull'.

    A '^push' operation means that the replication operation will send data
    from the source repository to the destination repository. In this case,
    the client will connect to the source repository, getting the data to
    replicate, and then it will send it to the destination repository. While
    the former (source) must have connectivity to the destination, the latter
    (destination) will not connect itself to the source.

    In a typical distributed scenario, a developer pushes data from his local
    server to the main server. Also, the developer might want to pull the latest
    repository updates from the main server, too.

    Replication can resolve situations where concurrent changes have
    been made on the same branch on two replicated repositories.

    - Push: If you try to push your data to a repository having newer changes
      than those you are sending, the system will ask you to pull the latest
      changes, resolve the merge operation and, finally, try to push again.
    - Pull: Whenever you pull changesets from a remote branch, they will be
      correctly linked to their parent changesets. If the changeset you pulled
      is not a child of the last changeset in the branch, then a multi-headed
      scenario will appear. The branch will have more than one 'head', or last
      changeset on the branch. You will need to merge the two 'heads' before
      being able to push again.

    Push can work in two modes:

    1) Direct communication between servers: The origin server will send
       the data to the destination server, automatically synchronizing data
       for the specified branch.
    2) Export package mode: The client will only connect to the source and
       generate a replication package obtaining both data and metadata for the
       specified branch. The '--^package' modifier will be used.

    Both modes require the user running the command to be authenticated
    by the server, either using the default authentication in the client.conf
    file, or specifiying the '--^authmode' and '--^authdata' modifiers.

    The ^push replication works in a direct way. When executed, the command
    will replicate the selected branch from source to destination, instead of
    asking the destination repository to connect to the source and obtain the
    selected branch (as the pull does).

Examples:

    cm ^push ^br:/main@project1@myserver:8084 projectx@remoteserver:8084
    (Replicates the 'main' branch from 'myserver' to 'remoteserver'. In this case,
    both servers are configured with the same authentication mode.)

    cm ^push ^br:/main@project1@myserver:8084 projectx@remoteserver:8084 \
        --^authmode=^LDAPWorkingMode --^authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
    (Replicates same branch as before, but now the remote server is configured
    to authenticate users with Active Directory. For instance, I am connecting
    from a Linux machine to a Windows server configured to use Active Directory
    integrated mode. I will specify my Active Directory user and cyphered
    password and pass it as LDAP to the server.)

    cm ^push ^br:/main@project1@myserver:8084 projectx@remoteserver:8084 \
        --^authmode=^UPWorkingMode --^user=dave --^password=mysecret
    (Replicates the same branch, but now users are authenticated on the remote
    server, taking advantage of the user/password database included in
    Unity VCS.)

== CMD_DESCRIPTION_CLONE ==
Clones a remote repository.

== CMD_USAGE_CLONE ==
Usage:

    cm ^clone <src_rep_spec> [<dst_rep_spec> | <dst_repserver_spec>]
             [--^user=<usr_name> [--^password=<pwd>] |
             [--^authmode=<mode> --^authdata=<data>] |
             [--^authfile=<authentication_file>]]
             [--^trmode=(^copy|^name|^table --^trtable=<translation_table_file>]
    (Direct repository-to-repository clone.)

    cm ^clone <src_rep_spec> --^package=<pack_file>
             [--^user=<usr_name> [--^password=<pwd>] |
             [--^authmode=<mode> --^authdata=<data>] |
             [--^authfile=<authentication_file>]]
             [--^trmode=(^copy|^name|^table --^trtable=<translation_table_file>]
    (Clones to an intermediate package, that can be imported later using a
    pull into the destination repository.)

Options:

    --^user, --^password  Credentials to use if the authentication mode is
                        different in source and destination and there is not a
                        profile to authenticate to destination.
    --^package           Exports the specified repository to a package file,
                        instead of a repository.
                        Useful for moving data between servers when there is no
                        direct network connection.
                        The resulting package must be imported using the
                        pull command.
    --^authmode         Describes the authentication method used in the server.
                        Admisible values: ^NameWorkingMode, ^LDAPWorkingMode,
                        ^ADWorkingMode, ^UPWorkingMode.
    --^authdata          Authentication information sent to the server. It holds
                        the content of the <^SecurityConfig> entry in the
                        client.conf file and the profiles.conf file. The 
                        profiles.conf file can be easily generated from the Unity
                        VCS in the connection profiles tab under Preferences.
    --^authfile         Allows to specify a path to a file with the credentials.
    --^trmode           The source and destination repositories may use different 
                        authentication modes. The '--^trmode' option specifies 
                        how to translate the user names from the source to the
                        destination. It must be one of the following values:
                        ^copy: (Default.) Means that the user identifiers will
                        be just copied; ^name: The user identifiers will be
                        matched by name; ^table: Uses a translation table
                        specified in the option '--^trtable'
    --^trtable          If the translation mode is 'table', a translation table
                        is a file containing lines in the form <oldname;newname>
                        (one per line). When the branch is written to the 
                        destination repository, the objects created by a user
                        identified by "oldname" in the source repository will
                        be set to the user with "newname" on the destination.
    src_rep_spec        Source repository of the clone operation.
                        (Use 'cm ^help ^objectspec' to learn more about repository
                        specs.)
    dst_rep_spec        Destination repository of the clone operation. If it
                        exists, it must be empty. If it does not exist, it will
                        be created.
                        If it is not specified, the command will use user's
                        default repository server.
                        (Use 'cm ^help ^objectspec' to learn more about repository
                        specs.)
    dst_repserver_spec  Destination repository server of the clone operation.
                        If there is a repository with the same name as
                        <src_rep_spec> in the destination repository server, it
                        must be empty. If there is not, it will be created.
                        If it is not specified, the command will use user's
                        default repository server.
                        (Use 'cm ^help ^objectspec' to learn more about repository
                        server specs.)
    translation_table_file  If the translation mode is 'table', a translation table
                        is a file containing lines in the form <oldname;newname>                        
                        (one per line). When the branch is written to the destination 
                        repository, the objects created by a user identified by 
                        "oldname" in the source repository will be set to the user 
                        with "newname" on the destination.
    authentication_file  Authentication file where you may have a different file
                        for each server you connect to, containing the credentials
                        for that server. The file format must comply with the
                        following structure: 2 lines, where the first line
                        describes the authentication mode, equally described as in
                        --^auth_mode; and the second line containing the data as
                        sdescribed in --^auth_data.

== CMD_HELP_CLONE ==
Remarks:

    The clone command can replicate branches (along with their changesets,
    labels, attributes, reviews, and so on) from a source repository to a
    destination repository. The repositories can be located at different servers.

    The destination repository can be created beforehand, but if it contains
    previous data, the clone operation will fail.

    The clone operation does NOT clone repository submodules, nor repositories
    under a Xlink.

Examples:

    cm ^clone awesomeProject@tardis@cloud
    (Clones 'awesomeProject' repository from 'tardis@cloud' organization into
    a local repository with the same name.)

    cm ^clone <EMAIL>:9095 repo-local
    (Clones 'repo' from 'server.home:9095' into 'repo-local' at user's default
    repository server.)

    cm ^clone project@192.168.111.130:8084 ^repserver:192.168.111.200:9095
    (Clones 'project' repository from '192.168.111.130:8084' into
    'project@192.168.111.200:9095'.)

    cm ^clone project@ldapserver:8084 --authmode=^LDAPWorkingMode \
        --^authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
    (Clones 'project' repository from 'ldapserver:8084' using LDAP
    authentication against the remote repository.)

    cm ^clone project@upserver:8084 --authmode=^UPWorkingMode \
        --^authdata=dave:fPBea2rPsQaagEW3pKNveA==
    (Clones 'project' repository from 'upserver:8084' using UPWorkingMode
    authentication against the remote repository.)

    cm ^clone project@upserver:8084 --authmode=^UPWorkingMode \
        --^user=<user> --^password=<pwd>
    (Clones 'project' repository from 'upserver:8084' using UPWorkingMode
    authentication against the remote repository but specifying the 
    ^user and ^password instead of the ^authdata.)

    cm ^clone project@ldapserver:8084 --authfile=credentials.txt \
        --^trmode=table --^trtable=table.txt
    (Clones 'project' repository from 'ldapserver:8084' using an authentication
    file against the remote repository, and translating users following the
    specified translation table.)

    cm ^clone <EMAIL>:9095 --^package=project.plasticpkg
    cm ^repository ^create <EMAIL>:8084
    cm ^pull --^package=project.plasticpkg <EMAIL>:8084
    (Clones 'project' repository from 'server.home:9095' into the package
    'project.plasticpkg', which is later imported through a pull into
    the 'project' repository at 'mordor.home:8084'.)

== CMD_DESCRIPTION_REVERT ==
Reverts an item to a previous revision.

== CMD_USAGE_REVERT ==
Usage:

    cm ^revert <revspec>

Options:

    revspec    Specification of the changeset that contains the
               revision which content will be loaded in the workspace.
               (Use 'cm ^help ^objectspec' to learn more about revision specs.)

== CMD_HELP_REVERT ==
Remarks:

    The item must be checked in.

Examples:

    cm ^revert dir#^cs:0
    cm ^revert C:\mywks\dir\file1.txt#23456

== CMD_DESCRIPTION_HISTORY ==
Displays the history of a file or directory.

== CMD_USAGE_HISTORY ==
Usage:

    cm ^history | ^hist <item_path>[ ...] [--^long | --^format=<str_format>]
                      [--^symlink] [--^xml[=<output_file>]] [--^encoding=<name>]
                      [--^moveddeleted]

Options:

    --^long              Shows additional information.
    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info. This option cannnot be combined
                        with '--^xml'.
    --^symlink           Applies the history operation to the symlink and not to
                        the target.
    --^xml               Prints the output in XML format to the standard output.
                        It is possible to specify an output file. This option
                        cannot be combined with '--^format'.
    --^encoding          Used with the '--^xml' option, specifies the encoding to
                        use in the XML output, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^moveddeleted      Include move and remove operations in the history.
    --^limit             Displays the N most recent revisions for the specified items, 
                        sorted by date and changeset id. If a negative number is supplied, 
                        it will return an empty list. If a number higher than the number 
                        of revisions is supplied, it will return all the available revisions 
                        regarding that item.
    item_path            Item's path. Use a whitespace to separate paths. Use
                        double quotes (" ") to specify paths containing spaces.
                        Paths can be server path revisions too.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_HISTORY ==
Remarks:

    This command shows a list of revisions for a given item, and label, branch,
    and comment info for each revision.

    Output format parameters (--^format option):
        This command accepts a format string to show the output.
        The output parameters of this command are the following:
        {0} | {^date}              Date.
        {1} | {^changesetid}       Changeset number.
        {2} | {^branch}            Branch.
        {4} | {^comment}           Comment.
        {5} | {^owner}             Owner.
        {6} | {^id}                Revision id.
        {7} | {^repository}        Repository.
        {8} | {^server}            Server.
        {9} | {^repspec}           Repository spec.
        {10} | {^datastatus}        Availability of the revision data.
        {11} | {^path}              Path or spec passed as <item_path>.
        {12} | {^itemid}            Item Id.
        {13} | {^size}              Size.
        {14} | {^hash}              Hash code.
        {^tab}                     Inserts a tab space.
        {^newline}                 Inserts a new line.

Examples:

    cm ^history file1.txt "file 2.txt"

    cm ^hist c:\workspace --^long
    (Displays all information.)

    cm ^history link --^symlink
    (Applies the history operation to the symlink file and not to the target.)

    cm ^history ^serverpath:/src/foo/bar.c#^br:/main/task001@myserver
    (Retrieves the revision history from a server path in a given branch.)

    cm ^history bar.c, foo.c --long --limit=2
    (Retrieves the 2 last revisions for the bar.c and foo.c items.)

== CMD_DESCRIPTION_REVISION_TREE ==
> **This command is deprecated.** Shows the revision tree for an item.

== CMD_USAGE_REVISION_TREE ==
Usage:

    cm ^tree <path> [--^symlink]

Options:

    --^symlink   Applies the operation to the link file and not to the target.
    path         Item path.

== CMD_HELP_REVISION_TREE ==
Examples:

    cm ^tree fichero1.txt
    cm ^tree c:\workspace
    cm ^tree link --^symlink
    (Applies the operation to the symlink file and not to the target.)

== CMD_DESCRIPTION_REMOVE ==
Allows the user to delete files and directories.

== CMD_USAGE_REMOVE ==
Usage:

    cm ^remove | ^rm <command> [options]

Commands:

    - ^controlled (optional)
    - ^private

    To get more information about each command run:
    cm ^remove <command> --^usage
    cm ^remove <command> --^help

== CMD_HELP_REMOVE ==
Examples:

    cm ^remove \path\controlled_file.txt
    cm ^remove ^private \path\private_file.txt

== CMD_DESCRIPTION_REMOVE_CONTROLLED ==
Deletes a file or directory from version control.

== CMD_USAGE_REMOVE_CONTROLLED ==
Usage:

    cm ^remove | ^rm <item_path>[ ...] [--^format=<str_format>]
                   [--^errorformat=<str_format>] [--^nodisk]

Options:

    --^format            Retrieves the output progress message in a specific
                        format. See the Examples for more information.
    --^errorformat       Retrieves the error message (if any) in a specific
                        format. See the Examples for more information.
    --^nodisk            Removes from version control, but keeps the item on
                        disk.
    item_path            Items path to remove. Use double quotes (" ") to specify
                        paths containing spaces. Use a whitespace to separate
                        paths.

== CMD_HELP_REMOVE_CONTROLLED ==
Remarks:

    Items are deleted from disk. Removed items are removed from the parent
    directory in the source code control.

    Requirements:
    - The item must be under source code control.

Reading input from stdin:

    The '^remove' command can read paths from stdin. To do this, pass a single
    dash "-".
    Example: cm ^remove -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify which files to remove.
    Example:
      dir /S /B *.c | cm ^remove -
      (In Windows, removes all .c files in the workspace.)

Examples:

    cm ^remove src
    (Removes 'src'. If src is a directory, this is the same as
    'cm ^remove -^R src'.)

    cm ^remove c:\workspace\file.txt --^format="{0} - REMOVED" \ 
        --^errorformat="{0} - ERROR REMOVING"
    (Removes 'file.txt' from version control and from disk, writing
    "c:\workspace\file.txt - ^REMOVED" if the operation succeeded, or
    "c:\workspace\file.txt - ^ERROR ^REMOVING" otherwise.)

    cm ^remove c:\workspace\file.txt --^nodisk
    (Removes 'file.txt' from version control, but keeps it on disk.)

== CMD_DESCRIPTION_REMOVE_PRIVATE ==
Deletes a private file or directory.

Warning: files deleted using the command are permanently erased, and are not
recoverable. It is recommended that you use the '--^dry-run' option to check
which files will be affected by the command.

== CMD_USAGE_REMOVE_PRIVATE ==
Usage:

    cm ^remove | ^rm ^private <path>[ ...] [-^R | -^r | --^recursive] [--^ignored]
                           [--^verbose] [--^dry-run]

Options:

    -^R | -^r | --^recursive  Recursively deletes private files from within controlled
                        directories.
    --^ignored           Deletes also ignored and cloaked files and directories.
    --^verbose           Prints all affected paths.
    --^dry-run           Runs the command without making any changes on disk.
    path                 Path of the files or directories to remove. Use double
                        quotes (" ") to specify paths containing spaces. Use a
                        whitespace to separate paths.

== CMD_HELP_REMOVE_PRIVATE ==
Remarks:

    If the path is a private file or directory, it will be deleted from disk.
    If the path is a controlled file, the command fails.
    If the path is a controlled directory, the command fails unless you
    specify the '-^r' option, in which case it will delete all private files and
    directories below the specified directory.

Examples:

    cm ^remove ^private private_directory
    (Deletes 'private_directory'.)

    cm ^remove ^private c:\workspace\controlled_directory
    (Fails, because 'controlled_directory' is not private.)

    cm ^remove ^private -^r c:\workspace\controlled_directory
    (Deletes all private files and directories below 'controlled_directory'.)

    cm ^rm ^private --^dry-run --^verbose c:\workspace\controlled_directory -^r
    (Shows all of the paths affected by the deletion of private files below
    'controlled_directory' without actually deleting anything.)

    cm ^rm ^private --^verbose c:\workspace\controlled_directory -^r
    (Shows all of the paths affected by the deletion of private files below
    'controlled_directory', performing the delete.)

== CMD_DESCRIPTION_TRIGGER_DELETE ==
Deletes a trigger.

== CMD_USAGE_TRIGGER_DELETE ==
Usage:

    cm ^trigger | ^tr ^delete | ^rm <subtype-type> <position_number>
                                [--^server=<repserverspec>]

Options:

    --^server            Deletes the trigger on the specified server.
                        If no server is specified, executes the command on the
                        one configured on the client.
    subtype-type        Trigger execution and trigger operation.
                        (Use 'cm ^showtriggertypes' to see a list of trigger
                        types.)
    position_number     Position assigned to the trigger when it was created.

== CMD_HELP_TRIGGER_DELETE ==
Examples:

    cm ^trigger ^delete ^after-setselector 4
    cm ^tr ^rm ^after-setselector 4

== CMD_DESCRIPTION_ATTRIBUTE_SET ==
Sets an attribute on a given object.

== CMD_USAGE_ATTRIBUTE_SET ==
Usage:

    cm ^attribute | ^att ^set <att_spec> <object_spec> <att_value>

    att_spec           Attribute specification. (Use 'cm ^help ^objectspec' to
                       learn more about attribute specs.)
    object_spec        Specification of the object to set the attribute on.
                       Attributes can be set on: branches, changesets,
                       shelvesets, labels, items, and revisions.
                       (Use 'cm ^help ^objectspec' to learn more about specs.)
    att_value          The attribute value to set to the object.

== CMD_HELP_ATTRIBUTE_SET ==
Remarks:

    An attribute can be set on an object to save additional information for
    this object.
    Attributes can be set on the following objects: branches, changesets,
    shelvesets, labels, items, and revisions.

Examples:

    cm ^attribute ^set ^att:status ^br:/main/SCM105 open
    (Sets attribute 'status' to branch 'SCM105' with value 'open'.)

    cm ^att ^set ^att:integrated@reptest@server2:8084 ^lb:LB008@reptest@server2:8084 yes
    (Sets attribute 'integrated' to label 'LB008' in repository 'reptest' with
    value 'yes'.)

== CMD_DESCRIPTION_SETOWNER ==
Sets the owner of an object.

== CMD_USAGE_SETOWNER ==
Usage:

    cm ^setowner | ^sto --^user=<usr_name> | --^group=<group> <object_spec>

Options:

    --^user              User name. New owner of the object.
    --^group             Group name. New owner of the object.
    object_spec         Specification of the object to set the new owner on.
                        The owner can be set on the following objects:
                        repository server, repository, branch, changeset,
                        label, item, revision and attribute.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_SETOWNER ==
Remarks:

    The owner of an object can be a user or a group.

    The owner can be set on the following objects: repository server,
    repository, branch, changeset, label, item, revision, and attribute.

Examples:

    cm ^setowner --^user=john ^repserver:localhost:8084
    (Sets 'john' as repository server owner.)

    cm ^sto --^group=development ^rep:mainRep@PlasticServer:8084
    (Sets 'development' group as owner of 'mainRep' repository.)

== CMD_DESCRIPTION_SETSELECTOR ==
> **This command is deprecated.** Sets the selector to a workspace.

== CMD_USAGE_SETSELECTOR ==
Selectors are discontinued and its use is not recommended.

Usage:
    cm ^setselector | ^sts [--^file=<selector_file>] [--^ignorechanges]
                           [--^forcedetailedprogress] [<wk_path> | <wk_spec>]
                           [--^noinput]

Options:

    --^file                   File to load a selector from.
    --^ignorechanges          Ignores the pending changes warning message that is
                             shown if there are pending changes detected when
                             updating the workspace.
    --^forcedetailedprogress  Forces detailed progress even when standard output
                             is redirected.
    --^noinput                Skips the interactive questions to continue the
                             operation with pending changes or to shelve them.
    wk_path                  Path of the workspace to set the selector.
    wk_spec                  Workspace specification. (Use 'cm ^help ^objectspec'
                             to learn more about workspace specs.)

== CMD_HELP_SETSELECTOR ==
Remarks:

    This command sets the selector of a workspace.

    A workspace needs information to load revisions from the repository.
    To get this information, Unity VCS uses a selector.

    Using a selector, it is possible to load revisions from a given branch,
    label, or changeset.

    If a file to load the selector is not specified, the default Operating
    System editor will be executed.

    Sample selector:

  - ^repository "^default" // working repository
      - ^path "/" // rules will be applied to the root directory
          - ^branch "/^main" // obtain latest revisions from ^br:/^main
          - ^checkout "/^main" // place checkouts on branch ^br:/^main

Examples:

    cm ^sts
    (Opens the current selector file to be applied.)

    cm ^sts ^wk:workspace_projA@reptest
    (Opens the specified selector file to be applied.)

    cm ^setselector --^file=c:\selectors\sel.xml
    (Sets the specified selector file in the current workspace.)

    cm ^setselector --^file=c:\selectors\sel.xml ^wk:MyWorkspace
    (Sets the specified selector file in the selected workspace.)

== CMD_DESCRIPTION_SHELVE ==
> **This command is deprecated.** Use cm ^shelveset instead.

Shelves the contents of checked-out items.

== CMD_USAGE_SHELVE ==
Usage:

    cm ^shelve [<item_path>+] [--^all] [--^dependencies] 
              [-^c=str_comment | -^commentsfile=<comments_file>]
              [--^encoding=name] [--^comparisonmethod=comp_method]
              [--^summaryformat]
    (Shelves the contents.)

    cm ^shelve --^apply=<sh_spec> [--^mount]
    (Applies a stored shelveset.)

    cm ^shelve --^delete=<sh_spec>
    (Removes a stored shelveset.)

Options:

    item_path           Items to be shelved, separated by spaces. Quotes (") can
                        be used to specify paths containing spaces.
    --^all               The items changed, moved and deleted locally, on the
                        given paths, will also be included.
    --^dependencies      Includes local change dependencies in the items to
                        shelve.
    -^c                  Applies the specified comment to the created shelveset.
    -^commentsfile       Applies the comment in the specified file to the created
                        shelveset.
    --^encoding          Specifies the output encoding, i.e.: utf-8.
                        See the MSDN documentation at
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        to get the table of supported encodings and its format, 
                        (at the end of the page, in the "Name" column).
    --^comparisonmethod  Sets the comparison method. See remarks for more info.
    --^summaryformat     Just prints the shelveset spec created for the main repo,
                        skipping shelvesets of xlinked repos and any other output
                        messages. For automation purposes.
    --^mount             The mount point for the given repository.
    --^delete            Removes the specified shelveset.
                        Shelveset specification: check 'cm ^help ^objectspec'.
    --^apply             Restores the shelved contents of the specified shelveset.
                        Shelve specification: check 'cm ^help ^objectspec'.

== CMD_HELP_SHELVE ==

Remarks:

    If neither <item_path> nor any option is specified, the shelve will involve
    all the pending changes in the workspace.

    The shelve operation is always applied recursively from the given path.

    Requirements to shelve an item:
    - The item must be under source code control.
    - The item must be checked out or changed (--^all option must be used).

    Comparison methods:
        ^ignoreeol                Ignores end of line differences.
        ^ignorewhitespaces        Ignores whitespace differences.
        ^ignoreeolandwhitespaces  Ignores end of line and whitespace differences.
        ^recognizeall             Detects end of line and whitespace differences.

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Examples:

    cm ^shelve -^c="my comment"
    (Shelves all the pending changes in the current workspace including a
    comment.)

    cm ^shelve file1.txt "file 2.txt" -^commentsfile=commentshelve.txt
    (Shelves the selected pending changes and applies the comment in the 
    commentshelve.txt file.)

    cm ^shelve --^apply=^sh:3
    (Applies a stored shelveset.)

    cm ^shelve --^delete=^sh:3
    (Removes a stored shelveset.)

    cm ^status --^short --^changelist=pending_to_review | cm ^shelve -
    (Shelves client changelist.
    The command above lists the paths in the changelist named
    'pending_to_review' and the path list is redirected to the input of the
    shelve command.)

== CMD_DESCRIPTION_SHELVESET ==
Allows the user to manage shelvesets.

== CMD_USAGE_SHELVESET ==
Usage:

    cm ^shelveset <command> [options]

Commands:

    - ^create | ^mk
    - ^delete | ^rm
    - ^apply

    To get more information about each command run:
    cm ^shelveset <command> --^usage
    cm ^shelveset <command> --^help

== CMD_HELP_SHELVESET ==
Examples:

    cm ^shelveset ^create -^c="my comment"
    cm ^shelveset ^delete ^sh:3
    cm ^shelve ^apply ^sh:3

== CMD_DESCRIPTION_SHELVESET_CREATE ==
Shelves pending changes.

== CMD_USAGE_SHELVESET_CREATE ==
Usage:

    cm ^shelveset ^create | ^mk [<item_path>[ ...]] [--^all] [--^dependencies]
                             [-^c=<str_comment> | -^commentsfile=<comments_file>]
                             [--^summaryformat]

Options:

    item_path           Items to shelve. Use a whitespace to separate user names.
                        Use double quotes (" ") to specify paths containing
                        spaces.
    --^all               The items changed, moved, and deleted locally, on the
                        given paths, will also be included.
    --^dependencies      Includes local change dependencies into the items to
                        shelve.
    --^summaryformat     Just prints the shelveset spec created for the main repo,
                        skipping shelvesets of xlinked repos and any other output
                        messages. For automation purposes.
    -^c                  Applies the specified comment to the created shelve.
    -^commentsfile       Applies the comment in the specified file to the created
                        shelve.

== CMD_HELP_SHELVESET_CREATE ==
The '^shelveset ^create' command stores the contents of checked out items inside the
    repository. This way the contents are protected without the need to
    checkin the files.

Remarks:

    If neither <item_path> nor any option is specified, the shelveset will
    include all the pending changes in the workspace.

    The '^shelveset ^create' operation is always applied recursively from the
    given path.

    Requirements to shelve an item:
    - The item must be under source code control.
    - The item must be checked out or changed ('--^all' option must be used).

    Set the PLASTICEDITOR environment variable to specify an editor for
    entering comments. If the PLASTICEDITOR environment variable is set, and
    the comment is empty, the editor will be automatically launched to allow
    you to specify the comment.

Examples:

    cm ^shelveset ^create -^c="my comment"
    (Shelves all the pending changes in the current workspace including a
    comment.)

    cm ^shelveset file1.txt "file 2.txt" -^commentsfile=commentshelve.txt
    (Shelves the selected pending changes and applies the comment in the 
    'commentshelve.txt' file. Note, '^create' is the default subcommand.)

    cm ^status --^short --^changelist=pending_to_review | cm ^shelveset -
    (Shelves client changelist.
    The command above lists the paths in the changelist named
    'pending_to_review' and the path list is redirected to the input of the
    '^shelveset' command.)

== CMD_DESCRIPTION_SHELVESET_DELETE ==
Deletes a shelveset.

== CMD_USAGE_SHELVESET_DELETE ==
Usage:

    cm ^shelveset ^delete | ^rm <sh_spec>
    
    sh_spec             Shelveset specification. (Use 'cm ^help ^objectspec' to
                        learn more about shelveset specs.)

== CMD_HELP_SHELVESET_DELETE ==
The '^shelveset ^delete' command deletes a shelveset.

Examples:

    cm ^shelveset ^delete ^sh:3
    (Removes a stored shelveset.)

== CMD_DESCRIPTION_SHELVESET_APPLY ==
Applies a stored shelveset.

== CMD_USAGE_SHELVESET_APPLY ==
Usage:

    cm ^shelveset ^apply <sh_spec> [<change_path>[ ...]] [--^preview] 
                       [--^mount] [--^encoding=<name>]
                       [--^comparisonmethod=(^ignoreeol | ^ignorewhitespaces| \
                                            ^ignoreeolandwhitespaces | ^recognizeall)]

Options:

    --^preview           Prints the changes to apply on the workspace without
                         applying them
    --^mount             The mount point for the given repository.
    --^encoding          Specifies the output encoding, i.e.: utf-8.
                         See the MSDN documentation at
                         http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                         to get the table of supported encodings and its format, 
                         (at the end of the page, in the "Name" column).
    --^comparisonmethod  Sets the comparison method. See Remarks for more info.
    sh_spec              Shelveset specification. (Use 'cm ^help ^objectspec' to
                         learn more about shelveset specs.)
    change_path          The change path(s) of the shelve to apply. It's a 
                         server path, the one printed by the --preview option. 
                         When no path is set, all changes will be applied.

== CMD_HELP_SHELVESET_APPLY ==
The '^shelveset ^apply' command restores the contents of a stored shelveset.

Remarks:

    Comparison methods:
        ^ignoreeol                Ignores end of line differences.
        ^ignorewhitespaces        Ignores whitespace differences.
        ^ignoreeolandwhitespaces  Ignores end of line and whitespace differences.
        ^recognizeall             Detects end of line and whitespace differences.

Examples:

    cm ^shelveset ^apply ^sh:3
    (Applies a stored shelve.)

    cm ^shelveset ^apply ^sh:3 /src/foo.c
    (Applies only the /src/foo.c change stored on the shelve.)

== CMD_DESCRIPTION_SHOW_FIND_OBJECTS ==
Lists objects and attributes.

== CMD_USAGE_SHOW_FIND_OBJECTS ==
Usage:

    cm ^showfindobjects <object>

Options:

    ^object    Check remarks for available objects and attributes.

== CMD_HELP_SHOW_FIND_OBJECTS ==
Remarks:

    Available objects and attributes:

    - ^attribute.
    - ^attributetype.
    - ^branch.
    - ^changeset.
    - ^label.
    - ^merge.
    - ^replicationlog.
    - ^review.
    - ^revision.
    - ^shelve.

^attribute:
    You can find attributes by filtering using the following fields:

    ^type    string.
    ^value   string.
    ^date    date. Check "date constants" for more info in this guide.
    ^owner   user. Admits special user '^me'.
    ^GUID    Global Unique Identifier.
              Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^comment string.
    ^srcobj  object spec: item path, branch, changeset, revision, or label.
              Use 'cm ^help ^objectspec' to learn how to specify these objects.
    ^ID      integer.

    Examples:

    cm ^find ^attribute "^where ^type = 'status'"
    (Finds all attributes of type 'status'.)

    cm ^find ^attribute "^where ^date > '^this ^week'"
    (Finds all attributes applied during the current week.)

    cm ^find ^attribute "^where ^value = 'resolved' ^and ^owner = '^me'"
    (Finds all attributes with value 'resolved' applied by me.)

    cm ^find ^attribute "^where ^srcobj = '^item:readme.txt'"
    (Finds the attributes applied to the item 'readme.txt'.)

    cm ^find ^attribute "^where ^srcobj = '^br:/main/scm23343'"
    (Finds the attributes applied to the branch scm23343.)

    cm ^find ^attribute "^where ^srcobj = '^rev:readme.txt#^br:/main/task002'"
    (Finds the attributes applied to the specified revision.)

    cm ^find ^attribute "^where ^srcobj = '^rev:^revid:1126'"
    (Finds the attributes applied to the specified revision id.)

^attributetype:
    You can find attribute types by filtering using the following fields:

    ^name     string.
    ^value    string.
    ^date     date. Check "date constants" for more info in this guide.
    ^owner    user. Admits special user '^me'.
    ^GUID     Global Unique Identifier.
              Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^comment  string.
    ^source   object spec: item path, branch, changeset or label.
              Use 'cm ^help ^objectspec' to learn how to specify these objects.
    ^ID       integer.
    Replication field  Check "replication related fields" below.

    Replication related fields:
    - ^ReplLogId
    - ^ReplSrcDate
    - ^ReplSrcId
    - ^ReplSrcRepository
    - ^ReplSrcServer

    Examples:

    cm ^find ^attributetype "^where ^name ^like 'st%'"
    (Finds all attribute where name starts with 'st'.)

    cm ^find ^attribute "^where ^date > '^today'"
    (Finds all attributes applied today.)

    cm ^find ^attributetype "^where ^comment != ''" --^xml
    (Finds all attribute types that have a comment and prints the 
    output in XML format to the standard output.)

    cm ^find ^attributetype "^where ^source = '^item:readme.txt'"
    (Finds all attribute types in item 'readme.txt'.)

    cm ^find ^attributetype "^where ^source = '^cs:30'"
    (Finds all attribute types in changeset '30'.)

    cm ^find ^attributetype "^where ^source = '^lb:v0.14.1'"
    (Finds all attribute types in label 'v0.14.1'.)

^branch:
    You can find branches by filtering using the following fields:

    ^name        string.
    ^date        date. Check "date constants" for more info in this guide.
    ^changesets  date (of the changesets in the branch). Check "date constants"
                  for more info in this guide.
    ^attribute   string.
    ^attrvalue   string.
    ^owner       user. Admits special user '^me'.
    ^parent      branch spec.
                 Use 'cm ^help ^objectspec' to learn how to specify this object.
    ^comment    string.
    ^hidden     boolean. A way to show the hidden branches.
    ^GUID       Global Unique Identifier.
                 Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^ID         integer.
    ^order ^by  field to use for sorting. Check "sorting fields" for more info.
    Replication fields  Check "replication related fields" below.

    Sorting fields:
    - ^date
    - ^branchname

    Replication related fields:
    - ^ReplLogId
    - ^ReplSrcDate
    - ^ReplSrcId
    - ^ReplSrcRepository
    - ^ReplSrcServer

    Examples:
    cm ^find ^branch "^where ^name ^like 'scm23%'"
    (Finds branches which name starts with 'scm23'.)

    cm ^find ^branch "^where ^date > '^one ^week ^ago'"
    (Finds branches created during the last week.)

    cm ^find ^branch "^where ^changesets >= '^today'"
    (Finds branches with changesets created today.)

    cm ^find ^branch "^where ^attribute = 'status' ^and ^attrvalue = 'failed'"
    (Finds branches that have the attribute 'status' and which 
    value is 'failed'.)

     cm ^find ^branch "^where ^owner != '^me' ^and ^parent != '^br:/main'"
     (Finds branches created by other than me and which parent 
     branch is not '/main'.)

    cm ^find ^branch "^where ^id = 2029607"
    (Finds the branch which id is 2029607.)

    cm ^find ^branch "^where ^hidden = 'true'"
    (Finds the hidden branches.)

^changeset:
    You can find changesets by filtering using the following fields:

    ^branch             branch spec. Use 'cm ^help ^objectspec' to learn how to
                        specify this object.
    ^changesetid        integer.
    ^attribute          string.
    ^attrvalue          string.
    ^date               date. Check "date constants" for more info in this guide.
    ^owner              user. Admits special user '^me'.
    ^GUID               Global Unique Identifier.
                        Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^comment            string.
    ^ignorehidden       boolean. A way to return also the changesets from hidden branches.
    ^onlywithrevisions  boolean. To filter whether a cset has revisions or not.
    ^returnparent       boolean. A way to return the parent of a cset. Good for scripting.
    ^parent             changeset id (integer).
    ^ID                 integer.
    ^order ^by          field to use for sorting. Check "sorting fields" for more info.
    Replication fields  Check "replication related fields" below.

    Sorting fields:
    - ^date
    - ^changesetid

    Replication related fields:
    - ^ReplLogId
    - ^ReplSrcDate
    - ^ReplSrcId
    - ^ReplSrcRepository
    - ^ReplSrcServer

    Examples:

    cm ^find ^changeset "^where ^branch = '/main/scm23119'"
    (Finds all changesets in branch 'scm23119'.)

    cm ^find ^changeset "^where ^attribute = 'status'"
    (Finds the changesets with the attribute 'status'.)

    cm ^find ^changeset "^where ^date >= '6/8/2018' ^and ^owner != '^me'"
    (Finds all changesets with creation date equal or
    greater than 6/8/2018 and created by others than me.)

    cm ^find ^changeset "^where ^guid = '1b30674f-14cc-4fd7-962b-676c8a6f5cb6'"
    (Finds the changeset with the specified guid.)

    cm ^find ^changeset "^where ^comment = ''"
    (Finds the changesets with no comments.)

    cm ^find ^changeset "^where ^onlywithrevisions = 'false'"
    (Finds changesets with no revisions.)

    cm ^find ^changeset "^where ^changesetid = 29 ^and ^returnparent = 'true'"
    (Finds the parent of changeset 29.)

    cm ^find ^changeset "^where ^parent = 548"
    (Finds all changesets which parent is cset 548.)

^label:
    You can find labels by filtering using the following fields:

    ^name       string.
    ^attribute  string.
    ^attrvalue  string.
    ^date       date. Check "date constants" for more info in this guide.
    ^owner      user. Admits special user '^me'.
    ^GUID       Global Unique Identifier.
                Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^branch     branch spec. Use 'cm ^help ^objectspec' to learn how to
                specify this object.
    ^branchid   integer.
    ^changeset  changeset id (integer).
    ^comment    string.
    ^ID         integer.
    ^order ^by  field to use for sorting. Check "sorting fields" for more info.
    Replication fields  Check "replication related fields" below.

    Sorting fields:
    - ^date
    - ^labelname

    Replication related fields:
    - ^ReplLogId
    - ^ReplSrcDate
    - ^ReplSrcId
    - ^ReplSrcRepository
    - ^ReplSrcServer

    Examples:

    cm ^find ^label "^where ^name ^like '7.0.16.%'"
    (Finds the labels with a name that starts with '7.0.16.'.)

    cm ^find ^label "^where ^date >= '^this ^month' ^and \
        ^attribute = 'publish-status' ^and ^attrvalue != 'PUBLISHED'"
    (Finds the labels created this month with an attribute 'publish-status'
    set to a value other than 'PUBLISHED'.)

    cm ^find ^label "^where ^branch = '/main'"
    (Finds all labels applied to the main branch.)

    cm ^find ^label "^where ^changeset = 111733"
    (Finds the labels applied to changeset 111733.)

^merge:
    You can find merges by filtering using the following fields:

    ^srcbranch     branch spec.
                   Use 'cm ^help ^objectspec' to learn how to specify this object.
    ^srcchangeset  changeset id (integer).
    ^dstbranch     branch spec. Use 'cm ^help ^objectspec' to learn how to
                   specify this object.
    ^dstchangeset  changeset id (integer).
    ^date          date. Check "date constants" for more info in this guide.
    ^owner         user. Admits special user '^me'.
    ^GUID          Global Unique Identifier.
                   Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^type          string. Check possible values in "Type values" below.
    ^ID            integer.

    ^Type values:
    - ^merge
    - ^cherrypick
    - ^cherrypicksubstractive
    - ^interval
    - ^intervalcherrypick
    - ^intervalcherrypicksubstractive

    Examples:

    cm ^find ^merge "^where ^srcbranch = '^br:/main'"
    (Finds merges from the main branch.)

    cm ^find ^merge "^where ^dstchangeset = 108261" \
        --^format="{^srcbranch} {^srcchangeset} {^dstbranch} {^dstchangeset} {^owner}"
    (Finds the merges to changeset 108261 and prints the
    formatted output showing the source (branch and cset id),
    the destination (branch and cset id), and the merge owner.)

    cm ^find ^merge "^where ^type = '^cherrypick' ^and ^owner = '^me'"
    (Finds all my cherry picks.)

^replicationlog:
    You can find replication log by filtering using the following fields:

    ^branch          branch spec. Use 'cm ^help ^objectspec' to learn how to
                     specify this object.
    ^repositoryname  string.
    ^owner           user. Admits special user '^me'.
    ^date            date. Check "date constants" for more info in this guide.
    ^server          string.
    ^package         boolean.
    ^ID              integer.

    Examples:

    cm ^find ^replicationlog "^where ^branch = '/main/gm22358'"
    (Finds the replication logs of branch 'gm22358'.)

    cm ^find ^replicationlog "^where ^package = 'T' ^and ^server ^like '%cloud%'"
    (Finds the replication logs created from package which
    server name contains 'cloud'.)

^review:
    You can find code reviews by filtering using the following fields:

    ^status      string.
    ^assignee    string.
    ^title       string.
    ^target      object spec: branch or changeset.
                 Use 'cm ^help ^objectspec' to learn how to specify this object.
    ^targetid    integer.
    ^targettype  string. Check "target types" for more info.
    ^date        date. Check "date constants" for more info in this guide.
    ^owner       user. Admits special user '^me'.
    ^GUID        Global Unique Identifier.
                 Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^ID          integer.
    ^order ^by   field to use for sorting. Check "sorting fields" for more info.

    Target types:
    - ^branch
    - ^changeset

    Sorting fields:
    - ^date
    - ^modifieddate
    - ^status

    Examples:

    cm ^find ^review "^where ^status = 'pending' ^and ^assignee = '^me'"
    (Finds all my pending reviews.)

    cm ^find ^review "^where ^target = '^br:/main/scm17932'"
    (Finds the reviews related to branch 'scm17932'.)

    cm ^find ^review "^where ^targettype = '^changeset'"
    (Finds the reviews which target type is changeset.)

^revision:
    You can find revisions by filtering using the following fields:

    ^branch               branch spec. Use 'cm ^help ^objectspec' to learn how to
                          specify this object.
    ^changeset            changeset id (integer).
    ^item                 string or integer.
    ^itemid               integer.
    ^attribute            string.
    ^attrvalue            string.
    ^archived             boolean.
    ^comment              string.
    ^date                 date. Check "date constants" for more info in this guide.
    ^GUID                 Global Unique Identifier.
                          Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^owner                user. Admits special user '^me'.
    ^parent               revision id (integer).
    ^returnparent         boolean.
    ^shelve               shelve id (integer).
    ^size                 integer (in bytes).
    ^type                 string. Check "type values" for more info.
    ^workspacecheckoutid  integer.
    ^ID                   integer.
    Replication field     Check "replication related fields" below.

    ^type values:
    - ^dir
    - ^bin
    - ^txt

    Replication related fields:
    - ^ReplLogId
    - ^ReplSrcDate
    - ^ReplSrcId
    - ^ReplSrcRepository
    - ^ReplSrcServer

    Examples:

    cm ^find ^revision "^where ^changeset >= 111756"
    (Finds the revisions created in changeset 111756 
    and later.)

    cm ^find ^revision "^where ^item = 'readme.txt' ^or ^itemid = 2250"
    (Finds the revisions of item 'readme.txt' plus 
    item id 2250.)

    cm ^find ^revision "^where ^item = 'readme.txt' ^or ^item = 2250"
    (Gets the same revisions as the previous example.)

    cm ^find ^revision "^where ^attribute = 'status' ^and ^attrvalue != 'open'"
    (Finds the revisions with attribute 'status' which
    value is other than 'open'.)

    cm ^find ^revision "^where ^archived = 'true'"
    (Finds the revisions that are archived in an 
    external storage.)

    cm ^find ^revision "^where ^type = '^txt' and \
        ^size > 300000 ^and ^owner = '^me' and ^date >= '2 ^months ^ago'"
    (Finds the text revisions created by me two months
    ago and with size greater than about 3MB.)

^shelve:
    You can find shelves by filtering using the following fields:

    ^owner      user. Admits special user '^me'.
    ^date       date. Check "date constants" for more info in this guide.
    ^attribute  string.
    ^attrvalue  string.
    ^comment    string.
    ^GUID       Global Unique Identifier.
                Hexadecimal id in the format xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    ^parent     integer.
    ^shelveid   integer.
    ^ID         integer.
    Replication field     Check "replication related fields" below.

    Replication related fields:
    - ^ReplLogId
    - ^ReplSrcDate
    - ^ReplSrcId
    - ^ReplSrcRepository
    - ^ReplSrcServer

    Examples:

    cm ^find ^shelve "^where ^owner != '^me' ^and ^date >= '^1 ^years ^ago'"
    (Finds the shelves created by others than me during the last
    year.)

    cm ^find ^shelve "^where ^shelveid = 2"
    (Finds the shelve with name 2.)

    cm ^find ^shelve "^where ^id >= 3848"
    (Finds the shelves which object id is greater than 3848.)

Replication related fields:
    Many objects track replication data, meaning Unity VCS tracks where they were
    originally created.

    The fields you can use are:
    ^ReplSrcServer       repspec. Stands for "replication source server". 
                         Server where the object was replicated from.
     ^ReplSrcRepository  string. Stands for "replication source repo". It is
                         the repository where the object was replicated from.
     ^ReplLogId          integer. ID of the replication operation. In Unity VCS,
                         each time new objects are created from a replica, 
                         a new 'replicationlog' is created.
     ^ReplSrcDate        date. It is the date when the replica actually took 
                         place.
                         Replicated objects will retain its original creation
                         date, o this field is useful if you want to find 
                         objects that where replicated within a specific 
                         timeframe.
     ^ReplSrcId          integer. It is the ID of the replication source server. 
                         You can discover this ID searching for 
                         '^replicationsource' objects with the 'cm ^find' command.

    Examples:

    cm ^find ^branch "^where ^replsrcserver='skull.codicefactory.com:9095'"
    (Finds the branches replicated from server 'skull'.)

    cm ^find ^branch "^where ^replsrcserver = 'skull.codicefactory.com:9095' \
        ^and ^replsrcrepository = 'codice'"
    (Finds the branches replicated from server 'skull'
    and from repository 'codice'.)

    cm ^find ^revision "^where ^repllogid = 2019974"
    (Finds the revisions replicated from replica 2019974.)

    cm ^find ^label "^where ^replsrcdate >= '^one ^month ^ago' \
        ^and ^date >= '15 ^days ^ago'"
    (Finds the labels created 15 days ago and were 
    replicated one month ago.)

    cm ^find ^replicationlog "^where ^date > '^one ^week ^ago'"
    (returns the following line:)
    - 8780433 27/09/2018 8:49:38 codice@BACKYARD:8087 F mbarriosc

    (Finds the replication logs created one week ago. Now, you can
    check that the replicated branch was created before it was 
    replicated over:)

    cm ^find ^branch "^where ^repllogid = 8780433"
    (returns the following line:)
    - 8780443 26/09/2018 12:20:55 /main/scm23078 maria codice T

    cm ^find ^replicationsource
    (returns the following two lines:) 
    - 7860739 codice@AFRODITA:8087 d9c4372a-dc55-4fdc-ad3d-baeb2e975f27
    - 8175854 codice@BACKYARD:8087 66700d3a-036b-4b9a-a26f-adfc336b14f9

    (Now, you can find the changesets replicated from codice@AFRODITA:8087:)
    cm ^find ^changesets "^where ^replsrcid = 7860739"

Date constants:
    You can use date formats that follow your machine localization settings.
    For example, if your computer displays dates in the format 'MM-dd-yyyy',
    you can use dates such as '12-31-2019' in your queries.

    You can also use the following constants to simplify your queries:
    '^today'            today's date.
    '^yesterday'        yesterday's date.
    '^this ^week'       current week's Monday date.
    '^this ^month'      current month's 1st day date.
    '^this ^year'       current year's January 1st date.
    '^one ^day ^ago'    one day before the current date.
    '^one ^week ^ago'   seven days before the current date.
    '^one ^month ^ago'  one month before the current date.
    'n ^days ^ago'      'n' days before the current date.
    'n ^months ^ago'    'n' months before the current date.
    'n ^years ^ago'     'n' years before the current date.

    The following '^where' clauses are valid for fields of type '^date':
    - '(...) ^where ^date > '^today' (...)'
    - '(...) ^where ^date < '^yesterday' (...)'
    - '(...) ^where ^date > '^this ^week' (...)'
    - '(...) ^where ^date > '^this ^month' (...)'
    - '(...) ^where ^date < '^one ^day ^ago' ^and ^date > '3 ^days ^ago' (...)'
    - '(...) ^where ^date < '^one ^week ^ago' ^and ^date > '3 ^weeks ^ago' (...)'
    - '(...) ^where ^date < '^one ^month ^ago' ^and ^date > '3 ^months ^ago' (...)'
    - '(...) ^where ^date > '1 ^year ^ago' (...)'

    You can also force a specific date format on the 'cm ^find' command using the
    --^dateformat flag. Check 'cm ^find --^help' for further details.

== CMD_DESCRIPTION_TRIGGER_SHOWTYPES ==
Displays available trigger types.

== CMD_USAGE_TRIGGER_SHOWTYPES ==
Usage:

    cm ^trigger ^showtypes

== CMD_HELP_TRIGGER_SHOWTYPES ==
Displays the list of available trigger types.

Examples:

    cm ^trigger ^showtypes

== CMD_DESCRIPTION_SHOWACL ==
Shows the ACL of an object.

== CMD_USAGE_SHOWACL ==
Usage:

    cm ^showacl | ^sa <object_spec> [--^extended] [--^xml[=<output_file>]]
                                [--^encoding=<name>]

Options:

    --^extended    Shows ACL hierarchy tree.
    --^xml         Prints the output in XML format to the standard output.
                  It is possible to specify an output file.
    --^encoding    Used with the '--^xml' option, specifies the encoding to
                  use in the XML output, i.e.: utf-8.
                  See the MSDN documentation at
                  http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                  to get the table of supported encodings and its format, 
                  (at the end of the page, in the "Name" column).
    object_spec    Specification of the object to show the ACL of.
                  The valid objects for this command are:
                  repserver, repository, branch, changeset, label, item,
                  and attribute. 
                  (Use 'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_SHOWACL ==
Examples:

    cm ^showacl ^repserver:PlasticServer:8084
    (Shows the ACL of the selected server.)

    cm ^sa ^br:/main --^extended
    (Shows the ACL hierarchy tree of the selected branch specification.)

== CMD_DESCRIPTION_SHOWCOMMANDS ==
Shows all the available commands.

== CMD_USAGE_SHOWCOMMANDS ==
Usage:

    cm ^showcommands

== CMD_HELP_SHOWCOMMANDS ==
    Bear in mind that there might be deprecated commands not displayed here
    but which functionallity remains due to retrocompatibility reasons.

== CMD_DESCRIPTION_SHOWOWNER ==
Shows the owner of an object.

== CMD_USAGE_SHOWOWNER ==
Usage:

    cm ^showowner | ^so <object_spec>

Options:

    object_spec         Specification of the object to show the owner of.
                        The object must be one of the following:
                        repository server, repository, branch, changeset,
                        label, attribute, revision, and item.
                        (Use 'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_SHOWOWNER ==
Remarks:

    This command displays the owner of an object. The owner can be a user or
    a group. The owner can be modified with 'cm ^setowner' command.

Examples:

    cm ^showowner ^repserver:PlasticServer:8084
    (Shows the owner of the selected server.)

    cm ^so ^item:samples\
    (Shows the owner of the selected item specification.)

== CMD_DESCRIPTION_SHOWPERMISSIONS ==
Lists the available permissions.

== CMD_USAGE_SHOWPERMISSIONS ==
Usage:

      cm ^showpermissions | ^sp

== CMD_HELP_SHOWPERMISSIONS ==
Examples:

    cm ^showpermissions

== CMD_DESCRIPTION_SHOWSELECTOR ==
> **This command is deprecated.** Shows the workspace selector.

== CMD_USAGE_SHOWSELECTOR ==
Selectors are discontinued and its use is not recommended.

Usage:

    cm ^showselector | ^ss [<wk_path> | <wk_spec>]

Options:

    wk_path             Path of the workspace to show the selector.
    wk_spec             Workspace specification. (Use 'cm ^help ^objectspec' to
                        learn more about workspace specs.)

== CMD_HELP_SHOWSELECTOR ==
Remarks:

    If neither path nor workspace spec is specified, the command will take the
    current directory as the workspace path.

Examples:

    cm ^showselector c:\workspace
    (Shows the selector for the selected workspace path.)

    cm ^ss
    (Shows the selector for current workspace.)

    cm ^showselector > mySelector.txt
    (Writes into a file the selector for the current workspace.)

    cm ^showselector ^wk:mywk@reptest
    (Shows the selector for the workspace 'mywk' in the repository 'reptest'.)

== CMD_DESCRIPTION_SUPPORT ==
Allows the user to perform support related operations.

== CMD_USAGE_SUPPORT ==
Usage:

    cm ^support <command> [options]

Commands:

    - ^bundle

    To get more information about each command run:
    cm ^support <command> --^usage
    cm ^support <command> --^help

== CMD_HELP_SUPPORT ==
Examples:

    cm ^support
    cm ^support ^bundle
    cm ^support ^bundle c:\outputfile.zip

== CMD_DESCRIPTION_SUPPORT_BUNDLE ==
Creates a "support bundle" package with relevant logs.
You can attach the file while requesting help, asking for extra info, or
submitting a bug.

== CMD_USAGE_SUPPORT_BUNDLE ==
Usage:

    cm ^support ^bundle [<outputfile>]

Options:

    outputfile          Creates the "support bundle" package at the specified
                        location.

== CMD_HELP_SUPPORT_BUNDLE ==
Remarks:

This command allows users to create a "support bundle" package which can be
attached when requesting help, asking for extra info, or submitting a bug.
The user can optionally specify a location for the output file; otherwise, the
output file will be written to the temp directory.

Examples:

    cm ^support ^bundle
    (Creates "support bundle" in temp directory.)

    cm ^support ^bundle c:\outputfile.zip
    (Creates "support bundle" at the specified location.)

== CMD_DESCRIPTION_SWITCH ==
Switches the workspace to a branch, changeset, label, or shelveset.

== CMD_USAGE_SWITCH ==
Usage:

    cm ^switch (<brspec> | <csetspec> | <lbspec> | <shspec>)
              [--^workspace=<path>] [--^repository=<name>]
              [--^forcedetailedprogress]
              [--^silent] [--^verbose] [--^xml[=<output_file>]] [--^encoding=<name>]
              [--^forcedetailedprogress] [--^noinput]

Options:

    --^workspace              Path where the workspace is located.
    --^repository             Switches to the specified repository.
    --^forcedetailedprogress  Forces detailed progress even when standard
                              output is redirected.
    --^noinput                Skips the interactive questions to continue the
                              operation with pending changes or to shelve them.
                              Using this option disables the possibility of
                              bringing your pending changes.
    --^silent                 No output is shown unless an error happens.
    --^verbose                Shows additional information.
    --^xml                    Prints the output in XML format to the standard output.
                              It is possible to specify an output file.
                              Using this option disables the possibility of
                              bringing your pending changes.
    --^encoding               Used with the --^xml option, specifies the encoding to
                              use in the XML output, i.e.: utf-8.
                              See the MSDN documentation at
                              http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                              to get the table of supported encodings and its format,
                              (at the end of the page, in the "Name" column).
    brspec                    Target branch.
                              (Use 'cm ^help ^objectspec' to learn more about branch specs.)
    csetspec                  Target changeset.
                              (Use 'cm ^help ^objectspec' to learn more about changeset specs.)
    lbspec                    Target label.
                              (Use 'cm ^help ^objectspec' to learn more about label specs.)
    shspec                    Target shelveset.
                              (Use 'cm ^help ^objectspec' to learn more about shelveset specs.)

== CMD_HELP_SWITCH ==
Remarks:

    This command allows users to update the workspace tree to the contents
    of the specified object (branch, label, shelveset, or changeset).

Examples:

    cm ^switch ^br:/main
    cm ^switch ^lb:Rel1.1
    cm ^switch ^br:/main/scm002 --^repository=rep2
    cm ^switch ^cs:4375
    cm ^switch ^sh:2

== CMD_DESCRIPTION_SWITCH_TO_BRANCH ==
> **This command is deprecated.** Use cm ^switch instead. Sets a branch as the working branch. 

== CMD_USAGE_SWITCH_TO_BRANCH ==
Usage:

    cm ^switchtobranch [options] [branch_spec]

Options:

    --^label=name | --^changeset=number  load revisions from the specified
                                         label or changeset. One of these options
                                         is required if no branch_spec is given.
    --^changeset=cset                    Switch to the specified changeset.
    --^repository=rep                    Switch to the specified repository.
    --^workspace | -wk=path              path where the workspace is located.
    branch_spec                          Branch specification.

== CMD_HELP_SWITCH_TO_BRANCH ==
Remarks:

    This command allows users to work in a branch.
    If no branch_spec specified, a label or a changeset must be specified.
    If no repository is specified, the branch is set to the current repository.

Examples:

    cm ^switchtobranch ^br:/main
    cm ^switchtobranch ^br:/main/task001
    cm ^switchtobranch --^label=BL050
    (Read-only configuration. The command loads the contents of the labeled
    changeset.)


== CMD_DESCRIPTION_SYNC ==
Synchronize with Git.

== CMD_USAGE_SYNC ==
Usage:

    cm ^synchronize | ^sync <repspec> ^git [<url> [--^user=<usr_name> --^pwd=<pwd>]]
                          [(--^txtsimilaritypercent | --^binsimilaritypercent | \
                            --^dirsimilaritypercent)=<value>]
                          [--^author] [--^skipgitlfs] [--^gitpushchunk=<value>]

Options:

    --^user                    User name for the specified URL.
    --^pwd                     Password for the specified URL.
    --^txtsimilaritypercent    Similarity percentage to consider two text files
                              are the same (moved item)
    --^binsimilaritypercent    Similarity percentage to consider two binary files
                              are the same (moved item)
    --^dirsimilaritypercent    Similarity percentage to consider two directories
                              are the same (moved item)
    --^author                  Uses name and timestamp values from the git author.
                              (git committer by default)
    --^skipgitlfs              Ignores the Git LFS configuration in the 
                              .gitattributes file. It acts like without Git LFS
                              support.
    --^gitpushchunk            Process the push operation (exporting changes from
                              Unity VCS to Git) in chunks of a certain number of
                              changesets. This is only useful for huge repos to
                              avoid network or package size related issues or
                              just for debugging purposes. It uses chunks of
                              1000 changesets if no value is specified.
    url                        Remote repository URL (http(s):// or git:// or a
                              SSH URL).
    repspec                    Repository specification. (Use 'cm ^help ^objectspec' t
                              learn more about repository specs.)
    git                        (Default).

== CMD_HELP_SYNC ==
Remarks:

    - If the git repository requires user and password, then use '^url', '--^user',
    and '--^pwd' options.
    - If the git repository doesn't require user and password, then use '^url'
    option with the first sync operation. With next sync operations, '^url'
    option is optional.
    - To use the SSH protocol to perform the sync, you must have the 'ssh' client
    added to the PATH environment variable and properly configured to connect
    to the remote host (i.e. private/public keys configured).
    - Similarity works the same way as the Unity VCS GUI does.

Examples:

    cm ^sync default@localhost:8087 ^git git://localhost/repository

== CMD_DESCRIPTION_TRIGGER ==
Allows the user to manage triggers.

== CMD_USAGE_TRIGGER ==
Usage:

    cm ^trigger | ^tr <command> [options]

Commands:

    - ^create | ^mk
    - ^delete | ^rm
    - ^edit
    - ^list | ^ls
    - ^showtypes

    To get more information about each command run:
    cm ^trigger <command> --^usage
    cm ^trigger <command> --^help

== CMD_HELP_TRIGGER ==
Examples:

    cm ^tr ^mk ^before-mklabel new "/path/to/script" --^server=myserver:8084
    cm ^tr ^edit ^before-mklabel 7 --^position=4 --^server=myserver:8084
    cm ^tr ^ls ^before-mkbranch --^server=myserver:8084
    cm ^tr ^rm ^after-setselector 4
    cm ^tr ^showtypes

== CMD_DESCRIPTION_UNDOCHECKOUT ==
Undoes the checkout of an item.

== CMD_USAGE_UNDOCHECKOUT ==
Usage:

    cm ^undocheckout | ^unco <item_path>[ ...] [-^a | --^all] [--^symlink] [--^silent]
                           [--^keepchanges | -^k]
                           [--^machinereadable [--^startlineseparator=<sep>] 
                           [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]

Options:

    -^a | --^all           Undoes all of the changes in the specified items. If
                           the item(s) were checked out, the checkout will be
                           reverted. If the item(s) were locally modified, the
                           modifications will be reverted.
    --^symlink             Applies the undocheckout operation to the link and not
                           to the target.
    --^silent              Does not show any output.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag,
                           specifies how the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag,
                           specifies how the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag,
                           specifies how the fields should be separated.
    --^keepchanges | -^k   Undoes the checkout and preserves the local changes.
                           Sample: undo the checkout of a file leave it as locally
                           changed with the same content on disk that it was.
                           This option cannot be used with dynamic workspaces.
    item_path              Items to apply the operation. Use a whitespace to separate
                           paths. Use double quotes (" ") to specify paths
                           containing spaces. Use . to apply the operation to
                           the current directory.


== CMD_HELP_UNDOCHECKOUT ==
Remarks:

    If an item is checked out and you do not want to checkin it, you can undo
    the checkout using this command. Both files and folders can be unchecked
    out. The item will be updated to the state it had before checking it out.

    Requirements:
    - The item must be under source code control.
    - The item must be checked out.

Reading input from stdin:

    The '^undocheckout' command can read paths from stdin. To do this, pass a
    single dash "-".
    Example: 
    cm ^undocheckout ^checkin -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify for which files to undo the checkout.
    Example:
    dir /S /B *.c | cm ^undocheckout --^all -
    (In Windows, undoes the checkout of all .c files in the workspace.)

Examples:

    cm ^undocheckout .
    (Undoes checkouts in the current directory.)

    cm ^undocheckout file1.txt file2.txt
    cm ^unco c:\workspace\file.txt
    (Undoes checkouts of the selected files.)

    cm ^unco -^a file1.txt
    (Undoes checkouts or local modifications of 'file1.txt')

    cm ^unco link --^symlink
    (Applies the undocheckout operation to the symlink file and not to the target.)

    cm ^status --^short --^changelist=pending_to_review | cm ^undocheckout -
    (Undoes client changelist. The command above will list the paths in the
    changelist named 'pending_to_review' and the path list will be redirected
    to the input of the undocheckout command).

    cm ^unco . --^machinereadable
    (Undoes checkouts in the current directory, and prints the result in a
    simplified, easier-to-parse format.)

    cm ^unco . --^machinereadable --^startlineseparator=">" --^endlineseparator="<" \
        --^fieldseparator=","
    (Undoes checkouts in the current directory, and prints the result in a
    simplified, easier to parse format, starting and ending the lines, and
    separating the fields, with the specified strings.)

== CMD_DESCRIPTION_UNDOCHECKOUTUNCHANGED ==
Undoes non-changed checked out items.

== CMD_USAGE_UNDOCHECKOUTUNCHANGED ==
Usage:

    cm ^uncounchanged | ^unuc <item_path>[ ...] [-^R | -^r | --^recursive]
                            [--^symlink] [--^silent]

Options:

    -^R | -^r | --^recursive  Undoes unchanged items recursively in the specified paths.
    --^symlink                Applies the uncounchanged operation to the link and not
                              to the target.
    --^silent                 Does not show any output.
    item_path                 Items to apply the operation. Use a whitespace to separate
                              paths. Use double quotes (" ") to specify paths
                              containing spaces. Use . to apply the operation to current
                              directory.

== CMD_HELP_UNDOCHECKOUTUNCHANGED ==
Remarks:

    This command is applied from the root of the workspace recursively.

Reading input from stdin:

    The '^uncounchanged' command can read paths from stdin. To do this, pass a
    single dash "-".
    Example: cm ^uncounchanged -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify for which unchanged files to undo
    the checkout.
    Example:
      dir /S /B *.c | cm ^uncounchanged -
      (In Windows, undoes the checkout on all unchanged .c files in the
      workspace.)

Examples:

    cm ^uncounchanged . -^R
    (Undoes checkouts of not changed files recursively on the current directory.)

    cm ^unuc /home/<USER>/mywk/project/templates -^R
    (Undoes checkouts of not changed files recursively on the selected directory.)

== CMD_DESCRIPTION_UNDELETE ==
Undeletes an item using a specific revision.

== CMD_USAGE_UNDELETE ==
Usage:

    cm ^undelete <revspec> <path>

Options:

    revspec             Specification of the revision whose contents will
                        be loaded in the workspace. (Use 'cm ^help ^objectspec' to
                        learn more about revision specs.)
    path                Restore path.

== CMD_HELP_UNDELETE ==
Remarks:

    The item to undelete should not be already loaded in the workspace.

    The '^undelete' operation is not supported for xlinks.

Example:

    cm ^undelete ^revid:756 C:\mywks\src\foo.c
    cm ^undelete ^itemid:68#^cs:2 C:\mywks\dir\myfile.pdf
    cm ^undelete ^serverpath:/src#^br:/main C:\mywks\Dir

== CMD_DESCRIPTION_UNDOCHANGE ==
Undoes the changes on a path.

== CMD_USAGE_UNDOCHANGE ==
Usage:

    cm ^undochange | ^unc <item_path>[ ...] [-^R | -^r | --^recursive]

Options:

    -^R | -^r | --^recursive  Applies the operation recursively.
    item_path                 Items to apply the operation. Use a whitespace to separate
                              paths. Use double quotes (" ") to specify paths
                              containing spaces. Use . to apply the operation to 
                              the current directory.

== CMD_HELP_UNDOCHANGE ==
Remarks:

    If an item is checked out or modified but not checked in and you do not
    want to check it in, you can undo the changes using this command. The item
    will be updated to the contents it had before.

Reading input from stdin:

    The '^undochange' command can read paths from stdin. To do this, pass a
    single dash "-".
    Example: 
    cm ^undochange -

    Paths will be read until an empty line is entered.
    This allows you to use pipe to specify for which files to undo changes.
    Example:
    dir /S /B *.c | cm ^undochange -
    (In Windows, undoes the changes of all .c files in the workspace.)

Examples:

    cm ^unc .
    (Undoes changes of the files on the current directory.)

    cm ^undochange . -^R
    (Undoes changes of the files recursively on the current directory.)

    cm ^unc file1.txt "file 2.txt"
    (Undoes changes of the selected files.)

    cm ^unc c:\workspace\file.txt
    (Undoes changes of the selected file.)

== CMD_DESCRIPTION_UNDO ==
Undoes changes in a workspace.

== CMD_USAGE_UNDO ==
Usage:

    cm ^undo [<path> [...]] [--^symlink] [-^r | --^recursive] [<filter> [...]]
            [--^silent | [--^machinereadable [--^startlineseparator=<sep>]
                                           [--^endlineseparator=<sep>]
                                           [--^fieldseparator=<sep>]]

Options:

    --^symlink               Applies the undo operation to the symlink and not
                            to the target.
    -^r                      Executes the undo recursively.
    --^silent                Does not show any output.
    --^machinereadable       Outputs the result in an easy-to-parse format.
    --^startlineseparator    Used with the '--^machinereadable' flag, specifies
                            how the lines should start.
    --^endlineseparator      Used with the '--^machinereadable' flag, specifies
                            how the lines should end.
    --^fieldseparator        Used with the '--^machinereadable' flag, specifies
                            how the fields should be separated.
    path        Path of the files or directories to apply the operation to.
                Use double quotes (" ") to specify paths containing spaces.
                Use a whitespace to separate paths.
                If no path is specified, by default the undo operation will take
                all of the files in the current directory.
    filter      Applies the specified filter or filters to the given paths. Use
                a whitespace to separate filters. See the Filters section for
                more information.

== CMD_HELP_UNDO ==
Remarks:

    - If no path is specified, it will undo every change in the current
      directory but not recursively.

    - If one or more paths are specified, it will undo every change in the
      specified paths but not recursively.

    - If you want the operation to be recursive, you must specify the '-^r' flag.
      To undo all of the changes below a directory including changes affecting
      the directory itself, run the following command:

          cm ^undo dirpath -^r

      If dirpath is a workspace path, every change in the workspace will be
      undone.

    - The '^undo' command is dangerous because it undoes work irreversibly.
      Once the ^undo has finished, it is not possible to recover the previous
      state of the files and directories affected by it.

    - Consider the following scenario:

    - /src
        - file.txt
        - code.cs
        - /test
            - test_a.py
            - test_b.py

    These commands are equivalent when executed from the /src directory:
    cm ^undo
    cm ^undo *
    cm ^undo file.txt code.cs /test

    These commands are also equivalent when executed from the /src directory:
    cm ^undo .
    cm ^undo /src file.txt code.cs

Filters:

    The paths can be filtered using one or more of the filters below. Each of
    those filters refers to a type of change:
    --^checkedout    Selects checked-out files and directories.
    --^unchanged     Selects files whose content is unchanged.
    --^changed       Selects locally changed or checked-out files and directories.
    --^deleted       Selects deleted files and directories.
    --^moved         Selects moved files and directories.
    --^added         Selects added files and directories.

    If the path matches one or more of the specified kinds of changes, those
    types of changes will be undone on that said path.
    For example, if you specify both '--^checkedout' and '--^moved', if a file
    is both checkedout and moved, both changes will be undone.

    If no filter is specified, all kinds of changes are undone.

Examples:

    cm ^undo .
    (Undoes all changes in the current directory.
     It won't undo changes within its subdirectories.)

    cm ^undo . -^r
    (Undoes all changes in the current directory recursively. If executed
    from the workspace's root, undoes all changes in the entire workspace.)

    cm ^co file.txt
    cm ^undo file.txt
    (Undoes the checkout on 'file.txt'.)

    cm ^undo c:\otherworkspace\file.txt
    (Undoes changes in file 'file.txt' located in a workspace other than the
    current one.)

    ^echo ^content >> file.txt
    cm ^undo file.txt
    (Undoes the local change to 'file.txt'.)

    cm ^undo src
    (Undoes changes to the src directory and its files.)

    cm ^undo src/*
    (Undoes changes in every file and directory contained in src, without
    affecting src.)

    cm ^undo *.cs
    (Undoes changes to every file or directory that matches *.cs in the current
    directory.)

    cm ^undo *.cs -^r
    (Undoes changes on every file or directory that matches *.cs in the current
    directory and every directory below it.)

    cm ^co file1.txt file2.txt
    ^echo ^content >> file1.txt
    cm ^undo --^unchanged
    (Undoes the checkout of unchanged 'file2.txt', ignoring locally changed
    'file1.txt'.)

    ^echo ^content >> file1.txt
    ^echo ^content >> file2.txt
    cm ^co file1.txt
    cm ^undo --^checkedout
    (Undoes the changes in checked-out file 'file1.txt', ignoring 'file2.txt' as
    it is not checked-out.)

    cm ^add file.txt
    cm ^undo file.txt
    (Undo the add of 'file.txt' making it once again a private file.)

    ^rm file1.txt
    ^echo ^content >> file2.txt
    cm ^add file3.txt
    cm ^undo --^deleted --^added *
    (Undoes the 'file1.txt' delete and 'file3.txt' add, ignoring the 'file2.txt'
    change.)

== CMD_DESCRIPTION_LOCK_UNLOCK ==
Undoes item locks on a server.

== CMD_USAGE_LOCK_UNLOCK ==
Usage:

    cm ^lock ^unlock <itemspec>[ ...] [--^remove]

Options:
    --^remove            Removes the locks completely for the specified items.
                        If this option is not specified, it only releases the
                        Locked status.
    itemspec            One or more item specs. (Use 'cm ^help ^objectspec'
                        to learn more about itemspecs.)

== CMD_HELP_LOCK_UNLOCK ==
Remarks:

    - Only the administrator of the server can run the 'cm ^unlock --^remove'
      command to delete locks.
    - Only the administrator can release locks that belong to other users.
      Users can release their own locks.

Examples:

    cm ^lock ^unlock ^itemid:56@myrep@DIGITALIS:8084
    (Releases the lock for the item id 56 in the repo 'myrep@DIGITALIS'.)

    cm ^lock ^unlock ^item:/home/<USER>/workspace/foo.psd ^item:/home/<USER>/workspace/bar.psd
    (Releases the selected item locks.)

    cm ^lock ^unlock ^itemid:56@myrep ^itemid:89@myrep --^remove
    (Removes the selected items locks.)

== CMD_DESCRIPTION_LOCK_CREATE ==
Create item locks on a server.

== CMD_USAGE_LOCK_CREATE ==
Usage:

    cm ^lock ^create | ^mk <branchspec> <itemspec>[ ...]
    
    branchspec          The branch where the locks will be created. 
                        (Use 'cm ^help ^objectspec' to learn more about branchspecs.)
    itemspec            One or more item specs. (Use 'cm ^help ^objectspec'
                        to learn more about itemspecs.)

== CMD_HELP_LOCK_CREATE ==
Remarks:

    - Only the administrator can create locks.
    - All locks created with this command are created in Retained status.
    - If the revision loaded in the specified branch (for each item) is the
      same loaded in the lock destination branch (/main by default),
      then no lock is created.
    - The repspec or the branchspec must be the same of all the itemspecs
      specified.

Examples:

    cm ^lock ^create /main/task@myrep ^itemid:56@myrep
    (Creates a lock for the item id 56 in the branch /main/task@myrep.)

    cm ^lock ^create br:/main/task item:/workspace/foo.psd ^item:/workspace/bar.psd
    (Creates a lock for the selected item in the branch /main/task.)

== CMD_DESCRIPTION_UPDATE ==
Updates the workspace and downloads latest changes.

== CMD_USAGE_UPDATE ==
Usage:

    cm ^update [<item_path> | --^last]
              [--^changeset=<csetspec>] [--^cloaked] [--^dontmerge] [--^forced]
              [--^ignorechanges] [--^override] [--^recursewk] [--^skipchangedcheck]
              [--^silent] [--^verbose] [--^xml[=<output_file>]] [--^encoding=<name>]
              [--^machinereadable [--^startlineseparator=<sep>]
                [--^endlineseparator=<sep>] [--^fieldseparator=<sep>]]
              [--^forcedetailedprogress] [--^noinput]

Options:

    --^changeset             Updates the workspace to a specific changeset.
                            (Use 'cm ^help ^objectspec' to learn more about
                            changeset specs.)
    --^cloaked               Includes the cloaked items in the update operation.
                            If this option is not specified, those items that are
                            cloaked will be ignored in the operation.
    --^dontmerge             In case an update merge is required during the update
                            operation, does not perform it.
    --^forced                Forces updating items to the revision specified in
                            the selector.
    --^ignorechanges         Ignores the pending changes warning message that is
                            shown if there are pending changes detected when
                            updating the workspace.
    --^noinput               Skips the interactive questions to continue the
                            operation with pending changes or to shelve them.
                            Using this option disables the possibility of
                            bringing your pending changes.
    --^override              Overrides changed files outside Unity VCS control.
                            Their content will be overwritten with the server
                            content.
    --^recursewk             Updates all the workspaces found within the current
                            path. Useful to update all the workspaces contained
                            in a specific path.
    --^skipchangedcheck      Checks if there are local changes in your workspace
                            before starting. If you always checkout before
                            modifying a file, you can use this check and speed
                            up the operation.
    --^silent                No output is shown unless an error happens.
                            Using this option disables the possibility of
                            bringing your pending changes.
    --^verbose               Shows additional information.
    --^xml                   Prints the output in XML format to the standard output.
                            It is possible to specify an output file.
                            Using this option disables the possibility of
                            bringing your pending changes.
    --^encoding              Used with the --^xml option, specifies the encoding to
                            use in the XML output, i.e.: utf-8.
                            See the MSDN documentation at
                            http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                            to get the table of supported encodings and its format, 
                            (at the end of the page, in the "Name" column).
    --^machinereadable       Outputs the result in an easy-to-parse format.
                            Using this option disables the possibility of
                            bringing your pending changes.
    --^startlineseparator    Used with the '--^machinereadable' flag,
                            specifies how the lines should start.
    --^endlineseparator      Used with the '--^machinereadable' flag,
                            specifies how the lines should end.
    --^fieldseparator        Used with the '--^machinereadable' flag,
                            specifies how the fields should be separated.
    --^forcedetailedprogress Forces detailed progress even when standard output
                            is redirected.
    --^last                  Changes the workspace selector from a changeset
                            configuration or a label configuration to a branch
                            configuration before updating. The selector is changed
                            to the branch the changeset or label belongs to.
    item_path                Path to update. Use . to apply update to current directory.
                            If no path is specified, then the current workspace is
                            fully updated.

== CMD_HELP_UPDATE ==
Remarks:

    The '^update' command only downloads needed files.

    The command assumes recursive operation.

    When the '--^last' option is used, it is not necessary to specify a path.
    In this case, the workspace the current working directory belongs to will 
    be updated. (Remember that specifying this flag could cause the workspace
    selector to be changed to a branch configuration if the selector was
    previously pointing to a changeset or a label.)

Examples:

    cm ^update
    (Updates all in the current workspace.)

    cm ^update .
    (Updates current directory, and all children items.)

    cm ^update . --^forced --^verbose
    (Forces retrieval of all revisions.)

    cm ^update --^last

    cm ^update . --^machinereadable --^startlineseparator=">"
    (Updates current directory and prints the result in a simplified
    easier-to-parse format, starting the lines with the specified 
    strings.)

== CMD_DESCRIPTION_VERSION ==
Shows the current client version number.

== CMD_USAGE_VERSION ==
Usage:

    cm ^version

== CMD_HELP_VERSION ==
Shows the current client version number.

== CMD_DESCRIPTION_WHOAMI ==
Shows the current Unity VCS user.

== CMD_USAGE_WHOAMI ==
Usage:

    cm ^whoami

== CMD_HELP_WHOAMI ==
Shows the current Unity VCS user.

== CMD_DESCRIPTION_WORKSPACE ==
Allows the user to manage workspaces.

== CMD_USAGE_WORKSPACE ==
Usage:

    cm ^workspace | ^wk <command> [options]

Commands:

    - ^list | ^ls
    - ^create | ^mk
    - ^delete | ^rm
    - ^move | ^mv
    - ^rename

    To get more information about each command run:
    cm ^workspace <command> --^usage
    cm ^workspace <command> --^help

== CMD_HELP_WORKSPACE ==
Examples:

    cm ^workspace ^create myWorkspace wk_path
    cm ^workspace ^list
    cm ^workspace ^delete myWorkspace

== CMD_DESCRIPTION_WORKSPACE_CREATE ==
Creates a new workspace.

== CMD_USAGE_WORKSPACE_CREATE ==
Usage:

    cm ^workspace | ^wk [^create | ^mk] <rep_spec>
                        [^create | ^mk] <wk_name> <wk_path> [<rep_spec>]
                        [^create | ^mk] <wk_name> <wk_path> [--^selector[=<selector_file>]
    (Creates a new workspace.)

    cm ^workspace | ^wk [^create | ^mk] <wk_name> <wk_path> --^dynamic --^tree=[<tree>]
    (Creates a dynamic workspace. This feature is still experimental, and it's
    only available for Windows.)

Options:

    --^selector          Edits a selector for the new workspace.
                        If a selector file is specified, then sets the selector
                        for the new workspace from the specified file.
    --^dynamic           Creates a dynamic workspace. This feature is still
                        experimental, and it's only available for Windows.
                        Specifying this flag requires using the --^tree parameter.
    --^tree              Used with the '--^dynamic' flag, specifies the initial
                        point the dynamic workspace is going to load. It can
                        either be a branch, changeset, or label specification.
                        The workspace will later on use the repository in the
                        spec. (Use 'cm ^help ^objectspec' to learn more about specs.)
    wk_name             The new workspace name.
    wk_path             Path of the new workspace.
    rep_spec            Creates the new workspace with the specified repository.
                        Repository specification: check 'cm ^help ^objectspec'.

== CMD_HELP_WORKSPACE_CREATE ==
Remarks:

    - A workspace is a view of the repository mapped to the local filesystem.
      The workspace selector defines the rules that specify workspace content.
      Use 'cm ^showselector' to display a workspace selector or 'cm ^setselector'
      to modify it.
    - If neither rep_spec nor '--^selector' is specified, then the workspace
      will automatically be configured to use the first repository
      (alphabetically) of the server configured in the client.conf file.
    - The dynamic workspaces is a experimental feature (Windows only), and it
      requires the plasticfs.exe program running.

Examples:

    cm ^workspace ^create mycode
    cm ^wk ^mk mycode
    (Creates a 'mycode' workspace pointing to the repository with the same name.
    The workspace directory will be created under the current directory.)

    cm ^wk ^mk mycode@localhost:8084
    cm ^wk ^mk mycode@myorganization@cloud
    (Creates a 'mycode' workspace as before, but you can specify different repository server.)

    cm ^workspace ^create myworkspace c:\workspace
    cm ^wk ^mk myworkspace /home/<USER>/plastic_view
    (Creates 'myworkspace' workspace in Windows and in Linux respectively.)

    cm ^wk mywktest c:\wks\wktest --^selector=myselector.txt
    (Creates 'mywktest' workspace using the selector in 'myselector.txt' file.)

    cm ^wk mywkprj c:\wks\wkprj myrep@^repserver:localhost:8084
    (Creates 'mywkprj' workspace with the selected repository.)

    cm ^wk mywkprj c:\dynwks\mywkprj --^dynamic --^tree=^br:/main@myrep@localhost:8084
    (Creates dynamic 'mywkprj' workspace with the 'myrep@localhost:8084'
     repository, pointing to '^br:/main' the first time it gets mounted.)

== CMD_DESCRIPTION_WORKSPACE_DELETE ==
Deletes a workspace.

== CMD_USAGE_WORKSPACE_DELETE ==
Usage:

    cm ^workspace | ^wk ^delete | ^rm [<wk_path> | <wkspec>] [--^keepmetadata]

Options:

    --^keepmetadata      Does not delete the metadata files in the .plastic
                        folder.
    wk_path             Path of the workspace to be deleted.
    wkspec              Specification of the workspace to delete. (Use 
                        'cm ^help ^objectspec' to learn more about specs.)

== CMD_HELP_WORKSPACE_DELETE ==
Remarks:

    This command deletes a workspace, specified by path or spec.
    If no arguments are specified, current workspace will be assumed.

Examples:

    cm ^workspace ^delete
    (Removes current workspace.)

    cm ^wk ^delete c:\workspace
    cm ^workspace rm /home/<USER>/wks
    cm ^wk ^rm ^wk:MiWorkspace
    cm ^wk ^rm ^wk:MiWorkspace@DIGITALIS

== CMD_DESCRIPTION_WORKSPACE_LIST ==
Lists workspaces.

== CMD_USAGE_WORKSPACE_LIST ==
Usage:

    cm ^workspace | ^wk [^list | ^ls] [--^format=<str_format>]

Options:

    --^format            Retrieves the output message in a specific format. See
                        Remarks for more info.

== CMD_HELP_WORKSPACE_LIST ==
Remarks:

    Output format parameters (--^format option):
        This command accepts a format string to show the output.
        The output parameters of this command are the following:

        {0} | {^wkname}    Workspace name.
        {1} | {^machine}   Client machine name.
        {2} | {^path}      Workspace path.
        {3} | {^wkid}      Workspace unique identifier.
        {4} | {^wkspec}    Workspace specification using the format:
                           'wkname@machine'.
        {^tab}             Inserts a tab space.
        {^newline}         Inserts a new line.

Examples:

    cm ^wk
    (Lists all workspaces.)

    cm ^workspace ^list --^format={0}#{3,40}
    cm ^workspace ^list --^format={^wkname}#{^wkid,40}
    (Lists all workspaces and shows the workspace name, a # symbol and the
    workspace GUID field in 40 spaces, aligned to left.)

    cm ^wk --^format="Workspace {0} in path {2}"
    cm ^wk --^format="Workspace {^wkname} in path {^path}"
    (Lists all workspaces and shows result as formatted strings.)

== CMD_DESCRIPTION_WORKSPACE_MOVE ==
Moves a workspace.

== CMD_USAGE_WORKSPACE_MOVE ==
Usage:

    cm ^workspace | ^wk ^move | ^mv [<wkspec>] <new_path>

Options:

    wkspec              Specification of the workspace to move. (Use 
                        'cm ^help ^objectspec' to learn more about specs.)
    new_path            Workspace will be moved to here.

== CMD_HELP_WORKSPACE_MOVE ==
Remarks:

    This command allows users to move a workspace to another location on disk.

Examples:

    cm ^workspace ^move myWorkspace \new\workspaceDirectory
    (Moves 'myWorkspace' to the specified location.)

    cm ^wk ^mv c:\users\<USER>\wkspaces\newlocation
    (Moves the current workspace to the new location.)

== CMD_DESCRIPTION_WORKSPACE_RENAME ==
Renames a workspace.

== CMD_USAGE_WORKSPACE_RENAME ==
Usage:

    cm ^workspace | ^wk ^rename [<wk_name>] <new_name>

Options:

    wk_name             Workspace to rename.
    new_name            New name for the workspace.

== CMD_HELP_WORKSPACE_RENAME ==
Remarks:

    This command renames a workspace.
    If no workspace name is supplied, the current workspace will be used.

Examples:

    cm ^workspace ^rename mywk1 wk2
    (Renames the workspace 'mywk1' to 'wk2'.)

    cm ^wk ^rename newname
    (Renames the current workspace to 'newname'.)

== CMD_DESCRIPTION_WORKSPACESTATUS ==
Shows changes in the workspace.

== CMD_USAGE_WORKSPACESTATUS ==
Usage:

    cm ^status [<wk_path>] [--^changelist[=<name>] | --^changelists] [--^cutignored]
              [ --^header] [ --^noheader] [ --^nomergesinfo] [ --^head]
              [--^short] [--^symlink] [ --^dirwithchanges] [--^xml[=<output_file>]]
              [--^encoding=<name>] [ --^wrp | --^wkrootrelativepaths]
              [--^fullpaths | --^fp] [<legacy_options>] [<search_types>[ ...]]
              [--^pretty]
              [--^machinereadable [--^startlineseparator=sep]
                [--^endlineseparator=sep] [--^fieldseparator=sep]]

Options:

    --^changelist          Shows changes in the selected changelist.
    --^changelists         Shows changes grouped in client changelists.
    --^cutignored          Skips the contents of ignored directories.
                          Requires the '--^ignored' search type. See the Search
                          types section for more information.
    --^header              Only prints the workspace status.
    --^noheader            Only prints the modified item search result.
    --^nomergesinfo        Doesn't print the merge info for changes.
    --^head                Prints the status of the last changeset on the branch.
    --^short               Lists only paths that contains changes.
    --^symlink             Applies the operation to the symlink and not to the
                          target.
    --^dirwithchanges     Shows directories that contain changes inside them
                          (added, moved, removed items inside).
    --^xml                 Prints the output in XML format to the standard output.
                          It is possible to specify an output file.
    --^pretty              Prints workspace changes in a nice table format.
    --^encoding            Used with the --^xml option, specifies the encoding to
                          use in the XML output, i.e.: utf-8.
                          See the MSDN documentation at
                          http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                          to get the table of supported encodings and its format
                          (at the end of the page, in the "Name" column).
    --^wrp                Print workspace root relative paths instead of
                          current directory relative paths.
    --^fullpaths, --^fp     Force printing absolute paths, overriding any other
                          path printing setting.
    --^machinereadable     Outputs the result in an easy-to-parse format.
    --^startlineseparator  Used with the '--^machinereadable' flag,
                          specifies how the lines should start.
    --^endlineseparator    Used with the '--^machinereadable' flag,
                          specifies how the lines should end.
    --^fieldseparator      Used with the '--^machinereadable' flag,
                          specifies how the fields should be separated.
    --^iscochanged         Shows whether contents of a checked-out file have 
                          changed or not. This flag is only available with 
                          "--^compact", "--^xml" and "--^machinereadable" options.
                          If this flag is not set, the "--^compact", "--^xml" or 
                          "--^machinereadable" options will show just "^CO" 
                          status for a checked-out file with its contents
                          changed.
                          If this flag is set, the "--^compact", "--^xml" or 
                          "--^machinereadable" options will show "^CO+CH" status
                          for a checked-out file with its contents changed.
    wk_path               Path of the workspace where the search will be
                          performed.

Legacy options:

    --^cset              Prints the workspace status in the legacy format.
    --^compact           Prints the workspace status and changelists in the
                        legacy format.
    --^noheaders         When used in conjunction with the '--^compact' flag, the
                        changelist headers will not be printed. (Does not apply
                        to the new changelists format.)

Search types:

    --^added                        Prints added items.
    --^checkout                     Prints checkedout items.
    --^changed                      Prints changed items.
    --^copied                       Prints copied items.
    --^replaced                     Prints replaced items.
    --^deleted                      Prints deleted items.
    --^localdeleted                 Prints locally deleted items.
    --^moved                        Prints moved items.
    --^localmoved                   Prints locally moved items.
    --^percentofsimilarity=<value>  Percent of similarity between two files in
                                    order to consider them the same item. Used
                                    in the locally moved search. Its default
                                    value is 20%.
    --^txtsameext                   Only those text files that have the same
                                    extension will be taken into account by the
                                    similarity content matching process during
                                    the moved items search. By default, any
                                    text file will be processed.
    --^binanyext                    Any binary file will be taken into account
                                    by the similarity content matching process
                                    during the moved items search. By default,
                                    only those binary files that have the same
                                    extension will be processed.
    --^private                      Prints non controlled items.
    --^ignored                      Prints ignored items.
    --^hiddenchanged                Prints hidden changed items. (Includes 
                                    '--^changed')
    --^cloaked                      Prints cloaked items.
    --^controlledchanged            This flag substitutes the following options:
                                    '--^added', '--^checkout', '--^copied',
                                    '--^replaced', '--^deleted', '--^moved'.
    --^all                          This flag replaces the following parameters:
                                    '--^controlledchanged', '--^changed',
                                    '--^localdeleted', '--^localmoved', '--^private'.

== CMD_HELP_WORKSPACESTATUS ==
Remarks:

    The '^status' command prints the loaded changeset on a workspace and gets
    the changed elements inside the workspace.

    This command can be used to show the pending changes in a workspace; the
    type of changes that can be searched can be modified by using the command
    parameters. By default, all changes are displayed, be they controlled
    or local.

    The percent of similarity parameter '--^percentofsimilarity' (-^p) is used
    by the locally moved search to decide if two elements are the same item.
    The default value is 20% but it can be adjusted.

    It is possible to show workspace changes grouped by client changelists.
    The '^default' changelist includes the changes that are not included in
    other changelists. That being said, the changes the default changelist
    will show depends on the search types flags specified.

    Showing changes grouped by changelists requires showing controlled
    changes too (items with status equal to '^added', '^checkout', '^copied',
    '^replaced', '^deleted', or '^moved'). So, the '--^controlledchanged' option
    will be automatically enabled when changelists are shown.

    The default encoding for XML output is utf-8.

    By default, this command will print current directory relative paths,
    unless the '--^machinereadable' or '--^short' flags are specified. If
    any of them are specified, the command will print absolute paths.

    If '--^xml' flag is specified, workspace root relative paths will
    be printed (unless the '--^fp' flag is also specified, printing
    absolute paths instead).

Examples:

    cm ^status
    (Prints the working changeset and also all item types changed in the 
    workspace, except the ignored ones.)

    cm ^status --^controlledchanged
    (Prints the working changeset and also the items that are checkedout, added,
    copied, replaced, deleted, and moved.)

    cm ^status --^added
    (Prints only the working changeset and the added items inside the workspace.)

    cm ^status c:\workspaceLocation\code\client --^added
    (Prints the working changeset and the added items under the specified path
    recursively.)

    cm ^status --^changelists
    cm ^status --^changelist
    (Shows all the workspace changes grouped by client changelists.)

    cm ^status --^changelist=pending_to_review
    (Shows the changes on the changelist named 'pending_to_review'.)

    cm ^status --^changelist=default --^private
    (Shows the changes in the 'default' changelist, showing private items, along
    with items with controlled changes, if any.)

    cm ^status --^short --^changelist=pending_to_review | cm ^checkin -
    (Checkins the changes in the changelist 'pending_to_review'.)

    cm ^status C:\workspaceLocation --^xml=output.xml
    (Gets the status information in XML format and using utf-8 in the file
    output.xml.)

    cm ^status --^ignored
    (Shows all ignored items.)

    cm ^status --^ignored --^cutignored
    (Shows ignored files whose parent directory is not ignored and ignored 
    directories but not their contents.)

== CMD_DESCRIPTION_XLINK ==
Creates, edits, or displays details of an Xlink.

== CMD_USAGE_XLINK ==
Usage:

    cm ^xlink [-^w] [-^rs] <xlink_path> / (<csetspec> | <lbspec> | <brspec)>
             [<expansion_rules>[ ...]]
    (Creates an Xlink.)

    cm ^xlink [-^rs] <xlink_path> /<relative_path> (<csetspec> | <lbspec> | <brspec>)
             [<expansion_rules>[ ...]]
    (Creates a readonly partial Xlink pointing to /<relative_path> instead of
    the default root / .)

    cm ^xlink -^e <xlink_path> (<csetspec> | <lbspec> | <brspec>)
    (Edits an Xlink to change the target specification.)

    cm ^xlink -^s|--^show <xlink_path>
    (Shows the Xlink information including the expansion rules.)

    cm ^xlink -^ar|--^addrules <xlink_path> <expansion_rules>[ ...]
    (Adds the given expansion rules to the Xlink.)

    cm ^xlink -^dr|--^deleterules <xlink_path> <expansion_rules>[ ...]
    (Removes the given expansion rules from the Xlink.)

Options:

    -^e                  Edits an existing Xlink to change the target changeset
                        specification.
    -^s | --^show         Shows information about the selected Xlink.
    -^ar | --^addrules    Adds one or more expansion rules to the selected Xlink.
    -^dr | --^deleterules  Deletes one or more expansion rules from the selected
                        Xlink.
    -^w                  Indicates that the Xlink is writable. This means that
                        the contents of the target repository can be modified
                        through branch autoexpansion.
    -^rs                 Relative server. This option allows creating a relative
                        Xlink that is independent of the repository server. This
                        way, Xlinks created in replicated repositories in
                        different servers will be automatically identified.
    expansion_rules     To specify one or more expansion rule. Each expansion
                        rule is a pair branch-target branch:
                        ^br:/main/fix-^br:/main/develop/fix
    xlink_path          This is the directory in the current workspace where
                        the linked repository will be mounted (when creating an
                        Xlink) or is mounted (when editing an Xlink).
    csetspec            The full target changeset specification in the remote
                        repository.
                        This determines what version and branch is loaded in the
                        workspace for the linked repository.
                        (Use 'cm ^help ^objectspec' to learn more about changeset
                        specs.)
    lbspec              The full label specification in the remote repository.
                        (Use 'cm ^help ^objectspec' to learn more about label
                        specs.)
    brspec              The full branch specification in the remote repository.
                        This uses the current changeset where the specified
                        branch is pointing to. (Use 'cm ^help ^objectspec' to
                        learn more about branch specs.)

== CMD_HELP_XLINK ==
Remarks:

    This command creates an Xlink to a given changeset. By default, a read-only
    Xlink is created. This means that the contents loaded in the workspace
    inside the Xlink cannot be modified. To be able to make changes in the
    Xlinked content, create a writable Xlink instead (using the '-^w' option).

    It is possible to use a simplified syntax of the command when editing the
    target changeset of an Xlink. This way, the only required parameter is the
    new target changeset. The rest of parameters of the Xlink will not be
    modified.

    Branch auto-expansion:

    When a change is made in any writable-xlinked repositories ('-^w' option), a
    new branch needs to be created in the target repository. The name of the
    new branch is based on the checkout branch defined in the top-level
    repository. To determine the name of the branch to use, these rules apply:

    1) A check is made to see if a branch with the same full name exists
       in the target repository:
         - If it exists, this is used as the checkout branch.
         - If it does not exist, the branch name is built this way:
           - Name of the branch of the target Xlinked changeset + short name of
             the checkout branch (last part).
           - If this branch exists, it is used as the checkout branch.
           - Otherwise, the branch is created and the branch base is set to the
             Xlinked changeset.
    2) A new version of the Xlink is created in the branch on the parent
        repository pointing to the new changeset in the Xlinked repository.

    Finally, the complete Xlink structure is kept up to date with the latest
    changes in the right versions.

Examples:

    cm ^xlink code\firstrepo / 1@first@localhost:8084
    (Creates an Xlink in folder 'firstrepo' in the current workspace where the
    changeset '1' in the repository 'first' will be mounted.)

    cm ^xlink opengl\include /includes/opengl 1627@includes@localhost:8087
    (Creates a readonly partial Xlink in directory 'opengl\include' in the
     current workspace where the path '/includes/opengl' in changeset '1627' in
     the repository 'includes' will be mounted as root. It means that whatever
     is inside '/includes/opengl' will be mounted in 'opengl\include' while the
    rest of the repository will be ignored.)

    cm ^xlink -^w -^rs code\secondrepo / ^lb:LB001@second@localhost:8084
    (Creates a writable and relative Xlink in folder 'secondrepo' in the
     current workspace where the label 'LB001' in the repository 'second' will
    be mounted.)

    cm ^xlink code\thirdrepo / 3@third@localhost:8087 ^br:/main-^br:/main/scm003
    (Creates an Xlink in folder 'thirdrepo' in the current workspace where the
    changeset '3' in the repository 'third' will be mounted.)

    cm ^xlink -^e code\secondrepo ^br:/main/task1234@second@localhost:8084
    (Edits the Xlink 'code\secondrepo' to change the target repository by
    linking the branch 'main/task1234' in the repository 'second'.)

    cm ^xlink --^show code\thirdrepo
    (Shows information of the Xlink 'code\thirdrepo' including its expansion
     rules if exist).

    cm ^xlink -^ar code\secondrepo ^br:/main-^br:/main/develop ^br:/main/fix-^br:/main/develop/fix
    (Adds two expansion rules to the xlink 'code\secondrepo'.)

    cm ^xlink -^dr code\secondrepo ^br:/main/fix-^br:/main/develop/fix
    (Deletes the expansion rule from the xlink 'code\secondrepo').

== CMD_DESCRIPTION_AUTOCOMPLETE ==
Allows to implement autocomplete suggestions in a line at the cursor position.

== CMD_USAGE_AUTOCOMPLETE ==
Usage:

    cm ^autocomplete ^install
    (Installs 'cm' command completion in the shell.)

    cm ^autocomplete ^uninstall
    (Uninstalls 'cm' command completion from the shell.)

    cm ^autocomplete --^line <shell_line> --^position <cursor_position>
    (Returns autocomplete suggestions for 'shell_line' to be inserted at
    'cursor_position'.)

Options:

    shell_line       The line the user has written into the shell when the
                     autocompletion was requested.
                     In Bash, it is at the COMP_LINE environment variable.
                     In PowerShell, it is at the $wordToComplete variable.
    cursor_position  The position of the cursor when the autocompletion was
                     requested.
                     In Bash, it is at the COMP_POINT environment variable.
                     In PowerShell, it is at the $cursorPosition variable.

== CMD_HELP_AUTOCOMPLETE ==

Remarks:

    This command is not intended to be used by the final user, 
    but it is documented in case it is needed to extend autocompletion 
    support for the shell of choice.

Examples:

    cm ^autocomplete --^line "cm ^show" --^position 7
    (Shows a list of commands starting with "show". In this case: 
    showcommands, showfindobjects, showacl, showowner, showpermissions,
    showselector.)

== CMD_DESCRIPTION_API ==
Starts a local HTTP server that listens for REST API requests.

== CMD_USAGE_API ==
Usage:

    cm ^api [(-^p|--^port)=<portnumber>] [(-^r|--^remote)]

Options:

    -^p | --^port         Tells the server to listen on port <portnumber>
                        instead of 9090.
    -^r | --^remote       Allows incoming remote connections. This means
                        connections that come from other hosts instead of the
                        local one.

== CMD_HELP_API ==
Remarks:

    The cm ^api command allows programmers to perform Unity VCS client operations
    in their machines.
    Read the Unity VCS API Guide for more information:
    https://www.plasticscm.com/documentation/restapi/plastic-scm-version-control-rest-api-guide

    By default, the API listens for local connections only, on port 9090.

    Press the Enter key to stop the server.

Examples:

    cm ^api
    (Starts the API listening on port 9090, local connections only.)

    cm ^api -^r
    (Starts the API listening on port 9090, allowing any incoming connection.)

    cm ^api --^port=15000 -^r
    (Starts the API listening on port 15000, allowing any incoming connection.)

== CMD_DESCRIPTION_CONFIGURECLIENT ==
Configures the Unity VCS client for the current machine user to work with a default server.

== CMD_USAGE_CONFIGURECLIENT ==
Usage:
    cm ^configure [--^language=<language> --^workingmode=<mode> [AuthParameters] 
                 --^server=<server> [--^port=<port>]] [--^clientconf=<clientconfpath>]

Options:

    --^language          Available languages. See remarks for more info.
    --^workingmode       Available users/security working modes. See remarks for more info.
    --^server            Unity VCS server IP / address.
    --^port              Unity VCS server port (optional for Cloud servers).
    --^clientconf        File path used to create the configuration file (optional).
    AuthParameters       Authentication parameters. See Remarks for more info.

== CMD_HELP_CONFIGURECLIENT ==
Remarks:

    The cm ^configure command cannot be used on Cloud Edition or DVCS Edition of Unity VCS.
    Use 'plastic --configure' instead.

    Languages:

    - en (English)
    - es (Spanish)

    Working modes:

    - NameWorkingMode (Name)
    - NameIDWorkingMode (Name + ID)
    - LDAPWorkingMode (LDAP)
    - ADWorkingMode (Active Directory)
    - UPWorkingMode (User and password)
    - SSOWorkingMode (Single Sign On)

    Client configuration:

    If this parameter is not specified, the default directory for
    'client.conf' file would be '%LocalAppData%\plastic4'
    on Windows or '$HOME/.plastic4' on linux/macOS.

    AuthParameters:

    - ^LDAPWorkingMode and ^UPWorkingMode
        - --^user=<user>
        - --^password=<password>
    - ^SSOWorkingMode
        - --^user=<user>
        - --^token=<token>

Examples:

    cm ^configure 
    (runs the interactive Unity VCS client configuration command)

    cm ^configure --^language=^en --^workingmode=^LDAPWorkingMode --^user=^jack --^password=^01234 \
        --^server=^plastic.mymachine.com --^port=^8084
    (configures the Unity VCS client with the specified parameters and creates the 'client.conf'
    configuration file in the default directory)

    cm ^configure --^language=^en --^workingmode=^NameWorkingMode --^server=^plastic.mymachine.com \
        --^port=^8084 --^clientconf=^clientconf_exp.conf
    (configures the Unity VCS client with the specified parameters and creates the 'client.conf'
    configuration file in the specified path)

    cm ^configure --^clientconf=c:/path/to/myclient.conf
    (Specified path will be used to create the client configuration file)

    cm ^configure --^clientconf=myclient.conf
    (File myclient.conf inside default config directory will be used)

    cm ^configure --^clientconf=c:/exisitingDirectory
    (Default filename, client.conf, in specified directory will be used)
    configuration file in the specified path)

== CMD_DESCRIPTION_PURGE ==
Allows the user to inspect, register and execute repository purges. Purged revisions are no longer accessible for that repository, thus helping to free space.

Warning: Purge actions are irreversible. Once they are executed, you will not be able to load purged revisions anymore—either by switching a workspace or when showing branch or changeset differences. Use this under your own responsibility.

== CMD_USAGE_PURGE ==
Usage:
    cm ^purge <command> [options]

Commands:

    - ^register
    - ^execute
    - ^show
    - ^history
    - ^unregister

    To get more information about each command run:
    cm ^purge <command> --^usage
    cm ^purge <command> --^help

== CMD_HELP_PURGE ==
Examples:

    cm ^purge ^register ".mkv" "1955-Nov-05 6:00 AM" --^repository=timemachine
    (registers a purge action for the 'timemachine' repository)

    cm ^purge ^history
    (lists the ID and status of all the purge actions ever registerd in the server)

    cm ^purge ^show 545ec81b-23ea-462c-91f4-d7c62a6e8817 --^verbose
    (shows in detail the purge action metadata for a given ID,
    including items and revisions affected by the purge)

    cm ^purge ^execute 545ec81b-23ea-462c-91f4-d7c62a6e8817
    (starts a previously registered purge action)

    cm ^purge ^unregister 545ec81b-23ea-462c-91f4-d7c62a6e8817
    (purges that were not executed can be deleted from the registry)

== CMD_DESCRIPTION_PURGE_REGISTER ==
Computes and registers a purge action.

File revisions older than the selected date(s) would be considered for purging and, as a rule, we will keep at least a revision in every branch head and labeled revisions. You can use the "cm ^purge ^show" command to check the selected revisions prior to run it with the "cm ^purge ^execute" command.

== CMD_USAGE_PURGE_REGISTER ==
Usage:
    cm ^purge ^register (<extension> <before_date>)... [--^repository=<rep_spec>]

Options:

    --^repository          Repository where the purge is intended to be applied.
                          It is not necessary within a workspace path but to pick a different one.
    extension             Extension of the file type to be purged. Note that revisions of renamed
                          items will also be considered for its removal.
    before_date           Only revisions older than this date will be considered for its removal.

== CMD_HELP_PURGE_REGISTER ==
Examples:

    cm ^purge ^register ".mkv" "1955-Nov-05 6:00 AM"
    (registers a purge action over revisions of Matroska files created before a certain date)

    cm ^purge ^register ".mkv" "6:00 AM"
    (you can specify just the date or just the time)

    cm ^purge ^register ".mkv" "6:00Z"
    (you can specify the time in UTC instead of local time)

    cm ^purge ^register ".mkv" "1955-Nov-05 6:00 AM" --^repository=timemachine
    (you can specify a different repository to register the purge)

== CMD_DESCRIPTION_PURGE_EXECUTE ==
Executes a purge action previously registered.

Warning: Purge actions are irreversible. Once they are executed, you will not be able to load purged revisions anymore—either by switching a workspace or when showing branch or changeset differences. Use this under your own responsibility.

Please ensure you check the file history to confirm which revisions/changesets are not relevant. Do not hesitate to use the cm ^purge ^show command prior to the purge execution in order to verify no unexpected revision is selected for its removal.

== CMD_USAGE_PURGE_EXECUTE ==
Usage:
    cm ^purge ^execute <purge_guid> [--^server=<server>]

Options:

    --^server              Allows to specify the server where purge actions
                          will be executed.
    purge_guid             The GUID returned by the "cm ^purge ^register" command.

== CMD_HELP_PURGE_EXECUTE ==
Examples:

    cm ^purge ^execute be5b9145-1bd9-4c43-bd90-f2ff727bbf13
    (Executes a purge given its ID).

    cm ^purge ^execute be5b9145-1bd9-4c43-bd90-f2ff727bbf13 --^server=myorg@cloud
    (You can specify a precise server if you need it).

== CMD_DESCRIPTION_PURGE_SHOW ==
Provides a report of the purge status and contents.

== CMD_USAGE_PURGE_SHOW ==
Usage:
    cm ^purge ^show <purge_guid> [--^verbose | --^server=<server>]

Options:

    --^server              Allows to specify the server where purge actions
                          will be queried.
    --^verbose             Extends the report to include the items and revisions
                          involved in the purge.
    purge_guid            The GUID returned by the "cm ^purge ^register" command.

== CMD_HELP_PURGE_SHOW ==
Examples:

    cm ^purge ^show be5b9145-1bd9-4c43-bd90-f2ff727bbf13
    (Provides a brief report of the purge status)

    cm ^purge ^show be5b9145-1bd9-4c43-bd90-f2ff727bbf13 --^verbose
    (Provides additional information per purged extension,
    including items and revisions)

    cm ^purge ^show be5b9145-1bd9-4c43-bd90-f2ff727bbf13 --^server=myorg@cloud
    (You can specify a different server if you wish)

== CMD_DESCRIPTION_PURGE_HISTORY ==
Allows the user to check the state of all the purges registed in the server at some point.

Also, it is useful to get information about them that are preserved for query purposes, such as the author, the date of execution or the storage size and the types affected by the purge.

== CMD_USAGE_PURGE_HISTORY ==
Usage:
    cm ^purge ^history [--^verbose | --^server=<server>]
                     [--^sort=(^desc|^asc)]
                     [--^skip=<skip> | --^limit=<limit>]

Options:

    --^server              Allows to specify the server where purge actions
                          will be queried.
    --^verbose             By default, only the ID and the status is shown per
                          purge. This option includes more detailed information.
    --^sort                Specifies an ordering for purges shown in the result.
                          It can be descending (^desc) or ascending (^asc).
    --^skip                Specifies a number of entries to skip so they are not
                          reflected in the result.
    --^limit               Specifies the maximum number of entries to show at once.

== CMD_HELP_PURGE_HISTORY ==
Examples:

    cm ^purge ^history
    (Shows the ID and status for all purges ever registered in the server).

    cm ^purge ^history --^server=stoltz@cloud
    (You can override the server to use different one if needed).

    cm ^purge ^history --^verbose
    (Includes more detailed data associated to the shown purges).

    cm ^purge ^history --^sort=^asc
    (Shows the history starting from the oldest purges).

    cm ^purge ^history --^skip=0  --^limit=20
    cm ^purge ^history --^skip=20 --^limit=20
    cm ^purge ^history --^skip=40 --^limit=20
    (Instead of showing everything at once, you can paginate results).

== CMD_DESCRIPTION_PURGE_UNREGISTER ==
Purge actions can be deleted from the registry if you decide to not run them.
Remember that it is not possible to unregister purges that were already executed.

== CMD_USAGE_PURGE_UNREGISTER ==
Usage:
    cm ^purge ^unregister <purge_guid> [--^server=<server>]

Options:

    --^server              Allows to specify the server where purge actions
                          will be queried.
    purge_guid            The GUID returned by the "cm ^purge ^register" command.

== CMD_HELP_PURGE_UNREGISTER ==
Examples:

    cm ^purge ^unregister 545ec81b-23ea-462c-91f4-d7c62a6e8817
    (purges that were not executed can be deleted from the registry)

    cm ^purge ^unregister 545ec81b-23ea-462c-91f4-d7c62a6e8817 --^server=myorg@cloud
    (you can specify a different server to unregister the purge)
