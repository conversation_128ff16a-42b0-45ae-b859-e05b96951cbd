# Get started with an existing Unity Version Control repository

Suppose you want to start working on a Unity project in an existing Unity Version Control repository and already have a Unity Version Control account linked to your Unity ID. In that case, you will be able to open the project straight from the **Unity Hub**. A workspace will automatically be created for your project on your machine.

1. In the Unity Hub v3 Beta, click **Open** > **Open remote project** to see the list of your Unity Version Control repositories that contain a Unity project.
2. Click the project and click **Next**.
3. Click the Editor version and platform and click the **change version** button.
4. In the Editor pop-up, click the **Migrate** button to migrate your local workspace to a Unity Version Control workspace
5. Once the migration is completed, click the **Open Unity Version Control** button.

![Plastic Hub](images/plasticHub.gif)

## Accessing the Unity Version Control Window

You can access the **Unity Version Control** window in the Unity Editor by clicking **Window** &gt; **Unity Version Control**.
