{
    "dotnet.defaultSolution": "SquadMate-AI.sln",
    "omnisharp.useModernNet": true,
    "omnisharp.enableRoslynAnalyzers": true,
    "omnisharp.enableEditorConfigSupport": true,
    
    // Unity 6 specific settings
    "unity.enableCodeLens": true,
    "unity.enableDebugCodeLens": true,
    "unity.logLevel": "info",
    
    // File exclusions for better performance
    "files.exclude": {
        "**/.git": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/*.meta": true,
        "**/Library": true,
        "**/Temp": true,
        "**/Obj": true,
        "**/Build": true,
        "**/Builds": true,
        "**/Logs": true,
        "**/UserSettings": true,
        "**/ProjectSettings/Packages": true
    },
    
    // Search exclusions
    "search.exclude": {
        "**/Library": true,
        "**/Temp": true,
        "**/Obj": true,
        "**/Build": true,
        "**/Builds": true,
        "**/Logs": true,
        "**/UserSettings": true,
        "**/results": true,
        "**/*.meta": true
    },
    
    // File associations
    "files.associations": {
        "*.cs": "csharp",
        "*.yaml": "yaml",
        "*.yml": "yaml",
        "*.asmdef": "json",
        "*.asmref": "json"
    },
    
    // Python settings for ML-Agents
    "python.defaultInterpreterPath": "python",
    "python.terminal.activateEnvironment": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    
    // YAML schema for ML-Agents configs
    "yaml.schemas": {
        "https://raw.githubusercontent.com/Unity-Technologies/ml-agents/main/config/trainer_config_schema.json": [
            "config/*.yaml",
            "config/*.yml"
        ]
    },
    
    // Editor settings
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    "editor.rulers": [80, 120],
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 120,
    
    // C# specific settings
    "csharp.format.enable": true,
    "csharp.semanticHighlighting.enabled": true,
    "csharp.inlayHints.enableInlayHintsForParameters": true,
    "csharp.inlayHints.enableInlayHintsForLiteralParameters": true,
    "csharp.inlayHints.enableInlayHintsForIndexerParameters": true,
    "csharp.inlayHints.enableInlayHintsForObjectCreationParameters": true,
    "csharp.inlayHints.enableInlayHintsForOtherParameters": true,
    "csharp.inlayHints.enableInlayHintsForTypes": true,
    "csharp.inlayHints.enableInlayHintsForImplicitVariableTypes": true,
    "csharp.inlayHints.enableInlayHintsForLambdaParameterTypes": true,
    "csharp.inlayHints.enableInlayHintsForImplicitObjectCreation": true,
    
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.cwd": "${workspaceFolder}",
    
    // Git settings
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    
    // ML-Agents specific settings
    "files.watcherExclude": {
        "**/results/**": true,
        "**/models/**": true,
        "**/summaries/**": true,
        "**/Library/**": true,
        "**/Temp/**": true
    },
    
    // Performance settings
    "typescript.preferences.includePackageJsonAutoImports": "off",
    "extensions.ignoreRecommendations": false,
    
    // Unity 6 enhanced features
    "unity.enableAsyncStackTraces": true,
    "unity.enableManagedDebugging": true,
    "unity.enableNativeDebugging": false,
    
    // Workspace trust
    "security.workspace.trust.untrustedFiles": "open"
}
