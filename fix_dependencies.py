#!/usr/bin/env python3
"""
Dependency Fix Script for PUBG SquadMate AI
Handles Python 3.12+ compatibility issues with ML-Agents
"""

import sys
import subprocess
import platform

def get_python_version():
    """Get current Python version"""
    return sys.version_info

def install_with_prebuilt():
    """Install packages using pre-built wheels"""
    print("🔧 Installing with pre-built packages...")
    
    commands = [
        # Install numpy first with pre-built wheel
        ["pip", "install", "--only-binary=all", "numpy"],
        
        # Install ML-Agents with pre-built wheels
        ["pip", "install", "--only-binary=all", "mlagents"],
        
        # Install PyTorch CPU version (smaller and faster for training)
        ["pip", "install", "torch", "torchvision", "--index-url", "https://download.pytorch.org/whl/cpu"],
        
        # Install TensorBoard
        ["pip", "install", "tensorboard"],
        
        # Install additional dependencies
        ["pip", "install", "--only-binary=all", "protobuf", "grpcio"]
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ Success")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed: {e}")
            print(f"Error output: {e.stderr}")
            return False
    
    return True

def install_alternative():
    """Try alternative installation methods"""
    print("🔄 Trying alternative installation...")
    
    # Try installing specific versions that are known to work
    commands = [
        ["pip", "install", "numpy==1.24.3"],
        ["pip", "install", "mlagents==0.28.0", "--no-deps"],
        ["pip", "install", "torch==2.0.1", "torchvision==0.15.2", "--index-url", "https://download.pytorch.org/whl/cpu"],
        ["pip", "install", "tensorboard==2.13.0"],
        ["pip", "install", "protobuf==3.20.3"],
        ["pip", "install", "grpcio==1.48.2"],
        ["pip", "install", "h5py==3.7.0"],
        ["pip", "install", "mlagents-envs==0.28.0"]
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ Success")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Warning: {e}")
            # Continue with other packages
    
    return True

def verify_installation():
    """Verify that all packages are installed correctly"""
    print("🔍 Verifying installation...")
    
    packages_to_test = [
        ("mlagents", "import mlagents"),
        ("torch", "import torch; print(f'PyTorch version: {torch.__version__}')"),
        ("tensorboard", "import tensorboard"),
        ("numpy", "import numpy; print(f'NumPy version: {numpy.__version__}')"),
        ("h5py", "import h5py"),
        ("grpcio", "import grpc")
    ]
    
    all_good = True
    for package_name, test_code in packages_to_test:
        try:
            result = subprocess.run([sys.executable, "-c", test_code], 
                                  check=True, capture_output=True, text=True)
            print(f"✅ {package_name}: OK")
            if result.stdout.strip():
                print(f"   {result.stdout.strip()}")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_name}: FAILED")
            print(f"   Error: {e.stderr.strip()}")
            all_good = False
    
    return all_good

def create_simple_trainer():
    """Create a simplified training script that works without full ML-Agents"""
    print("📝 Creating simplified trainer...")
    
    simple_trainer = '''#!/usr/bin/env python3
"""
Simplified SquadMate Trainer
Works even if ML-Agents installation has issues
"""

import sys
import os

def check_unity_connection():
    """Check if Unity is ready for training"""
    print("🎮 Unity ML-Agents Training")
    print("=" * 40)
    print("📋 Manual Training Steps:")
    print("1. Open Unity with your SquadMate project")
    print("2. Open the TrainingEnvironment scene")
    print("3. Make sure the SquadMate has Behavior Parameters component")
    print("4. Set Behavior Type to 'Default' (not Inference Only)")
    print("5. Press Play in Unity")
    print("6. The agent should start learning automatically")
    print()
    print("📊 Monitor training progress:")
    print("- Watch the agent's behavior in Unity")
    print("- Check the Console for reward values")
    print("- Training typically takes 2-4 hours for basic behavior")
    print()
    print("💡 Tips:")
    print("- Increase Time Scale in Unity for faster training")
    print("- The agent will save automatically to results/ folder")
    print("- Look for .onnx files when training completes")

if __name__ == "__main__":
    check_unity_connection()
'''
    
    with open("simple_trainer.py", "w") as f:
        f.write(simple_trainer)
    
    print("✅ Created simple_trainer.py")

def main():
    print("🔧 PUBG SquadMate AI - Dependency Fix Tool")
    print("=" * 50)
    
    # Check Python version
    version = get_python_version()
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 12):
        print("⚠️  Python 3.12+ detected - using compatibility mode")
        success = install_with_prebuilt()
        if not success:
            print("🔄 Trying alternative installation...")
            success = install_alternative()
    else:
        print("✅ Python version is compatible")
        success = install_with_prebuilt()
    
    print("\n" + "=" * 50)
    
    # Verify installation
    if verify_installation():
        print("🎉 All packages installed successfully!")
        print("\n📋 Next steps:")
        print("1. Run: python train_squadmate.py --validate")
        print("2. Open Unity and set up your training scene")
        print("3. Start training with: python train_squadmate.py")
    else:
        print("⚠️  Some packages failed to install properly")
        print("🔧 Creating simplified trainer as backup...")
        create_simple_trainer()
        print("\n📋 Alternative approach:")
        print("1. Use Unity's built-in ML-Agents training")
        print("2. Run: python simple_trainer.py for instructions")
        print("3. Train directly in Unity without external scripts")
    
    print("\n🆘 If you still have issues:")
    print("1. Try installing Python 3.10 or 3.11")
    print("2. Use Anaconda/Miniconda instead of pip")
    print("3. Use Unity's Package Manager to install ML-Agents")

if __name__ == "__main__":
    main()
