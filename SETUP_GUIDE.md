# 🎮 PUBG SquadMate AI - Complete Setup Guide

This guide will walk you through setting up and training your PUBG-style AI SquadMate agent using Unity ML-Agents.

## 📋 Prerequisites

### Software Requirements
- **Unity 2022.3 LTS** or newer
- **Python 3.9-3.11** (⚠️ Python 3.12+ has compatibility issues)
- **Git** (for cloning repositories)

### Hardware Requirements
- **GPU**: NVIDIA GTX 1060 or better (recommended for faster training)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 5GB free space

## 🚀 Step 1: Install Dependencies

### Install Python Packages

⚠️ **IMPORTANT**: If you have Python 3.12+ (like 3.13), you need to use pre-built packages:

#### Option A: Use Pre-built Packages (Recommended for Python 3.12+)
```bash
# Install with pre-built wheels to avoid compilation issues
pip install --only-binary=all mlagents
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
pip install tensorboard
```

#### Option B: Standard Installation (Python 3.9-3.11)
```bash
pip install mlagents
pip install torch torchvision
pip install tensorboard
```

#### Option C: If Still Having Issues
```bash
# Use conda instead of pip
conda install -c conda-forge mlagents
conda install pytorch torchvision cpuonly -c pytorch
conda install tensorboard
```

### Verify Installation
```bash
python train_squadmate.py --validate
```

## 🎯 Step 2: Unity Project Setup

### 1. Create New Unity Project
1. Open Unity Hub
2. Create new **3D Core** project
3. Name it "SquadMate-AI"

### 2. Import ML-Agents Package
1. Open **Window > Package Manager**
2. Click **+ > Add package from git URL**
3. Enter: `com.unity.ml-agents`
4. Click **Add**

### 3. Import Project Files
1. Copy all files from this repository to your Unity project
2. The structure should look like:
```
Assets/
├── Scripts/
│   ├── Agents/
│   ├── Controllers/
│   ├── Environment/
│   └── Utils/
├── Data/
└── Scenes/
```

## 🏗️ Step 3: Create Training Scene

### 1. Create Basic Environment
1. **Create Empty GameObject** → Name it "Environment"
2. **Add Component** → `GameEnvironment` script
3. **Create Terrain** → GameObject > 3D Object > Terrain
4. **Size the terrain** to 50x50 units

### 2. Create Player
1. **Create Capsule** → Name it "Player"
2. **Add Components**:
   - `Rigidbody`
   - `PlayerController` script
3. **Position** at (0, 1, 0)

### 3. Create SquadMate Agent
1. **Create Capsule** → Name it "SquadMate"
2. **Add Components**:
   - `Rigidbody`
   - `SquadMateAgent` script
   - `RewardCalculator` script
   - `SquadMateDecisionTree` script
   - `Behavior Parameters` (ML-Agents)
3. **Configure Behavior Parameters**:
   - Behavior Name: "SquadMate"
   - Vector Observation Space: 22
   - Continuous Actions: 3
   - Discrete Actions: 5
   - Behavior Type: Default

### 4. Create Spawn Points
1. **Create Empty GameObjects** for spawn points
2. **Position them** around the environment
3. **Assign to Environment** script's spawn points array

### 5. Create Prefabs
Create prefabs for:
- **Enemy** (Capsule with `EnemyController`)
- **Medkit** (Cube with `MedkitPickup`)
- **Weapon** (Cylinder with `WeaponPickup`)

### 6. Configure Environment
1. **Select Environment GameObject**
2. **Assign all prefabs** to the `GameEnvironment` script
3. **Set environment size** to (50, 0, 50)

## 🧠 Step 4: Training Configuration

### 1. Verify Config File
Check that `config/squadmate_config.yaml` exists and contains proper settings.

### 2. Test Scene Setup
1. **Press Play** in Unity
2. **Verify** that objects spawn correctly
3. **Check Console** for any errors

## 🏃‍♂️ Step 5: Start Training

### 1. Start Training Process
```bash
python train_squadmate.py --run-id squadmate_v1 --tensorboard
```

### 2. Connect Unity
1. **Press Play** in Unity when prompted
2. **Wait for connection** message in console
3. **Training should begin** automatically

### 3. Monitor Progress
- **Console**: Real-time training logs
- **TensorBoard**: http://localhost:6006
- **Unity**: Watch agent behavior in real-time

## 📊 Step 6: Monitor Training

### Key Metrics to Watch
- **Cumulative Reward**: Should increase over time
- **Episode Length**: Should stabilize
- **Policy Loss**: Should decrease
- **Value Loss**: Should decrease

### Training Phases
1. **Exploration** (0-100k steps): Random behavior
2. **Learning** (100k-500k steps): Basic behaviors emerge
3. **Refinement** (500k-1M steps): Advanced tactics develop
4. **Mastery** (1M+ steps): Consistent performance

### Expected Training Time
- **Basic Behavior**: 2-4 hours
- **Good Performance**: 8-12 hours
- **Expert Level**: 24-48 hours

## 🎯 Step 7: Test Trained Agent

### 1. Export Model
```bash
python train_squadmate.py --export --run-id squadmate_v1
```

### 2. Use in Unity
1. **Copy** `models/SquadMate.onnx` to Unity project
2. **Select SquadMate** GameObject
3. **Behavior Parameters** → Model: Assign the .onnx file
4. **Behavior Type**: Inference Only
5. **Press Play** to test

## 🔧 Troubleshooting

### Common Issues

#### Training Won't Start
- ✅ Check Unity is in Play mode
- ✅ Verify ML-Agents package is installed
- ✅ Check console for error messages

#### Poor Performance
- ✅ Increase training time
- ✅ Adjust reward values in `RewardCalculator`
- ✅ Modify hyperparameters in config file

#### Agent Doesn't Learn
- ✅ Check observation space size (should be 22)
- ✅ Verify action space configuration
- ✅ Review reward function logic

#### Unity Crashes
- ✅ Reduce environment complexity
- ✅ Lower time scale in config
- ✅ Check for infinite loops in scripts

### Performance Optimization

#### For Faster Training
```yaml
# In squadmate_config.yaml
engine_settings:
  time_scale: 20        # Increase simulation speed
  no_graphics: true     # Disable rendering
  width: 84            # Lower resolution
  height: 84
```

#### For Better Quality
```yaml
# In squadmate_config.yaml
behaviors:
  SquadMate:
    max_steps: 5000000   # Train longer
    network_settings:
      hidden_units: 512  # Larger network
      num_layers: 4
```

## 🎮 Advanced Features

### 1. Decision Tree Integration
The agent uses a hybrid approach:
- **ML-Agents**: Low-level motor control
- **Decision Tree**: High-level strategy

Modify `Assets/Data/decision_tree.json` to adjust behavior priorities.

### 2. Custom Rewards
Edit `RewardCalculator.cs` to add new reward functions:
```csharp
public void OnCustomEvent(SquadMateAgent agent)
{
    agent.AddReward(customRewardValue);
}
```

### 3. Multi-Agent Training
For team-based scenarios, modify the config to support multiple agents:
```yaml
behaviors:
  SquadMate:
    self_play:
      save_steps: 50000
      team_change: 100000
```

## 📈 Next Steps

1. **Experiment** with different reward functions
2. **Add voice commands** for player interaction
3. **Implement** more complex scenarios
4. **Train specialized** agents for different roles
5. **Deploy** to actual game environments

## 🆘 Getting Help

- **Unity ML-Agents Docs**: https://github.com/Unity-Technologies/ml-agents
- **Discord Community**: Unity ML-Agents Discord
- **GitHub Issues**: Report bugs and feature requests

## 🎉 Success Indicators

Your agent is ready when it can:
- ✅ Follow the player in formation
- ✅ Revive downed teammates quickly
- ✅ Engage enemies effectively
- ✅ Seek health and weapons when needed
- ✅ Make tactical decisions independently

Happy training! 🚀
