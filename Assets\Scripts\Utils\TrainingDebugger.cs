using UnityEngine;
using Unity.MLAgents.Policies;

/// <summary>
/// Debug component to monitor training progress and display information
/// Attach this to any GameObject to see training status
/// </summary>
public class TrainingDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool showDebugInfo = true;
    public bool logRewards = true;
    public bool showGUI = true;
    public float updateInterval = 1f;
    
    [Header("References")]
    public SquadMateAgent squadMateAgent;
    public PlayerController playerController;
    public GameEnvironment gameEnvironment;
    
    private float lastUpdateTime;
    private int frameCount;
    private float totalRewards;
    private string statusMessage = "Initializing...";
    
    void Start()
    {
        // Auto-find components if not assigned
        if (squadMateAgent == null)
            squadMateAgent = FindObjectOfType<SquadMateAgent>();
        
        if (playerController == null)
            playerController = FindObjectOfType<PlayerController>();
        
        if (gameEnvironment == null)
            gameEnvironment = FindObjectOfType<GameEnvironment>();
        
        Debug.Log("🔍 Training Debugger Started");
        Debug.Log($"SquadMate Agent: {(squadMateAgent != null ? "Found" : "Missing")}");
        Debug.Log($"Player Controller: {(playerController != null ? "Found" : "Missing")}");
        Debug.Log($"Game Environment: {(gameEnvironment != null ? "Found" : "Missing")}");
        
        InvokeRepeating(nameof(UpdateStatus), 1f, updateInterval);
    }
    
    void Update()
    {
        frameCount++;
        
        if (showDebugInfo && Time.time - lastUpdateTime > updateInterval)
        {
            CheckTrainingStatus();
            lastUpdateTime = Time.time;
        }
    }
    
    void UpdateStatus()
    {
        if (squadMateAgent == null)
        {
            statusMessage = "❌ SquadMate Agent Missing";
            return;
        }
        
        BehaviorParameters behaviorParams = squadMateAgent.GetComponent<BehaviorParameters>();
        if (behaviorParams == null)
        {
            statusMessage = "❌ Behavior Parameters Missing";
            return;
        }
        
        if (behaviorParams.BehaviorType == BehaviorType.Default)
        {
            statusMessage = "🧠 Training Mode Active";
        }
        else if (behaviorParams.BehaviorType == BehaviorType.InferenceOnly)
        {
            statusMessage = "🤖 Inference Mode (Trained Model)";
        }
        else
        {
            statusMessage = "🎮 Heuristic Mode";
        }
        
        // Check if agent is actually learning
        if (Application.isPlaying)
        {
            statusMessage += " - Running";
        }
    }
    
    void CheckTrainingStatus()
    {
        if (squadMateAgent == null) return;
        
        // Log basic status
        Debug.Log($"🎮 Training Status: {statusMessage}");
        Debug.Log($"📊 Frame: {frameCount}, Time: {Time.time:F1}s");
        
        // Check agent state
        if (squadMateAgent.currentState != SquadMateAgent.AgentState.Idle)
        {
            Debug.Log($"🤖 Agent State: {squadMateAgent.currentState}");
        }
        
        // Check player status
        if (playerController != null)
        {
            Debug.Log($"👤 Player Health: {playerController.currentHealth:F1}, Downed: {playerController.isDowned}");
        }
        
        // Check environment
        if (gameEnvironment != null)
        {
            Transform[] enemies = gameEnvironment.GetEnemies();
            Transform[] medkits = gameEnvironment.GetMedkits();
            Transform[] weapons = gameEnvironment.GetWeapons();
            
            Debug.Log($"🌍 Environment - Enemies: {enemies.Length}, Medkits: {medkits.Length}, Weapons: {weapons.Length}");
        }
    }
    
    void OnGUI()
    {
        if (!showGUI) return;
        
        // Create a debug panel
        GUILayout.BeginArea(new Rect(10, 10, 300, 400));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🎮 SquadMate Training Debug", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
        GUILayout.Space(10);
        
        // Status
        GUILayout.Label($"Status: {statusMessage}");
        GUILayout.Label($"Frame: {frameCount}");
        GUILayout.Label($"Time: {Time.time:F1}s");
        GUILayout.Space(10);
        
        // Agent info
        if (squadMateAgent != null)
        {
            GUILayout.Label("🤖 SquadMate Agent:");
            GUILayout.Label($"  State: {squadMateAgent.currentState}");
            GUILayout.Label($"  Health: {squadMateAgent.currentHealth:F1}");
            GUILayout.Label($"  Has Weapon: {squadMateAgent.hasWeapon}");
            
            BehaviorParameters behaviorParams = squadMateAgent.GetComponent<BehaviorParameters>();
            if (behaviorParams != null)
            {
                GUILayout.Label($"  Behavior: {behaviorParams.BehaviorName}");
                GUILayout.Label($"  Type: {behaviorParams.BehaviorType}");
            }
        }
        else
        {
            GUILayout.Label("❌ SquadMate Agent: Missing");
        }
        
        GUILayout.Space(10);
        
        // Player info
        if (playerController != null)
        {
            GUILayout.Label("👤 Player:");
            GUILayout.Label($"  Health: {playerController.currentHealth:F1}");
            GUILayout.Label($"  Downed: {playerController.isDowned}");
            GUILayout.Label($"  AI Control: {playerController.useAIControl}");
        }
        else
        {
            GUILayout.Label("❌ Player: Missing");
        }
        
        GUILayout.Space(10);
        
        // Environment info
        if (gameEnvironment != null)
        {
            GUILayout.Label("🌍 Environment:");
            Transform[] enemies = gameEnvironment.GetEnemies();
            Transform[] medkits = gameEnvironment.GetMedkits();
            Transform[] weapons = gameEnvironment.GetWeapons();
            
            GUILayout.Label($"  Enemies: {enemies.Length}");
            GUILayout.Label($"  Medkits: {medkits.Length}");
            GUILayout.Label($"  Weapons: {weapons.Length}");
        }
        else
        {
            GUILayout.Label("❌ Environment: Missing");
        }
        
        GUILayout.Space(10);
        
        // Control buttons
        if (GUILayout.Button("🔄 Restart Episode"))
        {
            if (squadMateAgent != null)
            {
                squadMateAgent.EndEpisode();
                Debug.Log("🔄 Episode restarted manually");
            }
        }
        
        if (GUILayout.Button("💊 Heal Player"))
        {
            if (playerController != null)
            {
                playerController.Heal(50f);
                Debug.Log("💊 Player healed manually");
            }
        }
        
        if (GUILayout.Button("⚔️ Spawn Enemy"))
        {
            if (gameEnvironment != null)
            {
                // Try to spawn an enemy
                Vector3 spawnPos = gameEnvironment.GetRandomPosition();
                Debug.Log($"⚔️ Attempting to spawn enemy at {spawnPos}");
            }
        }
        
        GUILayout.Space(10);
        
        // Training tips
        GUILayout.Label("💡 Training Tips:");
        GUILayout.Label("• Set Time Scale to 5-15x");
        GUILayout.Label("• Watch Console for rewards");
        GUILayout.Label("• Training takes 1-4 hours");
        GUILayout.Label("• Agent learns gradually");
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
    
    // Method to be called when rewards are given
    public void OnRewardGiven(float reward)
    {
        if (logRewards)
        {
            totalRewards += reward;
            Debug.Log($"💰 Reward: {reward:F3}, Total: {totalRewards:F2}");
        }
    }
    
    // Method to test if everything is working
    [ContextMenu("Test Training Setup")]
    public void TestTrainingSetup()
    {
        Debug.Log("🧪 Testing Training Setup...");
        
        if (squadMateAgent == null)
        {
            Debug.LogError("❌ SquadMate Agent is missing!");
            return;
        }
        
        if (playerController == null)
        {
            Debug.LogError("❌ Player Controller is missing!");
            return;
        }
        
        if (gameEnvironment == null)
        {
            Debug.LogError("❌ Game Environment is missing!");
            return;
        }
        
        // Test reward system
        squadMateAgent.AddReward(0.1f);
        Debug.Log("✅ Reward system working");
        
        // Test environment
        Vector3 randomPos = gameEnvironment.GetRandomPosition();
        Debug.Log($"✅ Environment working - Random position: {randomPos}");
        
        Debug.Log("🎉 Training setup test completed successfully!");
    }
}
