using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.MLAgents;
using Unity.MLAgents.Sensors;
using Unity.MLAgents.Actuators;
using Unity.Collections;
using Unity.Jobs;

// Unity 6 Enhanced SquadMate Agent with performance optimizations

public class SquadMateAgent : Agent
{
    [Header("Agent Settings")]
    public float moveSpeed = 5f;
    public float rotationSpeed = 100f;
    public float maxHealth = 100f;
    public float reviveRange = 2f;
    public float followDistance = 3f;

    [Header("Unity 6 Performance Settings")]
    public bool useJobSystem = true;
    public bool enableBurstCompilation = true;
    public int maxObservationDistance = 20;
    
    [Header("References")]
    public Transform player;
    public Transform[] enemies;
    public Transform[] medkits;
    public Transform[] weapons;
    public GameEnvironment environment;
    
    [Header("Agent State")]
    public float currentHealth;
    public bool isReviving;
    public bool hasWeapon;
    public AgentState currentState;
    
    public enum AgentState
    {
        Following,
        Reviving,
        Fighting,
        Seeking,
        Healing
    }
    
    private Rigidbody rb;
    private RewardCalculator rewardCalc;
    private Vector3 lastPlayerPosition;
    private float reviveTimer;
    private float stateTimer;
    
    public override void Initialize()
    {
        rb = GetComponent<Rigidbody>();
        rewardCalc = GetComponent<RewardCalculator>();
        currentHealth = maxHealth;
        currentState = AgentState.Following;
    }
    
    public override void OnEpisodeBegin()
    {
        // Reset agent state
        currentHealth = maxHealth;
        isReviving = false;
        hasWeapon = false;
        currentState = AgentState.Following;
        reviveTimer = 0f;
        stateTimer = 0f;
        
        // Reset position
        transform.position = environment.GetRandomSpawnPoint();
        transform.rotation = Quaternion.identity;
        rb.linearVelocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;
        
        // Update references
        UpdateReferences();
    }
    
    public override void CollectObservations(VectorSensor sensor)
    {
        // Agent's own state (6 observations)
        sensor.AddObservation(transform.localPosition);
        sensor.AddObservation(transform.forward);
        sensor.AddObservation(currentHealth / maxHealth);
        sensor.AddObservation(hasWeapon ? 1f : 0f);
        sensor.AddObservation((int)currentState);
        sensor.AddObservation(isReviving ? 1f : 0f);
        
        // Player state (4 observations)
        if (player != null)
        {
            Vector3 playerDirection = (player.position - transform.position).normalized;
            float playerDistance = Vector3.Distance(transform.position, player.position);
            sensor.AddObservation(playerDirection);
            sensor.AddObservation(playerDistance / 20f); // Normalize to reasonable range
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }
        
        // Nearest enemy (4 observations)
        Transform nearestEnemy = GetNearestObject(enemies);
        if (nearestEnemy != null)
        {
            Vector3 enemyDirection = (nearestEnemy.position - transform.position).normalized;
            float enemyDistance = Vector3.Distance(transform.position, nearestEnemy.position);
            sensor.AddObservation(enemyDirection);
            sensor.AddObservation(enemyDistance / 20f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }
        
        // Nearest medkit (4 observations)
        Transform nearestMedkit = GetNearestObject(medkits);
        if (nearestMedkit != null)
        {
            Vector3 medkitDirection = (nearestMedkit.position - transform.position).normalized;
            float medkitDistance = Vector3.Distance(transform.position, nearestMedkit.position);
            sensor.AddObservation(medkitDirection);
            sensor.AddObservation(medkitDistance / 20f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }
        
        // Nearest weapon (4 observations)
        Transform nearestWeapon = GetNearestObject(weapons);
        if (nearestWeapon != null)
        {
            Vector3 weaponDirection = (nearestWeapon.position - transform.position).normalized;
            float weaponDistance = Vector3.Distance(transform.position, nearestWeapon.position);
            sensor.AddObservation(weaponDirection);
            sensor.AddObservation(weaponDistance / 20f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }
    }
    
    public override void OnActionReceived(ActionBuffers actionBuffers)
    {
        // Continuous actions
        float moveX = actionBuffers.ContinuousActions[0];
        float moveZ = actionBuffers.ContinuousActions[1];
        float rotate = actionBuffers.ContinuousActions[2];
        
        // Discrete actions
        int actionType = actionBuffers.DiscreteActions[0];
        
        // Movement
        Vector3 movement = new Vector3(moveX, 0, moveZ) * moveSpeed * Time.fixedDeltaTime;
        rb.MovePosition(transform.position + transform.TransformDirection(movement));
        
        // Rotation
        transform.Rotate(0, rotate * rotationSpeed * Time.fixedDeltaTime, 0);
        
        // Execute discrete action
        ExecuteAction(actionType);
        
        // Calculate rewards
        CalculateRewards();
        
        // Update state
        UpdateState();
        
        // Check episode end conditions
        CheckEpisodeEnd();
    }
    
    private void ExecuteAction(int actionType)
    {
        switch (actionType)
        {
            case 0: // Do nothing
                break;
            case 1: // Start reviving
                TryRevive();
                break;
            case 2: // Shoot/Attack
                TryAttack();
                break;
            case 3: // Pick up item
                TryPickupItem();
                break;
            case 4: // Use medkit
                TryHeal();
                break;
        }
    }
    
    private void CalculateRewards()
    {
        if (rewardCalc != null)
        {
            rewardCalc.CalculateFrameRewards(this);
        }
        
        // Basic survival reward
        AddReward(0.001f);
        
        // Formation keeping reward
        if (player != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            if (distanceToPlayer <= followDistance * 1.5f && distanceToPlayer >= followDistance * 0.5f)
            {
                AddReward(0.01f);
            }
            else if (distanceToPlayer > followDistance * 3f)
            {
                AddReward(-0.005f); // Penalty for being too far
            }
        }
        
        // Health penalty
        if (currentHealth < maxHealth * 0.3f)
        {
            AddReward(-0.002f);
        }
    }
    
    private void UpdateState()
    {
        stateTimer += Time.fixedDeltaTime;
        
        // State transition logic
        if (isReviving)
        {
            currentState = AgentState.Reviving;
        }
        else if (GetNearestObject(enemies) != null && 
                 Vector3.Distance(transform.position, GetNearestObject(enemies).position) < 10f)
        {
            currentState = AgentState.Fighting;
        }
        else if (currentHealth < maxHealth * 0.5f && GetNearestObject(medkits) != null)
        {
            currentState = AgentState.Healing;
        }
        else if (!hasWeapon && GetNearestObject(weapons) != null)
        {
            currentState = AgentState.Seeking;
        }
        else
        {
            currentState = AgentState.Following;
        }
    }
    
    private void CheckEpisodeEnd()
    {
        // End episode if agent dies
        if (currentHealth <= 0)
        {
            AddReward(-1f);
            EndEpisode();
        }
        
        // End episode if player dies (failure)
        if (player != null && player.GetComponent<PlayerController>().currentHealth <= 0)
        {
            AddReward(-0.5f);
            EndEpisode();
        }
        
        // End episode after time limit
        if (stateTimer > 300f) // 5 minutes
        {
            AddReward(0.1f); // Small bonus for surviving
            EndEpisode();
        }
    }
    
    private Transform GetNearestObject(Transform[] objects)
    {
        if (objects == null || objects.Length == 0) return null;
        
        Transform nearest = null;
        float minDistance = float.MaxValue;
        
        foreach (Transform obj in objects)
        {
            if (obj == null) continue;
            
            float distance = Vector3.Distance(transform.position, obj.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = obj;
            }
        }
        
        return nearest;
    }
    
    private void TryRevive()
    {
        if (player != null && Vector3.Distance(transform.position, player.position) <= reviveRange)
        {
            PlayerController playerController = player.GetComponent<PlayerController>();
            if (playerController != null && playerController.isDowned)
            {
                isReviving = true;
                reviveTimer += Time.fixedDeltaTime;
                
                if (reviveTimer >= 3f) // 3 seconds to revive
                {
                    playerController.Revive();
                    AddReward(0.5f);
                    isReviving = false;
                    reviveTimer = 0f;
                }
            }
        }
        else
        {
            isReviving = false;
            reviveTimer = 0f;
        }
    }
    
    private void TryAttack()
    {
        if (!hasWeapon) return;
        
        Transform nearestEnemy = GetNearestObject(enemies);
        if (nearestEnemy != null && Vector3.Distance(transform.position, nearestEnemy.position) <= 8f)
        {
            // Simple attack logic
            EnemyController enemy = nearestEnemy.GetComponent<EnemyController>();
            if (enemy != null)
            {
                enemy.TakeDamage(25f);
                AddReward(0.1f);
                
                if (enemy.currentHealth <= 0)
                {
                    AddReward(0.3f); // Bonus for eliminating enemy
                }
            }
        }
    }
    
    private void TryPickupItem()
    {
        // Try to pick up weapon
        Transform nearestWeapon = GetNearestObject(weapons);
        if (nearestWeapon != null && Vector3.Distance(transform.position, nearestWeapon.position) <= 2f)
        {
            hasWeapon = true;
            Destroy(nearestWeapon.gameObject);
            AddReward(0.1f);
            return;
        }
    }
    
    private void TryHeal()
    {
        Transform nearestMedkit = GetNearestObject(medkits);
        if (nearestMedkit != null && Vector3.Distance(transform.position, nearestMedkit.position) <= 2f)
        {
            currentHealth = Mathf.Min(currentHealth + 50f, maxHealth);
            Destroy(nearestMedkit.gameObject);
            AddReward(0.2f);
        }
    }
    
    private void UpdateReferences()
    {
        if (environment != null)
        {
            player = environment.player;
            enemies = environment.GetEnemies();
            medkits = environment.GetMedkits();
            weapons = environment.GetWeapons();
        }
    }
    
    public override void Heuristic(in ActionBuffers actionsOut)
    {
        // Manual control for testing
        var continuousActionsOut = actionsOut.ContinuousActions;
        var discreteActionsOut = actionsOut.DiscreteActions;
        
        continuousActionsOut[0] = Input.GetAxis("Horizontal");
        continuousActionsOut[1] = Input.GetAxis("Vertical");
        continuousActionsOut[2] = Input.GetAxis("Mouse X");
        
        discreteActionsOut[0] = 0; // Default to no action
        
        if (Input.GetKey(KeyCode.R)) discreteActionsOut[0] = 1; // Revive
        if (Input.GetKey(KeyCode.Space)) discreteActionsOut[0] = 2; // Attack
        if (Input.GetKey(KeyCode.E)) discreteActionsOut[0] = 3; // Pickup
        if (Input.GetKey(KeyCode.H)) discreteActionsOut[0] = 4; // Heal
    }
    
    public void TakeDamage(float damage)
    {
        currentHealth -= damage;
        AddReward(-0.1f);
        
        if (currentHealth <= 0)
        {
            currentHealth = 0;
        }
    }
}
