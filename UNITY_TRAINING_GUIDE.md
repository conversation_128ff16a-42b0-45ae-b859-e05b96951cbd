# 🎮 Unity-Based Training Guide for SquadMate AI

Since you have Python 3.13 (which has some compatibility issues with ML-Agents CLI), we'll use Unity's built-in training capabilities. This is actually **easier and more reliable**!

## ✅ **What's Working**
- ✅ ML-Agents core modules are installed
- ✅ PyTorch 2.7.1 is working
- ✅ All Unity scripts are ready
- ✅ Training can happen directly in Unity

## 🚀 **Step-by-Step Unity Training**

### **Step 1: Unity Project Setup**

1. **Open Unity 2022.3 LTS**
2. **Create new 3D project** named "SquadMate-AI"
3. **Install ML-Agents Package**:
   - Window → Package Manager
   - Click "+" → Add package from git URL
   - Enter: `com.unity.ml-agents`
   - Click Add

### **Step 2: Import Scripts**

1. **Copy all scripts** from this project to your Unity Assets folder:
   ```
   Assets/
   ├── Scripts/
   │   ├── Agents/
   │   │   ├── SquadMateAgent.cs
   │   │   └── SquadMateDecisionTree.cs
   │   ├── Controllers/
   │   │   ├── PlayerController.cs
   │   │   └── EnemyController.cs
   │   ├── Environment/
   │   │   ├── GameEnvironment.cs
   │   │   └── ObjectSpawner.cs
   │   └── Utils/
   │       └── RewardCalculator.cs
   ```

### **Step 3: Create Training Scene**

#### **A. Basic Environment**
1. **Create Empty GameObject** → Name: "Environment"
2. **Add GameEnvironment script** to it
3. **Create Terrain**: GameObject → 3D Object → Terrain
4. **Resize terrain** to 50x50 units

#### **B. Create Player**
1. **Create Capsule** → Name: "Player"
2. **Add components**:
   - Rigidbody
   - Capsule Collider
   - PlayerController script
3. **Position**: (0, 1, 0)
4. **Scale**: (1, 2, 1)

#### **C. Create SquadMate Agent**
1. **Create Capsule** → Name: "SquadMate"
2. **Add components**:
   - Rigidbody
   - Capsule Collider
   - **SquadMateAgent script**
   - **RewardCalculator script**
   - **SquadMateDecisionTree script**
   - **Behavior Parameters** (ML-Agents)
3. **Position**: (3, 1, 0)
4. **Scale**: (1, 2, 1)

#### **D. Configure Behavior Parameters**
Select SquadMate → Behavior Parameters component:
- **Behavior Name**: "SquadMate"
- **Behavior Type**: "Default"
- **Team ID**: 0
- **Use Child Sensors**: ✓
- **Vector Observation**:
  - **Space Size**: 22
- **Actions**:
  - **Continuous Actions**: 3
  - **Discrete Actions**: 5

#### **E. Create Prefabs**
Create these prefabs in Assets/Prefabs/:

**Enemy Prefab**:
1. Create Capsule → Name: "Enemy"
2. Add: NavMesh Agent, EnemyController script
3. Color: Red material
4. Save as prefab

**Medkit Prefab**:
1. Create Cube → Name: "Medkit"
2. Add: MedkitPickup script, Trigger collider
3. Color: Green material
4. Save as prefab

**Weapon Prefab**:
1. Create Cylinder → Name: "Weapon"
2. Add: WeaponPickup script, Trigger collider
3. Color: Blue material
4. Save as prefab

#### **F. Configure Environment**
Select Environment GameObject:
1. **Assign prefabs** to GameEnvironment script
2. **Set Environment Size**: (50, 0, 50)
3. **Create spawn points** (empty GameObjects) around the scene
4. **Assign spawn points** to the Environment script

### **Step 4: Setup NavMesh**

1. **Select all static geometry** (terrain, walls)
2. **Mark as Navigation Static**: Navigation window → Object tab
3. **Bake NavMesh**: Navigation window → Bake tab → Bake

### **Step 5: Start Training**

#### **Method 1: Unity Built-in Training (Recommended)**

1. **Select SquadMate** GameObject
2. **Behavior Parameters** → Behavior Type: "Default"
3. **Press Play** in Unity
4. **Watch the agent learn** in real-time!

The agent will:
- Start with random behavior
- Gradually learn to follow the player
- Learn to revive, fight, and collect items
- Save training progress automatically

#### **Method 2: External Training (If CLI works)**

1. **Open terminal** in project folder
2. **Run**: `mlagents-learn config/squadmate_config.yaml --run-id=squadmate_v1`
3. **Press Play** in Unity when prompted

### **Step 6: Monitor Training**

#### **In Unity Console**
Watch for reward messages:
```
SquadMate received reward: +0.5 (revive)
SquadMate received reward: +0.1 (formation)
SquadMate received reward: +0.3 (enemy eliminated)
```

#### **Visual Indicators**
- Agent should start following player
- Agent should move towards objectives
- Reward values should increase over time

#### **Training Phases**
- **0-10 minutes**: Random movement
- **10-30 minutes**: Basic following behavior
- **30-60 minutes**: Reviving and item collection
- **1-2 hours**: Combat and tactical behavior

### **Step 7: Save and Use Trained Model**

#### **Automatic Saving**
Unity ML-Agents automatically saves models to:
```
Assets/ML-Agents/Models/SquadMate/
```

#### **Use Trained Model**
1. **Find the .onnx file** in the models folder
2. **Select SquadMate** GameObject
3. **Behavior Parameters** → Model: Drag the .onnx file
4. **Behavior Type**: "Inference Only"
5. **Press Play** to test!

## 🎯 **Training Tips**

### **Speed Up Training**
- **Time Scale**: Edit → Project Settings → Time → Time Scale = 5-10
- **Reduce Graphics**: Lower quality settings
- **Multiple Agents**: Duplicate SquadMate for parallel training

### **Improve Learning**
- **Adjust Rewards**: Modify RewardCalculator.cs values
- **Add Scenarios**: Create different enemy configurations
- **Curriculum Learning**: Start simple, add complexity

### **Debug Issues**
- **Check Console**: Look for error messages
- **Observation Count**: Must be exactly 22
- **Action Count**: 3 continuous + 5 discrete
- **Colliders**: Ensure all objects have proper colliders

## 🔧 **Troubleshooting**

### **Agent Not Learning**
- ✅ Check Behavior Parameters configuration
- ✅ Verify observation space size (22)
- ✅ Check reward function in RewardCalculator
- ✅ Ensure NavMesh is baked

### **Unity Crashes**
- ✅ Reduce Time Scale
- ✅ Lower environment complexity
- ✅ Check for infinite loops in scripts

### **No Rewards Showing**
- ✅ Check Console for reward messages
- ✅ Verify RewardCalculator is attached
- ✅ Ensure agent is calling AddReward()

## 🎉 **Success Indicators**

Your agent is learning when:
- ✅ It follows the player consistently
- ✅ It moves towards downed player to revive
- ✅ It seeks medkits when health is low
- ✅ It engages enemies when armed
- ✅ Cumulative reward increases over time

## 📈 **Next Steps**

1. **Train for 2-4 hours** for basic behavior
2. **Export the .onnx model** for use in other projects
3. **Experiment with different scenarios**
4. **Add voice commands** and advanced features
5. **Create specialized agents** for different roles

## 🆘 **Need Help?**

If you encounter issues:
1. Check Unity Console for errors
2. Verify all scripts are attached correctly
3. Ensure ML-Agents package is properly installed
4. Test with a simpler scene first

**The Unity-based approach is actually more reliable than command-line training for your setup!** 🚀
