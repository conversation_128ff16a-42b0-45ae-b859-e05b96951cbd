﻿== Error ==
Error

== NoResultFileSelected ==
No se ha seleccionado un documento como resultado del merge.

== UsageCaption ==
Uso

== Usage ==
Uso: binmergetool <opciones>

    opciones: <ficheros> <ficheroBase> [<nombreBase>] [[<automatic>] <ficheroResultado>] [<mergeType>] [<opciones>]

        baseFile:              {-b | --base}=<filename> 
        baseSymbolicName:      {-bn | --basesymbolicname}=<symbolicname>
        automatic:             -a | --automatic
        resultFile:            {-r | --result}=<filename>
        mergeType:             {-m | --mergeresolutiontype}={onlyone | onlysrc | onlydst | try | forced}

    ficheros: <origen> [<nombreOrigen>] <destino> [<nombreDestino>]

        origen:                {-s | --source}=<nombreFichero>
        nombreOrigen:          {-sn | --srcsymbolicname}=<nombre>
        destino:               {-d | --destination}=<nombreFichero>
        nombreDestino:         {-dn | --dstsymbolicname}=<nombre>

        
    Examples:
        
        binmergetool -s=file1.txt -d=file2.txt
        binmergetool -s=file1.txt -b=file0.txt --destination=file2.txt
        binmergetool --base=file0.txt -d=file2.txt --source=file1.txt --automatic --result=result.txt
        binmergetool -b=file0.txt -s=file1.txt -d=file2.txt -a -r=result.txt -m=onlyone

== DiffRequiresTwoArguments ==
Se necesitan dos ficheros para hacer diferencias

== UnsupportedFileTypeForDiff ==
Tipo de ficheros no soportado para obtener diferencias

== SaveChanges ==
¿Desea guardar los cambios en el fichero resultado?

== ExitPrompt ==
Salir...

== CantLoadImage ==
No se puede cargar la imagen '{0}'.

== NoArguments ==
No se especificaron argumentos

== UsageHint ==
Utilice [binmergetool -?] para mostrar la ayuda para esta herramienta