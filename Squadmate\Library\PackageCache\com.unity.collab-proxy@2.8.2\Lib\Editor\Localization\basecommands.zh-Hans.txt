﻿== WrongRevFormat3D ==
指定的修订格式无效

== AcceptResults ==
是否要接受这些结果？(Y/N)： 

== ActivatedUsers ==
总计活动用户数：     {0}

== ActiveKey ==
活动

== Add ==
控制是否可以将文件和目录添加到存储库

== AddAndMove ==
添加/移动冲突

== AddMoveConflictAction1 ==
保留这两种更改，将目标重命名为

== AddMoveConflictAction2 ==
保留源更改（保留添加并放弃移动）

== AddMoveConflictAction3 ==
保留目标更改（保留移动并放弃添加）

== AddNotApplicable ==
新项无法加载到工作区中。可能的原因是路径已被使用。请卸载该项（从配置视图中），然后重试该操作。

== Added ==
已添加

== AddedMovedConflictDestinationOperation ==
已从 {0} 移动到 {1}

== AddedMovedConflictExplanation ==
一个项在源上已添加，而另一个项在目标上已移至同一位置。

== AddedMovedConflictSourceOperation ==
已添加 {0}

== AddedOldNameConflict ==
添加了一个名称已被使用的项。

== AddedSameNameConflict ==
添加了两个同名的项

== AdminReadonlyEntered ==
服务器已进入只读模式。

== AdminReadonlyEntering ==
服务器正在进入只读模式。请稍候。

== AdminReadonlyLeaving ==
服务器正在退出只读模式。请稍候。

== AdminReadonlyLeft ==
服务器已退出只读模式。

== Advancedquery ==
控制高级查询执行过程

== All ==
全部

== AllowedKey ==
已允许

== AlsoThreeOption ==
您还可以：3. 将目标重命名为新的名称

== AncestorContributor ==
上级变更集：{0}（分支 {1}）

== AppliedRename ==
本地重命名 {0} -> {1} 将生效。

== Applyattr ==
允许用户应用属性

== Applylabel ==
允许用户应用标签

== AskDeleteWorkspace ==
未指定工作区。是否要删除当前工作区？(Y/N)： 

== AskDeleteDynamicWorkspace ==
The workspace is dynamic. You will LOSE all of the private items in the workspace if you delete it. Do you want to continue? (Y/N): 

== AttValueField ==
属性值

== AvailableUsers ==
总计可用用户数：  {0}

== BinFileMetadataChangeset ==
变更集：{0}

== BinFileMetadataCreated ==
创建者：{0}

== BinFileMetadataModified ==
修改日期：{0}

== BinFileMetadataSize ==
大小：{0}

== BisyncAutoLinkedCommentFormat ==
已自动链接变更集 {0} 和 {1}

== BisyncBranchAttributeError ==
分支属性的格式不正确

== BisyncBranchBothChanges ==
分支 '{0}' 具有新的本地和外部更改 {1}

== BisyncBranchExcluded ==
- {0}：已排除

== BisyncBranchForeignChanges ==
- {0}：外部更改 {1}

== BisyncBranchLocalChanges ==
- {0}：本地更改 {1}

== BisyncBranchMappedNotFound ==
找不到映射到引用 '{1}' 的分支 '{0}'。

== BisyncBranchUpToDate ==
- {0}：最新 {1}

== BisyncBranches ==
分支

== BisyncBranchesInSync ==
分支已同步。没有要推送或拉取的变更集。

== BisyncChangesetNotTranslated ==
无法转换变更集 {0}

== BisyncChangesetsInvolved ==
涉及的变更集

== BisyncChangesetsNotSorted ==
要推送的变更集无法正确排序

== BisyncCheckCommit ==
检查提交 

== BisyncCommitCheckFailed ==
提交检查已失败

== BisyncCommitDiscarded ==
已导入提交 '{0}'。

== BisyncCommitNotFound ==
在 Plastic 中找不到引用的提交 {0}

== BisyncCommitedForeignChanges ==
提交的外部变更集 {0}（Plastic 变更集：{1}）

== BisyncCommitsCannotBeProcessed ==
有无法处理的提交

== BisyncComplete ==
已完成同步

== BisyncCompressing ==
压缩对象

== BisyncConflictBranches ==
包含冲突的分支

== BisyncConnectException ==
连接到 Git 时发生了错误：{0}

== BisyncCsetNotFoundWithGuid ==
找不到 GUID 为 {0} 的最后一个同步的变更集

== BisyncCtrlCDetected ==
已按下 Ctrl+C。请等到当前变更集已同步，以防止数据不匹配。此后，该操作将正常停止。

== BisyncDownloading ==
下载

== BisyncDownloadingMappings ==
从服务器升级映射

== BisyncErrorPushingCs ==
推送 cs:{0} 时出错。{1}\n{2}

== BisyncExportComplete ==

导出完成。

== BisyncExportedToCommit ==
已导出 cs:{0} 以供提交 ({1})

== BisyncExporting ==
导出

== BisyncForeignTreeNotRetrieved ==
无法检索 rev:{0} 的外部树

== BisyncGetfileNotValid ==
Git 拉取程序不支持按路径执行的 GetFile 方法

== BisyncGettingInfo ==
接收引用

== BisyncGettingInfoDone ==
从远程服务器接收引用，完成

== BisyncInvalidCredentials ==
为存储库引入的凭据无效

== BisyncInvalidNodeType ==
节点类型无效

== BisyncInvalidScm ==
指定的 SCM 无效

== BisyncItemDiscardedWithoutRevid ==
哈希为 '{1}' 的项 '{0}' 没有 revid 映射，因此将被丢弃。

== BisyncLastCsetForeign ==
外部 SCM 上的最新变更集：{0}

== BisyncLastEquivalenceFound ==
找到最后的等效项：{0}

== BisyncLfsException ==
访问 GitHub LFS 时出错：{0}。可使用 '--skipgitlfs' 选项从命令行 (cm sync) 禁用 Git LFS 支持。

== BisyncNoChangesPull ==
没有新的修订可从外部 SCM 拉取

== BisyncNoSettingsMultipleStore ==
尚未指定同步设置，并为存储库 {0} 存储了多个设置。请指定要同步的 Git 存储库。

== BisyncNoSettingsNoStore ==
尚未指定同步设置，并没有为分支/存储库 {0} 存储任何设置

== BisyncNothingToCommit ==
没有任何可提交的内容！

== BisyncObjectCount ==
对象计数

== BisyncPackaging ==
正在打包...

== BisyncPendingChanges ==
有要推送和拉取的更改。

== BisyncPendingChangesExplanation ==
将拉取远程更改，然后您需要合并这些更改并将更改推回。

== BisyncProcessingObjects ==
处理对象：

== BisyncPullComplete ==
拉取完成。

== BisyncPullCsetList ==
正在对要拉取的变更集进行计数...

== BisyncPullingChangesets ==
导入

== BisyncPushCsetList ==
有要推送的更改。

== BisyncPushErrors ==
推送某些引用已失败：

== BisyncReferenceWithoutSha ==
无法处理没有 SHA 的引用

== BisyncRemoteDeletedSkip ==
分支 '{0}' 已在远程存储库中删除，并将被跳过。

== BisyncRemoteError ==

      远程错误：{0}
    

== BisyncRemoteMappingSkip ==
分支 '{0}' 来自远程标记映射，并将被跳过。

== BisyncResults ==
结果：

== BisyncRevisionDiscardedNotRetrieved ==
无法检索 SHA 为 '{1}' 的已映射修订 {0}，因此将被丢弃

== BisyncSettingsNotMatch ==
您引入的设置与分支/存储库 {0} 的已存储设置不匹配。存储的设置为：{1}。映射存储在此处：{2}。仅适用于高级用户：可以删除这些映射并重新运行该操作。

== BisyncShaNotFound ==
找不到 revid:{0} 的 SHA

== BisyncShaNotValid ==
要处理的 SHA 无效

== BisyncSyncStatusSaved ==
已保存同步状态：Plastic 变更集 {0} -> {1} 提交 {2}

== BisyncTagCannotBeApplied ==
无法应用修订 '{1}' 的标记 '{0}'。

== BisyncTreeNotRetrieved ==
无法检索 cs:{0} 的树

== BisyncTypeWithoutBuilder ==
Git 类型 {0} 没有生成器

== BisyncUploadComplete ==
上传完成。

== BisyncUploading ==
正在上传... 

== BisyncWarnPushingCs ==
推送 cs:{0} 时警告。{1}\n{2}

== BisyncWrongBranchName ==
错误的分支名称

== BisyncWrongFileFormat ==
文件 '{0}' 损坏。行 '{1}' 的格式错误。

== Branch ==
分支

== BranchHistoryEntryBase ==
基础变更集（仅间隔合并）：{0}

== BranchHistoryEntryDate ==
日期：{0}

== BranchHistoryEntryDestination ==
目标变更集：{0}

== BranchHistoryEntryMergeType ==
合并类型：{0}

== BranchHistoryEntryOwner ==
所有者：{0}

== BranchHistoryEntrySource ==
源变更集：{0}

== BranchHistoryEntryType ==
类型：{0}

== CalculatingInitialChangeset ==
正在计算初始变更集

== CannotBeDeletedChanged ==
无法在磁盘上删除项 '{0}'，因为它已更改。

== CannotBeDownloadChanged ==
无法以新内容更新文件 '{0}'，因为它已在本地更改。

== CannotChangeRepoPartialSwitch ==
“partial switch”命令无法更改工作区中配置的存储库。请从分支规格中删除存储库规格。

== CannotCheckinIncomingChangesInProgress ==
正在进行“传入更改”操作时无法签入。请先完成此操作，再重试签入。

== CannotCheckinMergeInProgress ==
无法开始签入操作，因为正在进行合并：请在完成合并之后再签入更改。正在从变更集 {1} 进行合并：{0}

== CannotDeleteChangesetWithPendingChanges ==
在有待定更改时，无法删除当前工作区分支中的变更集。

== CannotDownloadRevision ==
无法从服务器下载修订 {0}：{1}

== CannotMoveChangesetWithPendingChanges ==
在有待定更改时，无法移动当前工作区分支中的变更集。

== CannotRestoreDeleteWithChangesInside ==
无法还原项 '{0}'，因为在删除时该项中包含更改。请撤销待定更改，并在没有待定更改的情况下或使用其他冲突解决方案再次进行合并。

== CannotUnlockItems ==
无法解锁以下项，因为当前用户既不是管理员，也不是锁 {0} 的所有者

== CantExecuteAdvQuery ==
无法执行查询。您当前的目录可能是私有目录。

== Change ==
控制是否可以在存储库中修改文件和目录

== ChangeBoth ==
必须重命名这两个元素

== ChangeDelete ==
更改/删除冲突

== ChangeDeleteConflictActions11 ==
保留源更改（保留添加并放弃删除）

== ChangeDeleteConflictActions12 ==
保留目标更改（保留删除并放弃添加）

== ChangeDeleteConflictActions21 ==
保留源更改（保留更改并放弃删除）

== ChangeDeleteConflictActions22 ==
保留目标更改（保留删除并放弃更改）

== ChangeDeleteConflictDestinationOperation ==
已删除 {0}

== ChangeDeleteConflictExplanation ==
一个项在源上{0}，且目标已删除该项或其父级

== ChangeDependencies ==
更改：{0}。依赖项：

== ChangeReviewNotFound ==
无法找到以下审查更改请求（在签入注释中指定），或者已在以前的变更集内应用了以下审查更改请求：{0}{1}

== Changecomment ==
允许用户更改注释

== Changed ==
已更改

== ChangelistBuiltIn ==
内置

== ChangelistCherryPickSubtractive ==
减法挑拣

== ChangelistCherryPicking ==
挑拣

== ChangelistDefaultComment ==
默认 Unity VCS 更改列表

== ChangelistHiddenComment ==
包含用户定义的隐藏更改的更改列表

== ChangelistIntervalCherryPick ==
间隔挑拣

== ChangelistIntervalCherryPickSubtractive ==
减法间隔挑拣

== ChangelistManagementChanged ==
已成功编辑更改列表 '{0}'。

== ChangelistManagementCreated ==
已创建更改列表 '{0}'。

== ChangelistManagementDeleted ==
已成功删除更改列表 '{0}'。

== ChangelistMerge ==
合并

== ChangelistMergeComment ==
受合并过程影响的文件

== ChangelistMergeName ==
{0}（从 cs:{1}）

== Changeset ==
变更集

== CheckedOutKey ==
已签出

== CheckinParallelMsg ==
多线程签入

== RemainingMsg ==
剩余

== CheckinParallelUploadNumThreads ==
正在并行上传 {0} 个块

== CheckinProgressUploadingFileData ==
正在将文件 {0} ({1}) 上传到存储库

== CheckinStatusCancelling ==
正在取消签入操作

== CheckinStatusConfirming ==
正在确认签入操作

== CheckinStatusFinished ==
已完成签入

== CheckinStatusGeneratingdata ==
正在整合签入数据

== CheckinStatusRestoringpermissions ==
正在恢复文件访问

== CheckinStatusStarting ==
签入操作开始...

== CheckinStatusUploading ==
正在上传文件数据

== CheckinStatusValidating ==
正在验证签入数据

== CheckoutCannotBeSaved ==
无法保存项 {0} 的内容。

== CherryPick ==
挑拣

== Chgowner ==
控制更改所有者操作

== Chgperm ==
控制更改权限操作

== ChooseResolution ==
请为此冲突选择一个解决方案。要保留哪个操作？

== Ci ==
控制签入操作

== CleanDiffCalcMerges ==
正在计算到分支 br:{0}@{1}@{2} 的合并

== CleanDiffNotifyFinish ==
已跳过合并中的差异

== CleanDiffNotifyTotal ==
正在跳过 {0} 个变更集（合并目标）中的差异

== CleanDiffProcessCset ==
已跳过 cs:{0}@{1}@{2} 中的差异

== Cloaked ==
已掩蔽

== CloneDstRepositoryAlreadyExists ==
目标存储库 '{0}' 已存在且为空。

== CloneDstRepositoryCreated ==
已创建目标存储库 '{0}'。

== CmUndoIncompatibleFlags ==
标志 '--silent' 和 '--machinereadable' 不兼容。

== CmdArchiveErrorRestore ==
还原存档数据时出错。请注意，只允许 Unity VCS 服务器管理员还原归档数据。{0}

== CmdAskMkworkspaceConfirmdeletedir ==
目录 {0} 将被删除。是否确定 [Y|N]：

== CmdCannotBePerformedInPartialWk ==
该工作区为 Gluon 格式，需要转换为“标准”格式。只需运行一次更新即可修复

== CmdCannotBePerformedInStandardWk ==
该工作区不是 Gluon 格式。只需运行一次更新即可修复。该工作区当前是“标准”工作区（也许您是从常规 Unity VCS 中使用该工作区的）。请从 Gluon 运行更新以进行修复（或使用 cm partial）

== CmdErrorAclNoSuchUserOrGroup ==
没有此类用户或组

== CmdErrorCannotMoveDynamicWk ==
无法移动动态工作区。

== CmdErrorGetfileCantWriteOutput ==
错误：访问路径 {0} 遭拒，因为该文件是只读的

== CmdErrorGetfileRevnotfound ==
找不到指定的修订 {0}

== CmdErrorIncorrecBrspec ==
不正确的分支规格

== CmdErrorIncorrecWkspec ==
不正确的工作区规格

== CmdErrorIncorrectCodeReviewId ==
不正确的代码审查 ID：{0}

== CmdErrorLabelIncorrectrevspec ==
不正确的修订规格：{0}

== CmdErrorLabelItemNotFound ==
无法应用标签 {0}。该项是私有的。

== CmdErrorLabelMarkerspecnotfound ==
无法找到指定的标签规格。{0}

== CmdErrorListrepRepserverunknown ==
未知的存储库服务器

== CmdErrorMergeWithModifiedItems ==

    您有待定更改需要签入您的工作区。
    建议在合并之前签入，以免在撤销合并时
    可能发生问题。

    如果您熟悉合并，则可以通过向“client.conf”文件添加以下键
    来禁用此行为：

        <MergeWithPendingChanges>yes</MergeWithPendingChanges>

    注意：我们更改了此行为以避免新用户遇到问题，但是如果您熟悉
    合并的工作原理，那么启用该功能并不危险。
    

== CmdErrorNoSuchBranch ==
分支 {0} 在存储库 {1} 中不存在。

== CmdErrorNoSuchTypeTrigger ==
类型 {0} 无效。请注意，其格式为“子类型-类型”。

== CmdErrorNoSuchUser ==
用户 {0} 不存在

== CmdErrorNoSuchUserDuCommandHint ==
也许您必须使用标志“--nosolveuser”来停用在身份验证系统中不再存在的用户。

== CmdErrorNoWkFoundFor ==
无法找到 {0} 的工作区

== CmdErrorNotLocateItemhandler ==
无法找到有效的 ItemHandler。

== CmdErrorRevertRevnotfound ==
找不到要还原到的指定修订：{0}

== CmdErrorUndocheckoutCantfindrev ==
无法找到指定的修订 {0}

== CmdErrorUndocheckoutIdNotCheckedout ==
未签出指定的修订 {0}

== CmdErrorUndocheckoutIncorrectspec ==
无效的签出规格：{0}

== CmdErrorUpdateProcessingItem ==
更新 {0} 时出错。{1}

== CmdErrorWkNotCreatedOnServer ==
无法创建工作区

== CmdMandatoryParameter ==
{0}：缺少需要的参数 {1}

== CmdMessageAddIgnoredError ==
无法添加项 {0}。错误：{1}

== CmdMessageAddItemadded ==
已正确添加项 {0}

== CmdMessageCiIgnoredError ==
无法签入项 {0}。错误：{1}

== CmdMessageItemcheckout ==
已正确签出项 {0}

== CmdMessageManipulateselectorNoselector ==
未指定选择器。

== CmdMessageMkworkspaceCorrectlycreated ==
已正确创建工作区 {0}

== CmdMessageMkworkspaceDynamicWaitToMount ==
plasticfs 可能需要花费几秒钟时间来装入新工作区

== CmdMessageMkworkspaceDynamicRequiredParams ==
创建动态工作区时，必须同时指定 '--dynamic' 和 '--tree=[tree]' 参数。请查看 'cm workspace create --help' 以了解更多信息。

== CmdMessageNoworkspacesfound ==
此机器中没有工作区。

== CmdMessageNoworkspacesfoundInPath ==
在路径 {0} 中尚未找到工作区。

== CmdMessageProceedAdd ==
即将添加所选项。请稍候...

== CmdMessageProceedCheckdb ==
检查数据库完整性可能需要一些时间。请稍候...

== CmdMessageProceedCheckin ==
即将签入所选项。请稍候...

== CmdMessageProceedCheckout ==
即将签出所选项。请稍候...

== CmdMessageRemoveItem ==
已删除项 {0}。

== CmdMessageShowselectorNoselector ==
此工作区中没有可用的选择器

== CmdMessageWorkspacedeleted ==
已删除工作区 {0}。

== CmdMktriggerPosition ==
在位置 {0} 上已创建触发器。

== CmdMoved ==
{0} 已移动到 {1}

== CmdMsgCopiedFromRep ==
已从 {1} 下载 {0}

== CmdMsgCreateDir ==
已创建目录 {0}

== CmdMsgFileAlreadyExistsInWk ==
文件 {0} 在该工作区中已存在

== CmdMsgFileChangedInWk ==
文件 {0} 在该工作区中已更改。不会覆盖。

== CmdMsgFileChangedInWkToRm ==
文件 {0} 在该工作区中已更改。不会删除。

== CmdMsgFileDateNotChangedInWk ==
文件 {0} 的日期在该工作区中已修改

== CmdMsgItemCantLabelNoLabelOnRep ==
修订 {0} 所在的存储库不包含该标签

== CmdMsgLabelCantLabelOtherRep ==
修订 {0} 与所选标签不在同一存储库中

== CmdMsgLabelCorrectlyLabeled ==
已正确标记变更集 {0}。

== CmdMsgLsNotloaded ==
未加载

== CmdMsgMergeCannotmergecloaked ==
不会合并元素 {0}，因为它已被掩蔽。

== CmdMsgMergeCannotmergedir ==
已放弃合并元素 {0}，因为在合并过程中已删除该元素。

== CmdMsgMergeCannotmergefile ==
已放弃合并元素 {0}，因为在合并过程中已删除该元素。

== CmdMsgMergeDone ==
合并完成

== CmdMsgMergeGoingtocopymerge ==
正在复制合并 {0}

== CmdMsgMergeGoingtomerge ==
正在合并 {0}

== CmdMsgMergeInvalidinterval ==
所选变更集间隔无效

== CmdMsgMergeMergingmoveddir ==
目录 {0} 已移动到 {1} 并且正在合并

== CmdMsgMergeMergingmovedfile ==
文件 {0} 已移动到 {1} 并且即将合并

== CmdMsgMergeNomergesdetected ==
未检测到合并

== CmdMsgMergeNotconnected ==
必须连接合并源和目标才能执行减法合并

== CmdMsgNocheckoutsfound ==
尚未找到签出

== CmdMsgNopathfound ==
无法解析路径

== CmdMsgRenaming ==
正在将 {0} 重命名为 {1}

== CmdMsgUpdateStoringCheckout ==
正在存储 {0} 的已签出数据

== CmdMsgUpdateWontOverwriteCo ==
更新不会覆盖已签出文件。{0}

== CmdMsgWkinfo ==
工作区 {0} 的选择器：

== CmdMsgWontOverwriteCheckout ==
不会覆盖已签出文件。{0}

== CmdNoLicensedUsers ==
没有活动用户。用户在系统上执行第一个操作时便被激活。

== CmdPatchRequiresDiff ==
找不到差异比较工具，请从以下网址进行下载：
http://gnuwin32.sourceforge.net/packages/diffutils.htm

安装后，请将其添加到 PATH 环境变量中，或使用
--tool 参数指定 diff.exe 的位置。

== CmdPatchRequiresPatch ==
找不到补丁，请从以下网址进行下载：
http://gnuwin32.sourceforge.net/packages/patch.htm 

安装后，请将其添加到 PATH 环境变量中，或使用
--tool 参数指定 patch.exe 的位置。

== CmdRepServerResultCheckdb ==
存储库服务器：{0}

== CmdRepoResultCheckdb ==
存储库：{0}

== CmdSetselectorEndwithdot ==
正在从 stdin 读取选择器配置，在一行中以“.”结尾

== CmdStatusIncompatibleOptionsShortAndXml ==
--short 和 --xml 选项不能一起使用，因为 --short 不会加载详细的状态信息。

== CmdUnchangeDone ==
{0} 撤销更改完成。

== CmdUncheckedOut ==
{0} 已正确取消签出

== CmdUnexpectedOption ==
{0}：意外选项 {1}

== CmdUpdated ==
{0} 已正确更新

== CommandFinishedSuccesfully ==
已成功完成命令

== Comment ==
注释

== Committing ==
签入

== Configlocks ==
允许添加新的锁定规则

== ConfirmNewPassword ==
确认密码： 

== ConnectTubeSucceeded ==
Unity VCS 服务器 ({0}) 已成功连接到 Plastic Tube。

== ConnectionFail ==
测试连接在尝试连接到 Unity VCS 服务器时遇到问题

== ConnectionOk ==
已成功执行测试连接

== ContinueSolving ==
{0} 个剩余冲突，是否要继续解决冲突？(Y/N)： 

== ContinueWithPendingChangesQuestion ==
您的工作区中有已更改的文件。您可以取消切换操作以便检查已更改的文件，也可以继续进行该操作。如果继续进行切换操作，已更改的文件将不会丢失，并在切换操作后仍将显示为已更改。是否要继续进行切换操作？(y/n)

== Controlled ==
已控制

== Copied ==
已复制（新）

== CopiedCannotBeApplied ==
无法复制项 '{0}'，因为有错误：'{1}'。

== CopyMergeIsRealMerge ==
项 {0} 当前已加载到工作区中，因此需要对其进行合并而不是复制合并。

== CopyMergeNeeded ==
在 {1} 中的项 {0} 上需要进行复制合并

== CopyMergePromoted ==
项 {0} 的复制合并已升级为合并

== CopyingMergeFiles ==
正在复制合并文件...

== CreatedChangesetNumber ==
已创建变更集 {0}

== CreatedOn ==
创建时间

== CreatedShelveNumber ==
已创建搁置 {0}

== CsetNumber ==
变更集编号

== CurrentOutputDirectory ==
当前输出目录为

== CycleMove ==
循环移动冲突

== CycleMoveConflictActions1 ==
保留源上完成的移动（放弃目标上的移动）

== CycleMoveConflictActions2 ==
保留目标上完成的移动（放弃源上的移动）

== CycleMoveConflictDestinationOperation ==
已从 {0} 移动到 {1}

== CycleMoveConflictExplanation ==
两个项已在源和目标上移动并发生冲突，因为它们形成了一个循环。

== CycleMoveConflictSourceOperation ==
已从 {0} 移动到 {1}

== DataWritten ==
已写入数据

== DefaultFormatDiffmetrics ==
已更改：{0}  已添加：{1}  已删除：{2}

== DeleteAndChange ==
删除/更改冲突

== DeleteAndMove ==
删除/移动冲突

== DeleteChangeConflictActions11 ==
保留源更改（保留删除并放弃添加）

== DeleteChangeConflictActions12 ==
保留目标更改（保留添加并放弃删除）

== DeleteChangeConflictActions21 ==
保留源更改（在目标上保留删除并放弃更改）

== DeleteChangeConflictActions22 ==
保留目标更改（保留更改并放弃删除）

== DeleteChangeConflictExplanation ==
一个项在源上已删除，且目标{0}它。

== DeleteChangeConflictSourceOperation ==
已删除 {0}

== DeleteChangesetDiscardAllChangesNotInteractive ==
有人删除了您正在处理的变更集。
这意味着您的签出和本地更改不再有效。

这是因为自基础变更集消失之后，现在无法找到实际的更改。

您需要撤销所有更改并更新工作区以恢复有效配置。
如果您以交互方式（即不使用 stdin 重定向）运行该命令，则可以实现此目的。

== DeleteChangesetDiscardAllChangesQuestion ==
有人删除了您正在处理的变更集。
这意味着您的签出和本地更改不再有效。

这是因为自基础变更集消失之后，现在无法找到实际的更改。

是否要撤销所有更改并更新工作区以恢复有效配置？(y/n)

== DeleteDelete ==
删除警告

== DeleteDeleteConflictDestinationOperation ==
已删除 {0}

== DeleteDeleteConflictSourceOperation ==
已删除 {0}

== DeleteDeleteWarningMessage ==
已丢弃此项，因为在源和目标上均已将其删除

== DeleteMoveConflictActions1 ==
保留源更改（保留删除并放弃移动）

== DeleteMoveConflictActions2 ==
保留目标更改（保留移动并放弃删除）

== DeleteMoveConflictDestinationOperation ==
已从 {0} 移动到 {1}

== DeleteMoveConflictExplanation ==
一个项在源上已删除，且该项在目标上已移动。

== DeleteMoveConflictSourceOperation ==
已删除 {0}

== DeleteNotApplicable ==
无法将该项卸载，因为无法在工作区中找到该项。您的工作区可能已损坏。请联系支持人员。 

== DeletePrivateDeletesSummary ==
已删除 {0} 个单文件和 {1} 个目录树。

== DeletePrivateDirectoryFailed ==
无法删除受控目录：{0}

== DeletePrivateDryrun ==
注意：这是试运行。未删除文件。

== DeletePrivateFailuresSummary ==
无法删除 {0} 个单文件和 {1} 个目录树。

== DeletePrivateFileFailed ==
无法删除受控文件：{0}

== DeletePrivateSkipControlledDir ==
已跳过受控目录：{0}

== DeletePrivateSkipControlledFile ==
已跳过受控文件：{0}

== DeletePrivateSkipIgnoredDir ==
已跳过已忽略目录：{0}

== DeletePrivateSkipIgnoredFile ==
已跳过已忽略文件：{0}

== DeletePrivateSkipMissingFileOrDirectory ==
已跳过缺失文件或目录：{0}

== Deleted ==
已删除

== DeletedPrivateDirectory ==
已删除目录：{0}

== DeletedPrivateFile ==
已删除文件：      {0}

== DeniedKey ==
已拒绝

== DependenciesApplyLocalChanges ==
应用本地更改

== DependenciesCheckin ==
签入

== DependenciesDescription ==
一些选择要{0}的项还取决于其他需要包含在该操作中的项。所有依赖项路径应包含在指向 {0} 的给定路径中。选项 "--dependencies" 也可用于自动包含所有依赖项。

== DependenciesShelve ==
搁置

== DependenciesUndoChanges ==
撤销更改

== Destination ==
目标

== DestinationRevisionNotFound ==
找不到要升级合并的目标修订

== DiffNotDownloadedRevision ==
无法从服务器下载修订 {0}。请再次尝试选择其他文件，然后返回到该文件。

== DirConflictCannotBeApplied ==
无法应用源为 '{1}' 且目标为 '{2}' 的冲突 {0}，因为有错误：'{3}'。

== DiscardedAddedWarningMessage ==
已丢弃该项，因为源添加了该项，但该项已经加载到目标中，所以无需添加该项

== DiscardedChangedWarningMessage ==
已丢弃该项，因为从源到目标之间未更改修订的内容，或者因为源和目标加载了相同的修订

== DiscardedDeleteWarningMessage ==
已丢弃该项，因为源删除了该项，并且在目标中未加载该项，所以无法删除该项

== DiscardedFsProtectionFormat ==
已丢弃 {1} 中的项 {0} 的 FS 保护，因为{2}

== DiscardedFsProtectionWarningMessage ==
已丢弃该项，因为已在源和目标中将该项的文件系统保护更改为同一个值，源已更改文件系统保护且已在目标上删除，或者源已删除该项且目标已更改文件系统保护

== DiscardedMergeConnectedrevision ==
源修订已与目标修订连接。

== DiscardedMergeFormat ==
已放弃 {1} 中的项 {0} 上的合并，因为{2}

== DiscardedMergeNotconnectrevision ==
源修订未与目标修订连接。

== DiscardedMergeNotloaded ==
无法再加载修订（已删除元素）。

== DiscardedMergeSamerevision ==
源修订与工作区中加载的修订相同。

== DiscardedMovedWarningMessage ==
已丢弃该项，因为该项已移至源和目标中的同一位置，或者该项已加载到目标上

== DiscardedSubtractiveFormat ==
已放弃 {1} 中的项 {0} 上的减法合并，因为{2}

== DisconnectTubeSucceeded ==
Unity VCS 服务器 ({0}) 已成功与 Plastic Tube 断开连接。

== DivergentMove ==
分歧移动冲突

== DivergentMoveConflictActions1 ==
保留源上完成的移动（放弃目标上的移动）

== DivergentMoveConflictActions2 ==
保留目标上完成的移动（放弃源上的移动）

== DivergentMoveConflictDestinationOperation ==
已从 {0} 移动到 {1}

== DivergentMoveConflictExplanation ==
一个项已在源和目标上移动到两个不同位置。

== DivergentMoveConflictSourceOperation ==
已从 {0} 移动到 {1}

== DownloadMissingFileNotFoundOnSource ==
无法下载 '{0}' (revid:{1})。可能与 --nodata 重复，但是无法在复制源 {2}@{3} 中找到其数据。

== DownloadMissingFileReplicationSourceGuidsResolutionMethodNotAvailable ==
无法下载 '{0}' (revid:{1})。可能与 --nodata 重复，但是未升级其复制源 {2}@{3}，所以有一个必需的 API 不可用。请升级服务器 {3}。

== DownloadMissingFileReplicationSourceNotAvailable ==
无法下载 '{0}' (revid:{1})。可能与 --nodata 重复，但是其复制源 {2}@{3} 不可用。

== DownloadMissingFileWithoutReplicationSource ==
无法下载 '{0}' (revid:{1})。可能与 --nodata 重复，但是在存储库 {2}@{3} 中不可用。

== DstContributor ==
目标变更集：{0}（分支 {1}）

== ElementNewName ==
具有新名称 {0} 的元素

== ElementOldNewName ==
具有旧名称 {0} 和新名称 {1} 的元素

== EnterNewDestinationName ==
请输入目标的新名称：

== Error ==
错误

== ErrorCheckingIn ==
签入 {0} 时出错。{1}

== ErrorCloneDstRepNotEmpty ==
目标存储库 '{0}' 不为空，因此克隆操作无法继续。

== ErrorCloneDstRepNotFound ==
无法确定用于克隆操作的目标存储库。

== ErrorClonePackageNotValid ==
如果指定了目标，则无法使用选项 --package。

== ErrorCloneSourceRepoNotSpecified ==
尚未指定源存储库。

== ErrorCloneSrcAndDstEquals ==
源和目标存储库相同 ('{0}')。

== ErrorCloneSrcRepNotFound ==
无法从 '{0}' 确定用于克隆操作的源存储库。

== ErrorExecutingQuery ==
执行查询时出错： 

== ErrorImportingCommit ==
无法导入变更集 '{0}'。错误：{1} 请与支持团队联系。

== ErrorLaunchingEditor ==
无法运行编辑器：{0}

== ErrorPullNodataNotValid ==
选项 --nodata 不能用于复制包。

== ErrorPushHydrateNotValid ==
推送期间不能使用选项 hydrate。

== ErrorPushNodataNotValid ==
推送期间不能使用选项 --nodata。

== ErrorReplicateDestinationRepoNotSpecified ==
尚未指定目标存储库

== ErrorReplicateHydrateSourceNotFound ==
无法自动获取 '{0}' 的 hydrate 源存储库。请手动指定源存储库。

== ErrorReplicateNodataNotValid ==
选项 --nodata 不能用于复制包，也不能与 --push 选项一起使用。

== ErrorReplicatePackageNotSpecified ==
尚未指定复制包和目标存储库

== ErrorReplicateSourceBranchNotSpecified ==
尚未指定源分支

== ErrorRepositoriesDontMatch ==
指定的存储库不匹配

== EvilTwin ==
双面恶魔冲突

== EvilTwinConflictActions1 ==
保留这两种更改，将目标重命名为

== EvilTwinConflictActions2 ==
保留源上添加的项（放弃目标上的添加）

== EvilTwinConflictActions3 ==
保留目标上添加的项（放弃源上的添加）

== EvilTwinConflictDestinationOperation ==
已添加 {0}

== EvilTwinConflictExplanation ==
一个项在源上和目标上已用相同的名称进行添加，但它们是不同的项。

== EvilTwinConflictSourceOperation ==
已添加 {0}

== Excluded ==
已排除 {0}。

== ExclusiveCheckoutDetail ==
{0} 已由 {2}@{3} 的用户 {1} 签出

== ExclusiveCheckoutDetailShort ==
{0} 已由用户 {1} 签出

== ExpirationDate ==
到期日期：        {0}

== False ==
否

== FastUpdCannotBePerformedInPartialWk ==
Gluon 工作区中不允许使用“快速更新”选项。请禁用该选项，然后重试该操作。

== FastUpdChanges ==
已应用以下更改：

== FastUpdDownloadProgress ==
已下载 {0} / {1} ({2:0.##%})

== FastUpdProcessProgress ==
已处理 {0} / {1} ({2:0.##%})

== FastUpdStageApplyingChanges ==
正在应用更改...

== FastUpdStageCalculatingChanges ==
正在计算更改...

== FastUpdStageCancelling ==
正在取消快速更新操作...

== FastUpdStageCompilingInfo ==
正在整合快速更新数据...

== FastUpdStageFinished ==
快速更新已完成

== FastUpdWarnings ==
无法在磁盘上应用以下更改：

== Fatal ==
严重

== FemaleNone ==
无

== FetchingAcls ==
正在提取 ACL

== FetchingAttributes ==
正在提取属性

== FetchingBranches ==
正在提取分支

== FetchingChangesets ==
正在提取变更集

== FetchingChildren ==
正在提取树

== FetchingFinished ==
正在传输元数据

== FetchingItems ==
正在提取项

== FetchingLabels ==
正在提取标签

== FetchingLinks ==
正在提取链接

== FetchingMetadata ==
正在提取元数据

== FetchingMoveRealizations ==
正在提取移动操作

== FetchingReferences ==
正在提取引用

== FetchingReviews ==
正在提取代码审查

== FetchingRevisions ==
正在提取树

== FetchingSeids ==
正在提取 SEID

== File ==
文件

== FileModifiedSource ==
文件 {0} 在源上已修改，并将替换目标版本

== Files ==
文件

== FinishedNotOk ==
已取消

== FinishedOk ==
已正常完成

== FinishedStatus ==
复制已中断 {0}

== FinishedWithErrors ==
已完成但有错误

== First ==
第一个

== From ==
自

== FsProtectionCannotBeApplied ==
无法应用项 '{0}' FS 保护，因为有错误：'{1}'。

== GameuiCheckinAddedMissingParentConflictAction ==
请撤销添加操作或将其移动到服务器中的现有位置。

== GameuiCheckinAddedMissingParentConflictDescription ==
无法添加项 '{0}'，因为其父目录不再加载到服务器中。

== GameuiCheckinAddedPathInUseConflictAction ==
请撤销添加操作或将其移动到服务器中的空闲位置。

== GameuiCheckinAddedPathInUseConflictDescription ==
无法添加项 '{0}'，因为在服务器中的同一位置加载了另一个项。

== GameuiCheckinChangedFileConflictAction ==
请撤销您的本地更改，下载最新版本，并重新应用您的本地更改。

== GameuiCheckinChangedFileConflictDescription ==
'{0}' 需要合并。

== GameuiCheckinChangedMissingConflictAction ==
请撤销本地更改或将该项还原到服务器中的现有位置。

== GameuiCheckinChangedMissingConflictDescription ==
无法更改项 '{0}'，因为其不再加载到服务器中。

== GameuiCheckinChangedXlinkConflictAction ==
请撤销您的本地更改，下载最新版本，并再次编辑 Xlink。

== GameuiCheckinChangedXlinkConflictDescription ==
无法更改 Xlink '{0}' 的目标，因为其在服务器中已更改。

== GameuiCheckinCopiedLoadedConflictAction ==
请撤销您的本地更改，下载最新版本，并重新应用您的本地更改。

== GameuiCheckinCopiedLoadedConflictDescription ==
无法复制项 '{0}'，因为其已加载到服务器中。

== GameuiCheckinCopiedMissingParentConflictAction ==
请撤销复制操作或将其移动到服务器中的现有位置。

== GameuiCheckinCopiedMissingParentConflictDescription ==
无法复制项 '{0}'，因为其父目录不再加载到服务器中。

== GameuiCheckinCopiedPathInUseConflictAction ==
请撤销复制操作或将其移动到服务器中的空闲位置。

== GameuiCheckinCopiedPathInUseConflictDescription ==
无法复制项 '{0}'，因为在服务器中的同一位置加载了另一个项。

== GameuiCheckinDeletedAlreadyDeletedConflictAction ==
请撤销删除操作并更新到最新版本。

== GameuiCheckinDeletedAlreadyDeletedConflictDescription ==
无法删除项 '{0}'，因为该项不再加载到服务器中。

== GameuiCheckinDeletedChangedWarningDescription ==
已删除的项 '{0}' 在服务器中已更改。如果已应用删除，则服务器更改将丢失。

== GameuiCheckinDeletedMovedWarningDescription ==
已删除的项 '{0}' 在服务器中已移动。如果已应用删除，则服务器移动将丢失。

== GameuiCheckinDeletedWarningAction ==
是否确定要删除项 '{0}'？

== GameuiCheckinDirReplacedChangedConflictDescription ==
无法还原项 '{0}'，因为其在服务器中已更改。

== GameuiCheckinFileReplacedChangedConflictDescription ==
'{0}' 需要合并。

== GameuiCheckinFsProtectionMissingConflictAction ==
请撤销本地更改或将该项还原到服务器中的现有位置。

== GameuiCheckinFsProtectionMissingConflictDescription ==
无法更改项 '{0}' 的权限，因为该项不再加载到服务器中。

== GameuiCheckinMovedAlreadyMovedConflictAction ==
请撤销移动操作并更新到最新版本。

== GameuiCheckinMovedAlreadyMovedConflictDescription ==
无法将项 '{0}' 移动到 '{1}'，因为其已加载到服务器中的目标中。

== GameuiCheckinMovedDivergentConflictAction ==
是否仍要将项 '{0}' 移动到 '{1}'？

== GameuiCheckinMovedDivergentConflictDescription ==
已移动的项 '{0}' 已移动到服务器中的不同位置。如果已应用移动，则服务器移动将丢失。

== GameuiCheckinMovedDstInUseConflictAction ==
请撤销移动操作或将其移动到服务器中的空闲位置。

== GameuiCheckinMovedDstInUseConflictDescription ==
无法将项 '{0}' 移动到 '{1}'，因为在服务器中的同一位置加载了另一个项。

== GameuiCheckinMovedInsideItselfConflictAction ==
请撤销移动操作或将其移动到服务器中的有效位置。

== GameuiCheckinMovedInsideItselfConflictDescription ==
无法将项 '{0}' 移动到 '{1}' 中，因为 '{1}' 已加载到服务器中的 '{0}' 内。

== GameuiCheckinMovedMissingDstConflictAction ==
请撤销移动操作或将该项移动到服务器中的现有位置。

== GameuiCheckinMovedMissingDstConflictDescription ==
无法将项 '{0}' 移动到 '{1}'，因为目标不再加载到服务器中。

== GameuiCheckinMovedMissingItemConflictAction ==
请撤销移动操作或将该项还原到服务器中的现有位置。

== GameuiCheckinMovedMissingItemConflictDescription ==
无法移动项 '{0}'，因为其不再加载到服务器中。

== GameuiCheckinReplacedChangedConflictAction ==
请撤销您的还原操作，下载最新版本，并再次进行还原。

== GameuiCheckinReplacedMissingConflictAction ==
请撤销您的还原操作。

== GameuiCheckinReplacedMissingConflictDescription ==
无法还原项 '{0}'，因为其不再加载到服务器中。

== GameuiOutOfDateUnresolvedXlink ==
无法解析 Xlink '{0}'。无法更新未解析的 Xlink 下的项。

== GetfileRevdatanotfound ==
对于给定的规格，找不到数据

== Group ==
组

== GroupKey ==
组

== HiddenChanged ==
隐藏已更改

== Ignored ==
已忽略

== InactiveKey ==
不活动（未获许可）

== Info ==
信息

== IntervalMerge ==
间隔合并

== IntroducingData ==
正在引入数据

== IssueTrackerCheckinFailed ==
无法将签入数据记录到问题跟踪程序中：{0}

== IssueTrackerNotSupported ==
指定的问题跟踪程序 '{0}' 不受支持。

== ItemAddedSource ==
项 {0} 在源上已添加，并将作为合并的结果添加

== ItemAlreadyChanged ==
不会下载该项，因为在工作区 '{0}' 中已更改（已签出、已在本地更改、已删除、已移动或已还原）该项。请撤销或签入本地更改，然后重试该操作。

== ItemDeletedSource ==
项 {0} 在源上已删除，并将作为合并的结果删除

== ItemFsprotToApply ==
项 {0} 已在源上更改文件系统权限

== ItemMovedSource ==
项 {0} 在源上已移动到 {1}，并将作为合并的结果移动

== ItemNotFound ==
无法找到项 {0}

== ItemPathAlreadyUsedByChange ==
不会下载/移动该项，因为 '{0}' 中已有更改。请撤销本地更改，卸载该项（从配置视图中），然后重试该操作。

== KeyEditable ==
可编辑

== KeyPrivate ==
私有

== KeyWorkspaceRevision ==
工作区修订

== Label ==
标签

== LicenseEdition ==
版本：                {0}

== LicenseInformation ==
许可证信息：

== LicenseUsage ==
许可证使用情况：

== LicensedTo ==
许可给：            {0}

== LicensedUsers ==
总计许可用户数：   {0}

== ListFindObjects ==
可用对象和属性： 

== ListPermissionsKey ==
可用权限： 

== ListTriggerTypes ==
可用触发类型： 

== LkNameField ==
链接名称

== LoadedRevision ==
已加载修订 {0}@{1}

== LoadedTwice ==
项加载两次冲突

== LoadedTwiceConflictActions1 ==
保留源上完成的添加（放弃目标上的添加）

== LoadedTwiceConflictActions2 ==
保留目标上完成的添加（放弃源上的添加）

== LoadedTwiceConflictDestinationOperation ==
已添加 {0}

== LoadedTwiceConflictExplanation ==
两个项已在源和目标上添加并发生冲突，因为它们是同一个项。

== LoadedTwiceConflictSourceOperation ==
已添加 {0}

== LocalAndDownloadedSameFile ==
不会下载文件 {0}，因为本地文件与要下载的文件的内容匹配。变更集：{1}

== LocatedOnBranch ==
位于分支上

== LogAdded ==
已添加

== LogChanged ==
已更改

== LogDefaultCsFormat ==
变更集编号：{changesetid}{newline}分支：{branch}{newline}所有者：{owner}{newline}日期：{date}{newline}注释：{comment}{newline}更改：{newline}{items}------------------------------------------------------------

== LogDefaultCsFormatWithoutChanges ==
变更集编号：{changesetid}{newline}分支：{branch}{newline}所有者：{owner}{newline}日期：{date}{newline}注释：{comment}{newline}------------------------------------------------------------

== LogDeleted ==
已删除

== LogMoved ==
已移动

== MakeTubeSucceeded ==
已正确创建 Tube {0} -> {1}。

== Markers ==
标记

== Merge ==
合并

== MergeBothChanges ==
两个参与者。

== MergeDstChanges ==
目标参与者。

== MergeExtraInfoBase ==
基项

== MergeExtraInfoCommentsLine ==
注释：{0}

== MergeExtraInfoContributorLine ==
{0}，来自 {1}{2}，由 {3} 于 {4} 创建

== MergeExtraInfoDestination ==
目标

== MergeExtraInfoRecursiveMergeLine ==
递归合并：这是递归合并。这意味着有多个上级而不只一个。
Unity VCS 将进行解析，并自动合并多个共同上级以形成单一上级，该上级将用于合并源参与者和目标参与者。

现在，您正在将上级 {0} 与上级 {1} 合并，这将生成一个在以后使用的虚拟上级 vcs:{2}。

== MergeExtraInfoSource ==
源

== MergeInProgress ==
您已有一个正在从变更集 {0}@{1} 进行的合并。请完成此合并后再从其他源进行合并

== MergeNeeded ==
需要将文件 {0} 从 {1} 合并到 {2} 基项 {3}。更改者：{4}

== MergeNoneChanges ==
无。

== MergeProgressRecursive ==
递归

== MergeProgressString ==
正在合并文件 {0}/{1}

== MergeResultMultipleLinkTargets ==
合并结果为符号链接 {0} 留下了多个目标

== MergeSourceFormat ==
{1}@{2} 的 {0}

== MergeSrcChanges ==
源参与者。

== MergeToMergeNeeded ==
分支 {0}@{1}@{2}（装入 '{3}'）具有多个头部，因为在“合并到”操作期间创建了新的变更集。请执行从变更集 {4}@{1}@{2} 到分支 {0}@{1}@{2} 的合并（或“合并到”），从而统一分支的头部。

== MergeVirtualChangesetComment ==
递归合并期间创建的虚拟变更集

== MergedFile ==
已合并文件 {0} / {1}：{2}

== Mergefrom ==
控制合并源操作

== MergingFile ==
正在合并文件 {0} / {1}：{2}

== MessageBranchSelector ==
选择一个分支以获取修订（如果没有，则为 /main）： 

== MessageCheckingIn ==
正在签入 {0}... 

== MessageCheckingOut ==
正在签出 {0}... 

== MessageCoSelector ==
选择一个分支以接收签出（如果没有，则为 /main）： 

== MessageContinueWithSelector ==
这是结果选择器。是否激活？(Y/N)： 

== MessageDone ==
完成

== MessageFileNotFound ==
文件 {0} 不存在。

== MessageLabelSelector ==
选择一个标签以获取其中的修订： 

== MessageMoreRepositories ==
更多存储库？(Y/N)： 

== MessageNoRepositories ==
此服务器中没有存储库。 

== MessageNumItemSelector ==
== 存储库选择器 #{1} 的项选择器 #{0} ==

== MessageNumRepSelector ==
== 存储库选择器 #{0} ==

== MessageRepositoryName ==
选择存储库名称： 

== MessageRepositoryServer ==
选择存储库服务器： 

== MessageSelectorNotSet ==
尚未设置选择器。

== Mkattr ==
允许用户创建属性

== Mkchildbranch ==
允许用户创建子分支

== Mklabel ==
允许用户创建标签

== Mkrepository ==
允许用户创建存储库

== Mktoplevelbranch ==
允许用户创建顶级分支

== Mktrigger ==
允许用户创建触发器

== Modified ==
已修改

== Move ==
控制是否可以在存储库中移动文件和目录

== MoveAddConflictAction1 ==
保留这两种更改，将目标重命名为

== MoveAddConflictAction2 ==
保留源更改（保留移动并放弃添加）

== MoveAddConflictAction3 ==
保留目标更改（保留添加并放弃移动）

== MoveAndAdd ==
移动/添加冲突

== MoveAndDelete ==
移动/删除冲突

== MoveDeleteConflictActions1 ==
保留源更改（保留移动并放弃删除）

== MoveDeleteConflictActions2 ==
保留目标更改（保留删除并放弃移动）

== MoveDeleteConflictDestinationOperation ==
已删除 {0}

== MoveDeleteConflictExplanation ==
一个项在源上已移动，且目标已删除该项或其父级。

== MoveDeleteConflictSourceOperation ==
已从 {0} 移动到 {1}

== MoveIgnoredDestinationWasntSelected ==
不会移动项 '{0}'，因为未选择移动的目标 '{1}'。请更新工作区或选择移动的源和目标，然后重试该操作。

== MoveIgnoredSourceWasntSelected ==
不会移动项 '{0}'，因为虽然选择了移动的目标 '{1}'，但未选择该项。请更新工作区或选择移动的源和目标，然后重试该操作。

== MoveItemAlreadyChanged ==
不会移动该项，因为在工作区 '{0}' 中已更改（已签出、已在本地更改、已删除、已移动或已还原）该项。请撤销或签入本地更改，然后重试该操作。

== MoveNotApplicable ==
无法将该项移动到 '{0}'。可能的原因是目标路径已被使用。请卸载该项（从配置视图中），然后重试该操作。

== MoveSourceDelete ==
移出已删除项冲突

== MoveSourceDeleteConflictDestinationOperation ==
已删除 {0}

== MoveSourceDeleteConflictSourceOperation ==
已从 {0} 移动到 {1}

== Moved ==
已移动

== MovedAddedConflictDestinationOperation ==
已添加 {0}

== MovedAddedConflictExplanation ==
一个项在源上已移动，并在目标上的同一位置添加了一个同名的项。

== MovedAddedConflictSourceOperation ==
已从 {0} 移动到 {1}

== MovedEvilTwin ==
已移动双面恶魔冲突

== MovedEvilTwinConflictActions1 ==
保留这两种更改，将目标重命名为

== MovedEvilTwinConflictActions2 ==
保留源上完成的移动（放弃目标上的移动）

== MovedEvilTwinConflictActions3 ==
保留目标上完成的移动（放弃源上的移动）

== MovedEvilTwinConflictDestinationOperation ==
已从 {0} 移动到 {1}

== MovedEvilTwinConflictExplanation ==
同名的两个不同项已移动到源和目标上的同一位置。

== MovedEvilTwinConflictSourceOperation ==
已从 {0} 移动到 {1}

== MovedLocally ==
已在本地移动

== MsgBinaryFile ==
二进制文件

== MsgDirectory ==
目录

== MsgItemAdded ==
受 Unity VCS 控制并已在最近添加。

== MsgItemAddedNotOnDisk ==
受 Unity VCS 控制并已在最近添加，但不在磁盘上。

== MsgItemChanged ==
受 Unity VCS 控制并已在本地更改。

== MsgItemCheckedin ==
受 Unity VCS 控制并已签入。

== MsgItemCheckedinNotOnDisk ==
受 Unity VCS 控制并已签入，但不在磁盘上。

== MsgItemCheckedout ==
受 Unity VCS 控制并已签出。

== MsgItemCheckedoutNotOnDisk ==
受 Unity VCS 控制并已签出，但不在磁盘上。

== MsgItemIgnored ==
已忽略。

== MsgItemNotOnDisk ==
不在磁盘上。

== MsgItemPrivate ==
私有。

== MsgItemStatus ==
项 {0} 的状态为：{1}

== MsgLinkFile ==
链接

== MsgPendingDstMergelink ==
至 {1}@{2}@{3} 的 cs:{0}

== MsgPendingMergelink ==
{0}（从 {2}@{3}@{4} 的 cs:{1}）

== MsgPrivFile ==
文件

== MsgPrivate ==
私有

== MsgTextFile ==
文本文件

== MsgUnknown ==
未知

== MsgWorkspaceWorkingInfo ==

      存储库：{0}
分支：{1}
      标签：{2}
    

== Multiple ==
多个

== NameAlreadyInDirectory ==
名称 {0} 已在目录中

== NameNotValid ==
名称 {0} 不再有效

== NewBrNameParamNotBrspec ==
新名称参数必须包含分支名称，而不是分支规格

== NewNameFor ==
{0} 的新名称： 

== NewNameInstead ==
新名称而不是 {0}： 

== No ==
否

== NoChangesApplied ==
未应用更改

== NoChangesInWk ==
工作区 {0} 中没有更改

== NoErrorsDetected ==
未检测到错误。

== NoValidAnswers ==
n;否

== NodeChangeAdded ==
已添加 {0}

== NodeChangeCopied ==
已复制 {0}

== NodeChangeDeleted ==
已删除 {0}

== NodeChangeModified ==
已修改 {0}

== NodeChangeModifiedAndMoved ==
已修改并已从 {0} 移动到 {1}

== NodeChangeMoved ==
从 {0} 移动到 {1}

== NodeChangeReplaced ==
已替换 {0}

== NotMergedFile ==
未合并文件 {0} / {1}：{2}

== Of ==
/

== OldRenameRenameNames ==
旧名称 {0}，重命名第一个 {1}，重命名第二个 {2}

== On ==
位于

== OnlyModifiedOnSource ==
已处理 {0} / {1} 个仅在源上已更改的文件：{2}

== OperationAbandoned ==
已放弃

== OperationDisabled ==
在 Plastic SCM 5.0 中已禁用此操作

== OperationStartingFetch ==
正在开始提取

== OperationStartingPush ==
正在开始推送

== Owner ==
所有者

== PasswordCantBeEmpty ==
密码不能为空。未设置密码。

== PasswordCorrectlySet ==
已正确设置密码

== PasswordDoesntMatch ==
密码不匹配。未设置密码。

== PathInConflictWarningMessage ==
已将项 '{0}' 重命名为 '{1}'，因为路径在合并操作期间已在使用中

== PathNotFound ==
在合并冲突中找不到路径 {0}

== PathNotFoundInDiff ==
在差异比较集内找不到路径 {0}

== PathNotUnique ==
路径 '{0}' 不是唯一的。它可能指代任何...

== PathsFsLocked ==
该操作无法开始，因为某些文件或目录已被另一个程序锁定。请关闭这个程序，然后重试。

== PendingMergeLinks ==
待定合并链接

== PerformingSwitch ==
正在执行切换操作...

== PermissionKey ==
权限

== Private ==
私有

== ProcessingBranches ==
正在处理分支

== ProcessingDirectoryConflicts ==
正在处理目录冲突

== ProcessingDirectoryOperations ==
正在处理目录操作

== ProcessingDirectoryOperationsApplyingFsProtections ==
正在处理目录操作（应用文件系统保护）

== ProcessingDirectoryOperationsDownloadingRevisions ==
正在处理目录操作（下载修订）

== ProcessingDirectoryOperationsUpdatingWorkspace ==
正在处理目录操作（更新工作区）

== PushingAcls ==
正在推送 ACL

== PushingAttributes ==
正在推送属性

== PushingBranches ==
正在推送分支

== PushingChangesets ==
正在推送变更集

== PushingChildren ==
正在推送树

== PushingFinished ==
已完成元数据推送

== PushingItems ==
正在推送项

== PushingLabels ==
正在推送标签

== PushingLinks ==
正在推送链接

== PushingMetadata ==
正在推送元数据

== PushingMoveRealizations ==
正在推送移动

== PushingReferences ==
正在推送引用

== PushingRevisions ==
正在推送修订

== Read ==
控制是否可读取对象

== ReconcilingAcls ==
正在协调 ACL

== ReconcilingObjects ==
正在协调对象

== RemoveTubeSucceeded ==
已正确删除 Tube {0} -> {1}。

== Removed ==
已删除

== RemovedLocally ==
已在本地删除

== Rename ==
控制是否可重命名对象

== RenamedOldNameConflict ==
一个项被重命名为仍在使用中的名称。

== RenamedRenamedConflict ==
一个项在两个参与者中均已重命名。

== RenamedSameNameConflict ==
两个项被重命名为相同的名称

== RenamedToDownloadFile ==
文件 {0} 将被重命名以便下载新版本。变更集：{1}

== RenamesNotValidIntroduce ==
重命名不再有效，您必须手动引入新名称

== Replaced ==
已替换

== Replicateread ==
控制复制读取操作

== Replicatewrite ==
控制复制写入操作

== ReportCmdMsgFileChangedInWk ==
此文件在工作区中已更改，不受 Unity VCS 控制，因此已保留数据。项不是最新状态。如果要更新，请尝试强制更新此项

== ReportCmdMsgUpdateStoringCheckout ==
正在安全存储此项的已签出数据

== Repository ==
存储库

== RepositorySpec ==
存储库规格

== ResolutionConfigured ==
已使用配置的解决方案解决了该冲突。

== ResolvedCreatedConflict ==
在解决冲突 {1} 之后，已产生冲突 {0}

== ResolvedRemovedConflict ==
在解决冲突 {1} 之后，已删除冲突 {0}

== ReviewStatusReviewed ==
已审查

== ReviewStatusReworkRequired ==
需要返工

== ReviewStatusUnderReview ==
正在审查

== RevisionCheckedout ==
本地

== RevisionHistoryOf ==
{0} 的修订历史记录

== RevisionNotExists ==
修订不存在

== RevisionNotFound ==
找不到要加载的修订

== RevisionNumber ==
修订编号

== RevisionSpec ==
修订规格

== RevisionType ==
修订类型

== Rm ==
控制是否可以从存储库中删除文件和目录

== Rmattr ==
允许用户删除属性

== Rmchangeset ==
控制是否可以从分支中删除变更集

== Rmlabel ==
允许用户删除标签

== Rmrepository ==
允许用户删除存储库

== Rmtrigger ==
允许用户删除触发器

== SearchingForChanged ==
正在搜索工作区中已更改的项...

== Second ==
第二个

== SelectorNoChanges ==
无更改。

== Server ==
服务器

== SetConfigSucceeded ==
已正确设置 Plastic Tube 配置。

== SettingNewSelector ==
正在设置新的选择器...

== ShareRepoSucceeded ==
已正确共享存储库 '{0}'。

== Shelve ==
搁置

== Skip ==
跳过

== Source ==
源

== SourceDestinationChanged ==
项 {0} 在源上已更改，但在合并期间已将该项丢弃，因为该项在源和目标上的内容相同

== SourceDestinationChangedFsprotection ==
项 {0} 的文件系统保护在源上已更改，但在合并期间已将该项丢弃，因为该项在源和目标上的文件系统保护相同

== SourceDestinationDeleted ==
项 {0} 在源上已删除，但在合并期间已将该项丢弃，因为该项在目标上已删除

== SourceDestinationMoved ==
文件 {0} 在源和目标上已移动到同一位置，因此将放弃移动。已从 {1} 移动到 {2}

== SrcContributor ==
源变更集：{0}（分支 {1}）

== StartResolveConflict ==
即将解决以下冲突：

== StatColLastmod ==
上次修改时间

== StatColPath ==
路径

== StatColSimilarity ==
相似度

== StatColSize ==
大小

== StatColStatus ==
状态

== StatusKey ==
状态

== StatusPerfWarningChanged ==
查找已更改文件用了太长时间。性能提示：{0}

== StatusPerfWarningMoved ==
计算已移动文件用了太长时间。您有太多私有/已删除文件。性能提示：{0}

== StatusPerfWarningPrivates ==
您有太多私有文件，这可能会影响性能。了解如何忽略私有文件：{0}

== SubtractiveIntervalMerge ==
减法间隔合并

== SubtractiveMerge ==
减法合并

== SubtractiveNeeded ==
项 {0} 需要从 {1} 到 {2} 基项 {3} 的减法合并。更改者：{4}

== SupportBundleCreated ==
已在 {0} 中创建支持捆绑包

== SupportBundleCreating ==
正在创建新的支持捆绑包...

== SupportContactUs ==
请注意，您可以随时通过 https://www.plasticscm.com/support 联系我们。
如有任何疑问、建议或需要指导，请随时
与我们联系。
    

== SyncAlreadyReplicated ==
同步无法开始，因为目标存储库是从与 Git 同步的存储库复制的。最初同步的存储库为 '{0} - {1}'。请联系支持人员以获取更多信息。

== To ==
至

== Total ==
总计

== TubeStatusConnected ==
Unity VCS 服务器 ({0}) 已连接到 Plastic Tube。

== TubeStatusDisconnected ==
Unity VCS 服务器 ({0}) 未连接到 Plastic Tube。

== TypeNewPassword ==
新密码： 

== TypeOldPassword ==
旧密码： 

== UnableToRemoveCset ==
无法删除所选变更集 {0}。

== UndoUnableToMove ==
无法将文件 '{0}' 移回到 '{1}'：{2}

在撤销更改之前，您可能需要包括已删除的项。请检查您当前的选项：

- 如果使用的是 CLI，请尝试向“undo”命令添加已删除的路径或“--deleted”参数。
- 如果使用的是 GUI，请在“待定更改”视图中打开“选项”对话框，然后在“显示内容”选项卡中选中“显示已删除文件和目录”。

== UndoneAddOperation ==
本地添加操作 {0} 将被撤销。

== UndoneDeleteOperation ==
本地删除操作 {0} 将被撤销。

== UndoneMoveOperation ==
本地移动操作 {0} -> {1} 将被撤销。

== UnknownError ==
未知错误

== Unlimited ==
无限制

== UnloadItemAlreadyChanged ==
不会卸载该项，因为在工作区 '{0}' 中已更改（已签出、已在本地更改、已删除、已移动或已还原）该项。请撤销或签入本地更改，然后重试该操作。

== UnneededMergeRevisionLoaded ==
不再需要进行 '{0}' 的复制合并，因为已加载正确的修订。

== UnresolvedXlink ==
未解析的 Xlink

== UnshareRepoSucceeded ==
已正确取消存储库 '{0}' 的共享。

== UnsupportedMergeType ==
不支持的合并类型

== UpdateDeletedSelectorObjectSkipped ==
无法将您的工作区更新为有效的分支配置。可能其他人删除了您的工作区所设置的搁置或标签。请将您的工作区切换到现有的分支、变更集、标签或搁置集。

== UpdateProgress ==
已更新 {0} / {1}（{2} / {3} 个文件待下载 / {4} / {5} 项操作待应用）

== UpdateProgressCalculating ==
正在计算...

== UpdateProgressDownloadingBigFile ==
正在从 {2} 下载文件 {0} ({1})

== UpdateProgressDownloadingBlock ==
正在从 {2} 下载 {0} 个文件的块 ({1})

== UpdateProgressUpdating ==
正在更新...

== UpdateStatusCalculating ==
正在计算

== UpdateStatusFinished ==
已完成

== UpdateStatusUpdating ==
正在更新

== UpdateWithChangesFromGluonWorkspaceNotAllowed ==
无法从 Gluon 工作区切换到带有签出项的标准工作区。您可以从 Gluon 签入（或撤销），然后重试更新。

== UpdateWkIsUpToDate ==
工作区 {0} 是最新状态（变更集：{1}）

== UpdatingWorkspace ==
Unity VCS 正在更新您的工作区。请稍等片刻...

== UploadingFiles ==
正在上传 {0} 个文件

== User ==
用户

== UserCorrectlyActivated ==
已成功激活用户 {0}

== UserCorrectlyDeactivated ==
已成功停用用户 {0}

== UserInformation ==
许可证信息：服务器：{0}

== UserKey ==
用户

== View ==
控制是否可查看对象

== VirtualPathDecorator ==
（虚拟上级，cs:{0} 上的修订）

== WaitingOperation ==
此操作可能需要几分钟。请稍候...

== Warning ==
警告

== WhichChange ==
要更改哪个元素？（源|目标）[1|2]

== WhichRename ==
要使用哪个重命名？[1|2]

== WillCreateRepo ==
存储库 {0} 不存在。系统将创建此存储库

== WorkspacestatusAddGrp ==
已添加项（AD = 已添加、CP = 已复制（新）、PR = 私有、IG = 已忽略）

== WorkspacestatusChGrp ==
已修改项（CH = 已更改、CO = 已签出、RP = 已替换）

== WorkspacestatusMvGrp ==
已移动项（MV = 已移动、LM = 已在本地移动）

== WorkspacestatusRmGrp ==
已删除项（DE = 已删除、LD = 已在本地删除）

== XlinkConflict ==
Xlink 冲突

== XlinkConflictActions1 ==
在 Xlink 中保留源更改

== XlinkConflictActions2 ==
在 Xlink 中保留目标更改

== XlinkConflictDestinationOperation ==
已将 Xlink 更改为：{0}

== XlinkConflictExplanation ==
在源和目标上已更改 Xlink。

== XlinkConflictSourceOperation ==
已将 Xlink 更改为：{0}

== XlinkWritableConflict ==
Xlink 缺少上级冲突

== XlinkWritableConflictExplanation ==
通过 Xlink 链接的存储库无法识别用于计算合并的上级变更集。用户必须手动指定上级变更集。

== Yes ==
是

== YesValidAnswers ==
y;是

== CmdConfigureErrorCannotCreateDir ==
Failed when creating a directory [{0}]:{1}

== CmdConfigureErrorWrongParameters ==
错误的参数编号。请键入 cm configure --help 以获取帮助

== CmdConfigureErrorReadingParameters ==
读取参数时出错；请检查提供的参数列表。

== CmdConfigureHeader ==
####--- 客户端配置向导 ---####

== CmdConfigureSuccess ==
已正确配置 Unity VCS 客户端。

== CmdConfigureDetectedWorkingMode ==
检测到工作模式：{0}

== CmdConfigureDetectedWorkingModeError ==
检查 Unity VCS 服务器安全模式时出错。
可能服务器处于脱机状态，或者指定的地址错误。
是否要重新输入 Unity VCS 服务器地址？

== CmdConfigureServerParams ==
配置 Unity VCS 服务器地址/端口：

== CmdConfigureServerParamsAddress ==
Unity VCS 服务器地址 [{0}]

== CmdConfigureServerParamsPort ==
Unity VCS 服务器端口 [{0}]

== CmdConfigureServerParamsSslPort ==
Unity VCS SSL 服务器端口 [{0}]

== CmdConfigureServerParamsUseSsl ==
使用加密 (SSL)？

== CmdConfigureProxyParams ==
配置代理服务器？

== CmdConfigureProxyParamsAddress ==
代理服务器地址

== CmdConfigureProxyParamsPort ==
代理服务器端口
