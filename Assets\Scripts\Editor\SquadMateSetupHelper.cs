using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;

/// <summary>
/// Helper script to automatically attach components and configure the SquadMate training setup
/// </summary>
public class SquadMateSetupHelper : EditorWindow
{
    [MenuItem("SquadMate AI/Setup Components")]
    public static void ShowWindow()
    {
        GetWindow<SquadMateSetupHelper>("SquadMate Setup Helper");
    }

    private void OnGUI()
    {
        GUILayout.Label("SquadMate Component Setup Helper", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This will automatically attach and configure:");
        GUILayout.Label("• PlayerController to Player GameObject");
        GUILayout.Label("• SquadMateAgent to SquadMate GameObject");
        GUILayout.Label("• RewardCalculator to SquadMate GameObject");
        GUILayout.Label("• GameEnvironment to Environment GameObject");
        GUILayout.Label("• Configure Behavior Parameters");

        GUILayout.Space(20);

        if (GUILayout.Button("Auto-Setup All Components", GUILayout.Height(40)))
        {
            SetupAllComponents();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("Fix Behavior Parameters", GUILayout.Height(30)))
        {
            FixBehaviorParameters();
        }

        if (GUILayout.Button("Assign Environment Prefabs", GUILayout.Height(30)))
        {
            AssignEnvironmentPrefabs();
        }
    }

    public static void SetupAllComponents()
    {
        Debug.Log("🔧 Setting up SquadMate components...");

        // Setup Player
        SetupPlayer();

        // Setup SquadMate
        SetupSquadMate();

        // Setup Environment
        SetupEnvironment();

        // Fix Behavior Parameters
        FixBehaviorParameters();

        // Assign prefabs
        AssignEnvironmentPrefabs();

        Debug.Log("✅ SquadMate setup completed!");
        Debug.Log("🎮 Ready to start training!");
    }

    private static void SetupPlayer()
    {
        GameObject player = GameObject.Find("Player");
        if (player == null)
        {
            Debug.LogWarning("⚠️ Player GameObject not found. Please create the scene first.");
            return;
        }

        // Add PlayerController if not present
        PlayerController playerController = player.GetComponent<PlayerController>();
        if (playerController == null)
        {
            player.AddComponent<PlayerController>();
            Debug.Log("✅ Added PlayerController to Player");
        }

        // Ensure Rigidbody is configured correctly
        Rigidbody rb = player.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
        }
    }

    private static void SetupSquadMate()
    {
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate == null)
        {
            Debug.LogWarning("⚠️ SquadMate GameObject not found. Please create the scene first.");
            return;
        }

        // Add SquadMateAgent if not present
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent == null)
        {
            squadmate.AddComponent<SquadMateAgent>();
            Debug.Log("✅ Added SquadMateAgent to SquadMate");
        }

        // Add RewardCalculator if not present
        RewardCalculator rewardCalc = squadmate.GetComponent<RewardCalculator>();
        if (rewardCalc == null)
        {
            squadmate.AddComponent<RewardCalculator>();
            Debug.Log("✅ Added RewardCalculator to SquadMate");
        }

        // Add SquadMateDecisionTree if not present
        SquadMateDecisionTree decisionTree = squadmate.GetComponent<SquadMateDecisionTree>();
        if (decisionTree == null)
        {
            squadmate.AddComponent<SquadMateDecisionTree>();
            Debug.Log("✅ Added SquadMateDecisionTree to SquadMate");
        }

        // Ensure Rigidbody is configured correctly
        Rigidbody rb = squadmate.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
        }

        // Configure references
        ConfigureSquadMateReferences(squadmate);
    }

    private static void SetupEnvironment()
    {
        GameObject environment = GameObject.Find("Environment");
        if (environment == null)
        {
            Debug.LogWarning("⚠️ Environment GameObject not found. Please create the scene first.");
            return;
        }

        // Add GameEnvironment if not present
        GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
        if (gameEnv == null)
        {
            environment.AddComponent<GameEnvironment>();
            Debug.Log("✅ Added GameEnvironment to Environment");
        }

        // Add ObjectSpawner if not present
        ObjectSpawner spawner = environment.GetComponent<ObjectSpawner>();
        if (spawner == null)
        {
            environment.AddComponent<ObjectSpawner>();
            Debug.Log("✅ Added ObjectSpawner to Environment");
        }
    }

    private static void FixBehaviorParameters()
    {
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate == null) return;

        BehaviorParameters behaviorParams = squadmate.GetComponent<BehaviorParameters>();
        if (behaviorParams == null)
        {
            behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        }

        // Configure behavior parameters
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;

        // Note: In newer ML-Agents versions, observation and action spaces are defined in the agent script
        // The BehaviorParameters component mainly handles the behavior name and type

        Debug.Log("✅ Configured Behavior Parameters:");
        Debug.Log($"   • Behavior Name: {behaviorParams.BehaviorName}");
        Debug.Log($"   • Behavior Type: {behaviorParams.BehaviorType}");
        Debug.Log($"   • Team ID: {behaviorParams.TeamId}");
        Debug.Log("   • Observation/Action spaces defined in SquadMateAgent script");
    }

    private static void ConfigureSquadMateReferences(GameObject squadmate)
    {
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent == null) return;

        // Find and assign player reference
        GameObject player = GameObject.Find("Player");
        if (player != null)
        {
            agent.player = player.transform;
            Debug.Log("✅ Assigned Player reference to SquadMateAgent");
        }

        // Find and assign environment reference
        GameObject environment = GameObject.Find("Environment");
        if (environment != null)
        {
            GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
            if (gameEnv != null)
            {
                agent.environment = gameEnv;
                Debug.Log("✅ Assigned Environment reference to SquadMateAgent");
            }
        }
    }

    private static void AssignEnvironmentPrefabs()
    {
        GameObject environment = GameObject.Find("Environment");
        if (environment == null) return;

        GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
        if (gameEnv == null) return;

        // Load prefabs
        GameObject enemyPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Enemy.prefab");
        GameObject medkitPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Medkit.prefab");
        GameObject weaponPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Weapon.prefab");

        // Assign prefabs
        if (enemyPrefab != null)
        {
            gameEnv.enemyPrefab = enemyPrefab;
            Debug.Log("✅ Assigned Enemy prefab");
        }

        if (medkitPrefab != null)
        {
            gameEnv.medkitPrefab = medkitPrefab;
            Debug.Log("✅ Assigned Medkit prefab");
        }

        if (weaponPrefab != null)
        {
            gameEnv.weaponPrefab = weaponPrefab;
            Debug.Log("✅ Assigned Weapon prefab");
        }

        // Find and assign player and squadmate references
        GameObject player = GameObject.Find("Player");
        GameObject squadmate = GameObject.Find("SquadMate");

        if (player != null)
        {
            gameEnv.player = player.transform;
            Debug.Log("✅ Assigned Player to Environment");
        }

        if (squadmate != null)
        {
            gameEnv.squadMate = squadmate.GetComponent<SquadMateAgent>();
            Debug.Log("✅ Assigned SquadMate to Environment");
        }

        // Find and assign spawn points
        Transform[] spawnPoints = new Transform[10];
        for (int i = 0; i < 10; i++)
        {
            GameObject spawnPoint = GameObject.Find($"SpawnPoint_{i + 1}");
            if (spawnPoint != null)
            {
                spawnPoints[i] = spawnPoint.transform;
            }
        }
        gameEnv.spawnPoints = spawnPoints;
        Debug.Log($"✅ Assigned {spawnPoints.Length} spawn points");

        // Configure environment settings
        gameEnv.environmentSize = new Vector3(50f, 0f, 50f);
        gameEnv.maxEnemies = 3;
        gameEnv.maxMedkits = 2;
        gameEnv.maxWeapons = 2;

        Debug.Log("✅ Environment configuration completed!");
    }
}

/// <summary>
/// Menu item to quickly test the setup
/// </summary>
public class SquadMateQuickTest
{
    [MenuItem("SquadMate AI/Quick Test Setup")]
    public static void QuickTest()
    {
        Debug.Log("🧪 Running SquadMate Quick Test...");

        // Check if all required components are present
        GameObject player = GameObject.Find("Player");
        GameObject squadmate = GameObject.Find("SquadMate");
        GameObject environment = GameObject.Find("Environment");

        bool allGood = true;

        if (player == null)
        {
            Debug.LogError("❌ Player GameObject not found");
            allGood = false;
        }
        else if (player.GetComponent<PlayerController>() == null)
        {
            Debug.LogWarning("⚠️ PlayerController missing from Player");
            allGood = false;
        }

        if (squadmate == null)
        {
            Debug.LogError("❌ SquadMate GameObject not found");
            allGood = false;
        }
        else
        {
            if (squadmate.GetComponent<SquadMateAgent>() == null)
            {
                Debug.LogWarning("⚠️ SquadMateAgent missing from SquadMate");
                allGood = false;
            }
            if (squadmate.GetComponent<BehaviorParameters>() == null)
            {
                Debug.LogWarning("⚠️ BehaviorParameters missing from SquadMate");
                allGood = false;
            }
        }

        if (environment == null)
        {
            Debug.LogError("❌ Environment GameObject not found");
            allGood = false;
        }
        else if (environment.GetComponent<GameEnvironment>() == null)
        {
            Debug.LogWarning("⚠️ GameEnvironment missing from Environment");
            allGood = false;
        }

        if (allGood)
        {
            Debug.Log("✅ All components are properly configured!");
            Debug.Log("🎮 Ready to start training! Press Play in Unity.");
        }
        else
        {
            Debug.Log("🔧 Use 'SquadMate AI → Setup Components' to fix issues");
        }
    }
}
