{"name": "com.unity.barracuda", "displayName": "Barr<PERSON><PERSON>", "version": "3.0.0", "unity": "2019.4", "description": "Barracuda is lightweight and cross-platform Neural Net inference library. Barracuda supports inference both on GPU and CPU.", "dependencies": {"com.unity.burst": "1.6.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}, "upm": {"changelog": "### Changed,- Combined verified release of 2.1.x-2.4.x improvements"}, "upmCi": {"footprint": "54ea95996e6dc22d83c61da4a96a507c19833ce0"}, "repository": {"url": "https://github.cds.internal.unity3d.com/unity/UnityInferenceEngine.git", "type": "git", "revision": "26d48d6d408d03dc87eab5c945a97d2e155877d2"}, "_fingerprint": "82bcfb51cf288487e9cfe313123e22a67d1dcb68"}