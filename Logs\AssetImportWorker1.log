Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.6f1 (d64b1a599cad) revision 14043930'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 16235 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-12T09:20:07Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Squadmate
-logFile
Logs/AssetImportWorker1.log
-srvPort
50918
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Squadmate
C:/Squadmate
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22148]  Target information:

Player connection [22148]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2862616905 [EditorId] 2862616905 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9I4BNV2) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22148] Host joined multi-casting on [***********:54997]...
Player connection [22148] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 7.15 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.6f1 (d64b1a599cad)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Squadmate/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce GTX 1050 Ti (ID=0x1c8c)
    Vendor:          NVIDIA
    VRAM:            4004 MB
    App VRAM Budget: 3403 MB
    Driver:          32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56048
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004601 seconds.
- Loaded All Assemblies, in  0.799 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 1451ms
	BeginReloadAssembly (295ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (94ms)
	LoadAllAssembliesAndSetupDomain (318ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (310ms)
				TypeCache.ScanAssembly (287ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (592ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (103ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (242ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
