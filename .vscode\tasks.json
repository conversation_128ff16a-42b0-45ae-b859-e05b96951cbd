{"version": "2.0.0", "tasks": [{"label": "Unity: Generate Project Files", "type": "shell", "command": "Unity", "args": ["-batchmode", "-quit", "-projectPath", "${workspaceFolder}", "-<PERSON><PERSON><PERSON><PERSON>", "UnityEditor.SyncVS.SyncSolution"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Generate Unity project files for VS Code IntelliSense"}, {"label": "ML-Agents: <PERSON><PERSON>te <PERSON>up", "type": "shell", "command": "python", "args": ["validate_setup.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Validate ML-Agents installation and project setup"}, {"label": "ML-Agents: Unity 6 Quick Start", "type": "shell", "command": "python", "args": ["unity6_quickstart.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "Run Unity 6 optimized setup validation"}, {"label": "ML-Agents: Start Training (Standard)", "type": "shell", "command": "python", "args": ["train_squadmate.py", "--run-id=squadmate_vscode", "--tensorboard"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "Start ML-Agents training with TensorBoard monitoring"}, {"label": "ML-Agents: Start Training (Unity 6 Optimized)", "type": "shell", "command": "python", "args": ["train_squadmate.py", "--config=config/squadmate_unity6_config.yaml", "--run-id=squadmate_unity6_vscode", "--tensorboard"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "Start Unity 6 optimized ML-Agents training"}, {"label": "ML-Agents: Resume Training", "type": "shell", "command": "python", "args": ["train_squadmate.py", "--run-id=squadmate_vscode", "--resume"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "Resume previous training session"}, {"label": "ML-Agents: Export Model", "type": "shell", "command": "python", "args": ["train_squadmate.py", "--export", "--run-id=squadmate_vscode"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Export trained model to ONNX format"}, {"label": "TensorBoard: Start Monitoring", "type": "shell", "command": "tensorboard", "args": ["--logdir=results", "--port=6006", "--host=localhost"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Start TensorBoard for training monitoring", "isBackground": true}, {"label": "Python: Install ML-Agents Dependencies", "type": "shell", "command": "python", "args": ["fix_dependencies.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "Install or fix ML-Agents Python dependencies"}, {"label": "Unity: Clean Project", "type": "shell", "command": "powershell", "args": ["-Command", "Remove-Item -Recurse -Force Library, Temp, Obj -ErrorAction SilentlyContinue; Write-Host 'Unity project cleaned'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Clean Unity project temporary files"}, {"label": "Git: Initialize Repository", "type": "shell", "command": "git", "args": ["init"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Initialize Git repository for version control"}]}