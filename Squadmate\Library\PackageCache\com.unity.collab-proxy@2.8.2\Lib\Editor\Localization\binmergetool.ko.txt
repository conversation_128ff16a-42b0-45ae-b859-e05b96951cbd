﻿== Error ==
오류

== NoResultFileSelected ==
결과 파일이 선택되지 않았습니다

== UsageCaption ==
사용법

== Usage ==
사용법: binmergetool <mergeOptions>

    mergeOptions: <generalFiles> <baseFile> [<baseSymbolicName>] [[<automatic>] <resultFile>] [<mergeType>] [<generalOptions>]

        baseFile:             {-b | --base}=<filename>
        baseSymbolicName:     {-bn | --basesymbolicname}=<symbolicname>
        automatic:            -a | --automatic
        resultFile:           {-r | --result}=<filename>
        mergeType:            {-m | --mergeresolutiontype}={onlyone | onlysrc | onlydst | try | forced}

    generalFiles:<sourceFile> [<srcSymbolicName>] <destination> [<dstSymbolicName>]

        sourceFile:           {-s | --source}=<filename>
        srcSymbolicName:      {-sn | --srcsymbolicname}=<symbolicname>
        destinationFile:      {-d | --destination}=<filename>
        dstSymbolicName:      {-dn | --dstsymbolicname}=<symbolicname>

        
    예:
        
        binmergetool -s=file1.txt -d=file2.txt
        binmergetool -s=file1.txt -b=file0.txt --destination=file2.txt
        binmergetool --base=file0.txt -d=file2.txt --source=file1.txt --automatic --result=result.txt
        binmergetool -b=file0.txt -s=file1.txt -d=file2.txt -a -r=result.txt -m=onlyone

== DiffRequiresTwoArguments ==
차이점에는 파일 2개 및 파일 2개만이 필요합니다

== UnsupportedFileTypeForDiff ==
바이너리 비교를 지원하지 않는 파일입니다. 이미지만 지원됩니다(JPEG, PNG, GIF, BMP)

== SaveChanges ==
결과 파일에 변경사항을 저장하시겠습니까?

== ExitPrompt ==
종료

== CantLoadImage ==
'{0}' 이미지를 로드할 수 없습니다.

== NoArguments ==
지정된 인수가 없습니다

== UsageHint ==
이 유틸리티에 대한 도움말을 표시하려면 [binmergetool -?]를 사용하십시오