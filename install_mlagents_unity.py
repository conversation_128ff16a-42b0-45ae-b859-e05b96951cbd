#!/usr/bin/env python3
"""
ML-Agents Unity Package Installation Helper
Downloads and sets up ML-Agents for Unity 6 when Package Manager has connectivity issues
"""

import os
import sys
import urllib.request
import zipfile
import json
import shutil
from pathlib import Path

def download_mlagents_package():
    """Download ML-Agents package from GitHub"""
    print("📦 Downloading ML-Agents package...")
    
    # ML-Agents GitHub release URL
    url = "https://github.com/Unity-Technologies/ml-agents/archive/refs/heads/release_21.zip"
    filename = "ml-agents-release_21.zip"
    
    try:
        print(f"Downloading from: {url}")
        urllib.request.urlretrieve(url, filename)
        print(f"✅ Downloaded: {filename}")
        return filename
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return None

def extract_package(zip_filename):
    """Extract the ML-Agents package"""
    print("📂 Extracting ML-Agents package...")
    
    try:
        with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
            zip_ref.extractall("temp_mlagents")
        
        print("✅ Package extracted")
        return "temp_mlagents"
    except Exception as e:
        print(f"❌ Extraction failed: {e}")
        return None

def setup_unity_package():
    """Set up ML-Agents package for Unity"""
    print("🔧 Setting up Unity package...")
    
    # Create Packages directory if it doesn't exist
    packages_dir = Path("Packages")
    packages_dir.mkdir(exist_ok=True)
    
    # Find the extracted ML-Agents package
    mlagents_source = None
    for root, dirs, files in os.walk("temp_mlagents"):
        if "com.unity.ml-agents" in dirs:
            mlagents_source = os.path.join(root, "com.unity.ml-agents")
            break
    
    if not mlagents_source:
        print("❌ Could not find com.unity.ml-agents in extracted files")
        return False
    
    # Copy to Packages directory
    target_dir = packages_dir / "com.unity.ml-agents"
    if target_dir.exists():
        shutil.rmtree(target_dir)
    
    shutil.copytree(mlagents_source, target_dir)
    print(f"✅ Copied ML-Agents to: {target_dir}")
    
    return True

def update_manifest():
    """Update Unity's package manifest"""
    print("📝 Updating package manifest...")
    
    manifest_path = Path("Packages/manifest.json")
    
    # Default manifest for Unity 6
    default_manifest = {
        "dependencies": {
            "com.unity.ml-agents": "file:./com.unity.ml-agents",
            "com.unity.collab-proxy": "2.0.7",
            "com.unity.feature.development": "1.0.1",
            "com.unity.textmeshpro": "3.0.6",
            "com.unity.timeline": "1.7.5",
            "com.unity.ugui": "1.0.0",
            "com.unity.visualscripting": "1.9.4",
            "com.unity.modules.ai": "1.0.0",
            "com.unity.modules.androidjni": "1.0.0",
            "com.unity.modules.animation": "1.0.0",
            "com.unity.modules.assetbundle": "1.0.0",
            "com.unity.modules.audio": "1.0.0",
            "com.unity.modules.cloth": "1.0.0",
            "com.unity.modules.director": "1.0.0",
            "com.unity.modules.imageconversion": "1.0.0",
            "com.unity.modules.imgui": "1.0.0",
            "com.unity.modules.jsonserialize": "1.0.0",
            "com.unity.modules.particlesystem": "1.0.0",
            "com.unity.modules.physics": "1.0.0",
            "com.unity.modules.physics2d": "1.0.0",
            "com.unity.modules.screencapture": "1.0.0",
            "com.unity.modules.terrain": "1.0.0",
            "com.unity.modules.terrainphysics": "1.0.0",
            "com.unity.modules.tilemap": "1.0.0",
            "com.unity.modules.ui": "1.0.0",
            "com.unity.modules.uielements": "1.0.0",
            "com.unity.modules.umbra": "1.0.0",
            "com.unity.modules.unityanalytics": "1.0.0",
            "com.unity.modules.unitywebrequest": "1.0.0",
            "com.unity.modules.unitywebrequestassetbundle": "1.0.0",
            "com.unity.modules.unitywebrequestexture": "1.0.0",
            "com.unity.modules.unitywebrequestwww": "1.0.0",
            "com.unity.modules.vehicles": "1.0.0",
            "com.unity.modules.video": "1.0.0",
            "com.unity.modules.vr": "1.0.0",
            "com.unity.modules.wind": "1.0.0",
            "com.unity.modules.xr": "1.0.0"
        }
    }
    
    try:
        # Read existing manifest if it exists
        if manifest_path.exists():
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
        else:
            manifest = default_manifest
        
        # Add ML-Agents dependency
        if "dependencies" not in manifest:
            manifest["dependencies"] = {}
        
        manifest["dependencies"]["com.unity.ml-agents"] = "file:./com.unity.ml-agents"
        
        # Write updated manifest
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print("✅ Updated manifest.json")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update manifest: {e}")
        return False

def cleanup():
    """Clean up temporary files"""
    print("🧹 Cleaning up...")
    
    # Remove temporary files
    temp_files = ["ml-agents-release_21.zip", "temp_mlagents"]
    
    for item in temp_files:
        if os.path.exists(item):
            if os.path.isfile(item):
                os.remove(item)
            else:
                shutil.rmtree(item)
            print(f"🗑️ Removed: {item}")

def remove_stub_file():
    """Remove the temporary ML-Agents stub file"""
    stub_file = Path("Assets/Scripts/Temp/MLAgentsStubs.cs")
    if stub_file.exists():
        stub_file.unlink()
        print("🗑️ Removed ML-Agents stub file")
        
        # Remove Temp directory if empty
        temp_dir = stub_file.parent
        if temp_dir.exists() and not any(temp_dir.iterdir()):
            temp_dir.rmdir()
            print("🗑️ Removed empty Temp directory")

def check_unity_project():
    """Check if we're in a Unity project directory"""
    required_dirs = ["Assets", "ProjectSettings"]
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"❌ Not in Unity project root (missing {dir_name} directory)")
            return False
    
    print("✅ Unity project detected")
    return True

def main():
    print("🎮 ML-Agents Unity Package Installer")
    print("=" * 50)
    print("This script will download and install ML-Agents for Unity 6")
    print("when the Package Manager has connectivity issues.")
    print()
    
    # Check if we're in a Unity project
    if not check_unity_project():
        print("Please run this script from your Unity project root directory.")
        return
    
    # Check if ML-Agents is already installed
    if os.path.exists("Packages/com.unity.ml-agents"):
        print("⚠️ ML-Agents package already exists in Packages/")
        choice = input("Do you want to reinstall? (y/n): ").lower().strip()
        if choice != 'y':
            print("Installation cancelled.")
            return
    
    try:
        # Download package
        zip_file = download_mlagents_package()
        if not zip_file:
            print("❌ Failed to download ML-Agents package")
            return
        
        # Extract package
        extract_dir = extract_package(zip_file)
        if not extract_dir:
            print("❌ Failed to extract package")
            return
        
        # Set up Unity package
        if not setup_unity_package():
            print("❌ Failed to set up Unity package")
            return
        
        # Update manifest
        if not update_manifest():
            print("❌ Failed to update package manifest")
            return
        
        # Clean up
        cleanup()
        
        # Remove stub file
        remove_stub_file()
        
        print("\n🎉 ML-Agents installation completed!")
        print("=" * 50)
        print("📋 Next steps:")
        print("1. Restart Unity Editor")
        print("2. Check Package Manager - ML-Agents should appear")
        print("3. Compilation errors should be resolved")
        print("4. Run 'Unity: Generate Project Files' task in VS Code")
        print("5. Start developing your SquadMate AI!")
        
    except Exception as e:
        print(f"❌ Installation failed: {e}")
        cleanup()

if __name__ == "__main__":
    main()
