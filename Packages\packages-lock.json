{"dependencies": {"com.unity.barracuda": {"version": "3.0.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.6.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.21", "depth": 2, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 3, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ml-agents": {"version": "2.0.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.barracuda": "2.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.multiplayer.center": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.uielements": "1.0.0"}}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}