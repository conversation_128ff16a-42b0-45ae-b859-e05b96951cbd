using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;

/// <summary>
/// Diagnostic tool to check and fix training scene issues
/// </summary>
public class TrainingSceneDiagnostic : EditorWindow
{
    [MenuItem("SquadMate AI/Diagnose Training Scene")]
    public static void ShowWindow()
    {
        GetWindow<TrainingSceneDiagnostic>("Scene Diagnostic");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Training Scene Diagnostic", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("This will check and fix common issues:");
        GUILayout.Label("• Missing components");
        GUILayout.Label("• Broken references");
        GUILayout.Label("• ML-Agents configuration");
        GUILayout.Label("• Script compilation errors");
        
        GUILayout.Space(20);
        
        if (GUILayout.Button("🔍 DIAGNOSE SCENE", GUILayout.Height(40)))
        {
            DiagnoseScene();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🔧 FIX ALL ISSUES", GUILayout.Height(40)))
        {
            FixAllIssues();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🎮 Test Scene Setup", GUILayout.Height(30)))
        {
            TestSceneSetup();
        }
    }
    
    public static void DiagnoseScene()
    {
        Debug.Log("🔍 DIAGNOSING TRAINING SCENE");
        Debug.Log("=" * 50);
        
        bool hasIssues = false;
        
        // Check basic GameObjects
        hasIssues |= CheckGameObjects();
        
        // Check components
        hasIssues |= CheckComponents();
        
        // Check ML-Agents setup
        hasIssues |= CheckMLAgentsSetup();
        
        // Check references
        hasIssues |= CheckReferences();
        
        // Check prefabs
        hasIssues |= CheckPrefabs();
        
        Debug.Log("=" * 50);
        
        if (hasIssues)
        {
            Debug.Log("❌ ISSUES FOUND - Use 'Fix All Issues' to resolve them");
        }
        else
        {
            Debug.Log("✅ NO ISSUES FOUND - Scene should work correctly");
        }
    }
    
    private static bool CheckGameObjects()
    {
        Debug.Log("🎮 Checking GameObjects...");
        bool hasIssues = false;
        
        GameObject player = GameObject.Find("Player");
        GameObject squadmate = GameObject.Find("SquadMate");
        GameObject environment = GameObject.Find("Environment");
        
        if (player == null)
        {
            Debug.LogError("❌ Player GameObject not found");
            hasIssues = true;
        }
        else
        {
            Debug.Log("✅ Player GameObject found");
        }
        
        if (squadmate == null)
        {
            Debug.LogError("❌ SquadMate GameObject not found");
            hasIssues = true;
        }
        else
        {
            Debug.Log("✅ SquadMate GameObject found");
        }
        
        if (environment == null)
        {
            Debug.LogError("❌ Environment GameObject not found");
            hasIssues = true;
        }
        else
        {
            Debug.Log("✅ Environment GameObject found");
        }
        
        return hasIssues;
    }
    
    private static bool CheckComponents()
    {
        Debug.Log("🔧 Checking Components...");
        bool hasIssues = false;
        
        GameObject player = GameObject.Find("Player");
        if (player != null)
        {
            if (player.GetComponent<PlayerController>() == null)
            {
                Debug.LogError("❌ PlayerController missing from Player");
                hasIssues = true;
            }
            else
            {
                Debug.Log("✅ PlayerController found on Player");
            }
        }
        
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            if (squadmate.GetComponent<SquadMateAgent>() == null)
            {
                Debug.LogError("❌ SquadMateAgent missing from SquadMate");
                hasIssues = true;
            }
            else
            {
                Debug.Log("✅ SquadMateAgent found on SquadMate");
            }
            
            if (squadmate.GetComponent<BehaviorParameters>() == null)
            {
                Debug.LogError("❌ BehaviorParameters missing from SquadMate");
                hasIssues = true;
            }
            else
            {
                Debug.Log("✅ BehaviorParameters found on SquadMate");
            }
        }
        
        GameObject environment = GameObject.Find("Environment");
        if (environment != null)
        {
            if (environment.GetComponent<GameEnvironment>() == null)
            {
                Debug.LogError("❌ GameEnvironment missing from Environment");
                hasIssues = true;
            }
            else
            {
                Debug.Log("✅ GameEnvironment found on Environment");
            }
        }
        
        return hasIssues;
    }
    
    private static bool CheckMLAgentsSetup()
    {
        Debug.Log("🧠 Checking ML-Agents Setup...");
        bool hasIssues = false;
        
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            BehaviorParameters behaviorParams = squadmate.GetComponent<BehaviorParameters>();
            if (behaviorParams != null)
            {
                if (string.IsNullOrEmpty(behaviorParams.BehaviorName))
                {
                    Debug.LogError("❌ BehaviorParameters has no Behavior Name");
                    hasIssues = true;
                }
                else
                {
                    Debug.Log($"✅ Behavior Name: {behaviorParams.BehaviorName}");
                }
                
                if (behaviorParams.BehaviorType != BehaviorType.Default)
                {
                    Debug.LogWarning("⚠️ Behavior Type should be 'Default' for training");
                }
                else
                {
                    Debug.Log("✅ Behavior Type is Default");
                }
            }
        }
        
        return hasIssues;
    }
    
    private static bool CheckReferences()
    {
        Debug.Log("🔗 Checking References...");
        bool hasIssues = false;
        
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                if (agent.player == null)
                {
                    Debug.LogError("❌ SquadMateAgent.player reference is null");
                    hasIssues = true;
                }
                else
                {
                    Debug.Log("✅ SquadMateAgent.player reference assigned");
                }
                
                if (agent.environment == null)
                {
                    Debug.LogError("❌ SquadMateAgent.environment reference is null");
                    hasIssues = true;
                }
                else
                {
                    Debug.Log("✅ SquadMateAgent.environment reference assigned");
                }
            }
        }
        
        return hasIssues;
    }
    
    private static bool CheckPrefabs()
    {
        Debug.Log("📦 Checking Prefabs...");
        bool hasIssues = false;
        
        GameObject enemyPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Enemy.prefab");
        GameObject medkitPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Medkit.prefab");
        GameObject weaponPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Weapon.prefab");
        
        if (enemyPrefab == null)
        {
            Debug.LogError("❌ Enemy prefab not found");
            hasIssues = true;
        }
        else
        {
            Debug.Log("✅ Enemy prefab found");
        }
        
        if (medkitPrefab == null)
        {
            Debug.LogError("❌ Medkit prefab not found");
            hasIssues = true;
        }
        else
        {
            Debug.Log("✅ Medkit prefab found");
        }
        
        if (weaponPrefab == null)
        {
            Debug.LogError("❌ Weapon prefab not found");
            hasIssues = true;
        }
        else
        {
            Debug.Log("✅ Weapon prefab found");
        }
        
        return hasIssues;
    }
    
    public static void FixAllIssues()
    {
        Debug.Log("🔧 FIXING ALL ISSUES");
        Debug.Log("=" * 50);
        
        // Fix missing GameObjects
        FixMissingGameObjects();
        
        // Fix missing components
        FixMissingComponents();
        
        // Fix ML-Agents setup
        FixMLAgentsSetup();
        
        // Fix references
        FixReferences();
        
        // Fix prefabs
        FixPrefabs();
        
        Debug.Log("=" * 50);
        Debug.Log("✅ ALL FIXES APPLIED");
        Debug.Log("🎮 Try pressing Play again!");
    }
    
    private static void FixMissingGameObjects()
    {
        if (GameObject.Find("Player") == null)
        {
            GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.name = "Player";
            player.transform.position = new Vector3(0, 1, 0);
            Debug.Log("🔧 Created missing Player GameObject");
        }
        
        if (GameObject.Find("SquadMate") == null)
        {
            GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            squadmate.name = "SquadMate";
            squadmate.transform.position = new Vector3(3, 1, 0);
            Debug.Log("🔧 Created missing SquadMate GameObject");
        }
        
        if (GameObject.Find("Environment") == null)
        {
            GameObject environment = new GameObject("Environment");
            Debug.Log("🔧 Created missing Environment GameObject");
        }
    }
    
    private static void FixMissingComponents()
    {
        GameObject player = GameObject.Find("Player");
        if (player != null && player.GetComponent<PlayerController>() == null)
        {
            player.AddComponent<PlayerController>();
            Debug.Log("🔧 Added PlayerController to Player");
        }
        
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            if (squadmate.GetComponent<SquadMateAgent>() == null)
            {
                squadmate.AddComponent<SquadMateAgent>();
                Debug.Log("🔧 Added SquadMateAgent to SquadMate");
            }
            
            if (squadmate.GetComponent<BehaviorParameters>() == null)
            {
                squadmate.AddComponent<BehaviorParameters>();
                Debug.Log("🔧 Added BehaviorParameters to SquadMate");
            }
            
            if (squadmate.GetComponent<RewardCalculator>() == null)
            {
                squadmate.AddComponent<RewardCalculator>();
                Debug.Log("🔧 Added RewardCalculator to SquadMate");
            }
        }
        
        GameObject environment = GameObject.Find("Environment");
        if (environment != null && environment.GetComponent<GameEnvironment>() == null)
        {
            environment.AddComponent<GameEnvironment>();
            Debug.Log("🔧 Added GameEnvironment to Environment");
        }
    }
    
    private static void FixMLAgentsSetup()
    {
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            BehaviorParameters behaviorParams = squadmate.GetComponent<BehaviorParameters>();
            if (behaviorParams != null)
            {
                behaviorParams.BehaviorName = "SquadMate";
                behaviorParams.BehaviorType = BehaviorType.Default;
                behaviorParams.TeamId = 0;
                behaviorParams.UseChildSensors = true;
                Debug.Log("🔧 Fixed BehaviorParameters configuration");
            }
        }
    }
    
    private static void FixReferences()
    {
        GameObject player = GameObject.Find("Player");
        GameObject squadmate = GameObject.Find("SquadMate");
        GameObject environment = GameObject.Find("Environment");
        
        if (squadmate != null && player != null)
        {
            SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                agent.player = player.transform;
                if (environment != null)
                {
                    agent.environment = environment.GetComponent<GameEnvironment>();
                }
                Debug.Log("🔧 Fixed SquadMateAgent references");
            }
        }
        
        if (environment != null)
        {
            GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
            if (gameEnv != null && player != null && squadmate != null)
            {
                gameEnv.player = player.transform;
                gameEnv.squadMate = squadmate.GetComponent<SquadMateAgent>();
                Debug.Log("🔧 Fixed GameEnvironment references");
            }
        }
    }
    
    private static void FixPrefabs()
    {
        // This would recreate missing prefabs if needed
        // For now, just log that prefabs should be created
        if (AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Enemy.prefab") == null)
        {
            Debug.Log("🔧 Enemy prefab missing - use scene generator to create");
        }
    }
    
    public static void TestSceneSetup()
    {
        Debug.Log("🧪 TESTING SCENE SETUP");
        Debug.Log("=" * 50);
        
        // Test basic functionality
        GameObject squadmate = GameObject.Find("SquadMate");
        if (squadmate != null)
        {
            SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                Debug.Log("🧪 Testing SquadMateAgent...");
                agent.AddReward(0.1f);
                Debug.Log("✅ SquadMateAgent.AddReward() works");
            }
        }
        
        GameObject environment = GameObject.Find("Environment");
        if (environment != null)
        {
            GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
            if (gameEnv != null)
            {
                Debug.Log("🧪 Testing GameEnvironment...");
                Vector3 randomPos = gameEnv.GetRandomPosition();
                Debug.Log($"✅ GameEnvironment.GetRandomPosition() = {randomPos}");
            }
        }
        
        Debug.Log("=" * 50);
        Debug.Log("🧪 Test completed - check console for any errors");
    }
}
