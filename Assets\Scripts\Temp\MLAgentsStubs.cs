// TEMPORARY STUBS FOR ML-AGENTS
// DELETE THIS FILE AFTER ML-AGENTS PACKAGE IS INSTALLED!

using UnityEngine;

namespace Unity.MLAgents
{
    /// <summary>
    /// Temporary Agent stub - DELETE after ML-Agents installation
    /// </summary>
    public class Agent : MonoBehaviour
    {
        public virtual void Initialize() { }
        public virtual void OnEpisodeBegin() { }
        public virtual void CollectObservations(Sensors.VectorSensor sensor) { }
        public virtual void OnActionReceived(Actuators.ActionBuffers actionBuffers) { }
        public virtual void Heuristic(in Actuators.ActionBuffers actionsOut) { }

        protected void AddReward(float reward)
        {
            Debug.Log($"[STUB] AddReward: {reward}");
        }

        protected void EndEpisode()
        {
            Debug.Log("[STUB] EndEpisode called");
        }
    }
}

namespace Unity.MLAgents.Sensors
{
    /// <summary>
    /// Temporary VectorSensor stub - DELETE after ML-Agents installation
    /// </summary>
    public class VectorSensor
    {
        public void AddObservation(Vector3 obs)
        {
            Debug.Log($"[STUB] AddObservation Vector3: {obs}");
        }

        public void AddObservation(float obs)
        {
            Debug.Log($"[STUB] AddObservation float: {obs}");
        }

        public void AddObservation(int obs)
        {
            Debug.Log($"[STUB] AddObservation int: {obs}");
        }

        public void AddObservation(bool obs)
        {
            Debug.Log($"[STUB] AddObservation bool: {obs}");
        }
    }
}

namespace Unity.MLAgents.Actuators
{
    /// <summary>
    /// Temporary ActionBuffers stub - DELETE after ML-Agents installation
    /// </summary>
    public struct ActionBuffers
    {
        public ActionSegment<float> ContinuousActions { get; set; }
        public ActionSegment<int> DiscreteActions { get; set; }

        public ActionBuffers(ActionSegment<float> continuousActions, ActionSegment<int> discreteActions)
        {
            ContinuousActions = continuousActions;
            DiscreteActions = discreteActions;
        }
    }

    /// <summary>
    /// Temporary ActionSegment stub - DELETE after ML-Agents installation
    /// </summary>
    public struct ActionSegment<T>
    {
        private T[] _array;

        public ActionSegment(T[] array)
        {
            _array = array ?? new T[0];
        }

        public T this[int index]
        {
            get
            {
                if (_array == null || index >= _array.Length) return default(T);
                return _array[index];
            }
            set
            {
                if (_array != null && index < _array.Length)
                    _array[index] = value;
            }
        }

        public int Length => _array?.Length ?? 0;
    }
}

namespace Unity.Collections
{
    /// <summary>
    /// Temporary Unity Collections stub - DELETE after ML-Agents installation
    /// </summary>
    public struct NativeArray<T> where T : struct
    {
        private T[] _array;

        public NativeArray(int length)
        {
            _array = new T[length];
        }

        public T this[int index]
        {
            get
            {
                if (_array == null || index >= _array.Length) return default(T);
                return _array[index];
            }
            set
            {
                if (_array != null && index < _array.Length)
                    _array[index] = value;
            }
        }

        public int Length
        {
            get { return _array?.Length ?? 0; }
        }

        public bool IsCreated => _array != null;

        public void Dispose()
        {
            _array = null;
        }
    }
}

namespace Unity.Jobs
{
    /// <summary>
    /// Temporary Unity Jobs stub - DELETE after ML-Agents installation
    /// </summary>
    public interface IJob
    {
        void Execute();
    }

    /// <summary>
    /// Temporary BurstCompile attribute stub
    /// </summary>
    public class BurstCompileAttribute : System.Attribute
    {
        public BurstCompileAttribute() { }
    }
}

// Additional stubs that might be needed
namespace Unity.MLAgents.Policies
{
    public enum BehaviorType
    {
        Default,
        HeuristicOnly,
        InferenceOnly
    }
}

namespace Unity.Barracuda
{
    public class Model
    {
        // Stub for Barracuda model
    }
}
