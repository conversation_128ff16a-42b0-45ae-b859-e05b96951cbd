// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: onnx.proto3
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Onnx {

  /// <summary>Holder for reflection information generated from onnx.proto3</summary>
  internal static partial class OnnxReflection {

    #region Descriptor
    /// <summary>File descriptor for onnx.proto3</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static OnnxReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cgtvbm54LnByb3RvMxIEb25ueCLoBAoOQXR0cmlidXRlUHJvdG8SDAoEbmFt",
            "ZRgBIAEoCRIVCg1yZWZfYXR0cl9uYW1lGBUgASgJEhIKCmRvY19zdHJpbmcY",
            "DSABKAkSMAoEdHlwZRgUIAEoDjIiLm9ubnguQXR0cmlidXRlUHJvdG8uQXR0",
            "cmlidXRlVHlwZRIJCgFmGAIgASgCEgkKAWkYAyABKAMSCQoBcxgEIAEoDBIc",
            "CgF0GAUgASgLMhEub25ueC5UZW5zb3JQcm90bxIbCgFnGAYgASgLMhAub25u",
            "eC5HcmFwaFByb3RvEi4KDXNwYXJzZV90ZW5zb3IYFiABKAsyFy5vbm54LlNw",
            "YXJzZVRlbnNvclByb3RvEg4KBmZsb2F0cxgHIAMoAhIMCgRpbnRzGAggAygD",
            "Eg8KB3N0cmluZ3MYCSADKAwSIgoHdGVuc29ycxgKIAMoCzIRLm9ubnguVGVu",
            "c29yUHJvdG8SIAoGZ3JhcGhzGAsgAygLMhAub25ueC5HcmFwaFByb3RvEi8K",
            "DnNwYXJzZV90ZW5zb3JzGBcgAygLMhcub25ueC5TcGFyc2VUZW5zb3JQcm90",
            "byK4AQoNQXR0cmlidXRlVHlwZRINCglVTkRFRklORUQQABIJCgVGTE9BVBAB",
            "EgcKA0lOVBACEgoKBlNUUklORxADEgoKBlRFTlNPUhAEEgkKBUdSQVBIEAUS",
            "EQoNU1BBUlNFX1RFTlNPUhALEgoKBkZMT0FUUxAGEggKBElOVFMQBxILCgdT",
            "VFJJTkdTEAgSCwoHVEVOU09SUxAJEgoKBkdSQVBIUxAKEhIKDlNQQVJTRV9U",
            "RU5TT1JTEAwiUQoOVmFsdWVJbmZvUHJvdG8SDAoEbmFtZRgBIAEoCRIdCgR0",
            "eXBlGAIgASgLMg8ub25ueC5UeXBlUHJvdG8SEgoKZG9jX3N0cmluZxgDIAEo",
            "CSKWAQoJTm9kZVByb3RvEg0KBWlucHV0GAEgAygJEg4KBm91dHB1dBgCIAMo",
            "CRIMCgRuYW1lGAMgASgJEg8KB29wX3R5cGUYBCABKAkSDgoGZG9tYWluGAcg",
            "ASgJEicKCWF0dHJpYnV0ZRgFIAMoCzIULm9ubnguQXR0cmlidXRlUHJvdG8S",
            "EgoKZG9jX3N0cmluZxgGIAEoCSKTAgoKTW9kZWxQcm90bxISCgppcl92ZXJz",
            "aW9uGAEgASgDEi4KDG9wc2V0X2ltcG9ydBgIIAMoCzIYLm9ubnguT3BlcmF0",
            "b3JTZXRJZFByb3RvEhUKDXByb2R1Y2VyX25hbWUYAiABKAkSGAoQcHJvZHVj",
            "ZXJfdmVyc2lvbhgDIAEoCRIOCgZkb21haW4YBCABKAkSFQoNbW9kZWxfdmVy",
            "c2lvbhgFIAEoAxISCgpkb2Nfc3RyaW5nGAYgASgJEh8KBWdyYXBoGAcgASgL",
            "MhAub25ueC5HcmFwaFByb3RvEjQKDm1ldGFkYXRhX3Byb3BzGA4gAygLMhwu",
            "b25ueC5TdHJpbmdTdHJpbmdFbnRyeVByb3RvIjQKFlN0cmluZ1N0cmluZ0Vu",
            "dHJ5UHJvdG8SCwoDa2V5GAEgASgJEg0KBXZhbHVlGAIgASgJImsKEFRlbnNv",
            "ckFubm90YXRpb24SEwoLdGVuc29yX25hbWUYASABKAkSQgoccXVhbnRfcGFy",
            "YW1ldGVyX3RlbnNvcl9uYW1lcxgCIAMoCzIcLm9ubnguU3RyaW5nU3RyaW5n",
            "RW50cnlQcm90byLYAgoKR3JhcGhQcm90bxIdCgRub2RlGAEgAygLMg8ub25u",
            "eC5Ob2RlUHJvdG8SDAoEbmFtZRgCIAEoCRImCgtpbml0aWFsaXplchgFIAMo",
            "CzIRLm9ubnguVGVuc29yUHJvdG8SMwoSc3BhcnNlX2luaXRpYWxpemVyGA8g",
            "AygLMhcub25ueC5TcGFyc2VUZW5zb3JQcm90bxISCgpkb2Nfc3RyaW5nGAog",
            "ASgJEiMKBWlucHV0GAsgAygLMhQub25ueC5WYWx1ZUluZm9Qcm90bxIkCgZv",
            "dXRwdXQYDCADKAsyFC5vbm54LlZhbHVlSW5mb1Byb3RvEigKCnZhbHVlX2lu",
            "Zm8YDSADKAsyFC5vbm54LlZhbHVlSW5mb1Byb3RvEjcKF3F1YW50aXphdGlv",
            "bl9hbm5vdGF0aW9uGA4gAygLMhYub25ueC5UZW5zb3JBbm5vdGF0aW9uIrgF",
            "CgtUZW5zb3JQcm90bxIMCgRkaW1zGAEgAygDEhEKCWRhdGFfdHlwZRgCIAEo",
            "BRIqCgdzZWdtZW50GAMgASgLMhkub25ueC5UZW5zb3JQcm90by5TZWdtZW50",
            "EhYKCmZsb2F0X2RhdGEYBCADKAJCAhABEhYKCmludDMyX2RhdGEYBSADKAVC",
            "AhABEhMKC3N0cmluZ19kYXRhGAYgAygMEhYKCmludDY0X2RhdGEYByADKANC",
            "AhABEgwKBG5hbWUYCCABKAkSEgoKZG9jX3N0cmluZxgMIAEoCRIQCghyYXdf",
            "ZGF0YRgJIAEoDBIzCg1leHRlcm5hbF9kYXRhGA0gAygLMhwub25ueC5TdHJp",
            "bmdTdHJpbmdFbnRyeVByb3RvEjUKDWRhdGFfbG9jYXRpb24YDiABKA4yHi5v",
            "bm54LlRlbnNvclByb3RvLkRhdGFMb2NhdGlvbhIXCgtkb3VibGVfZGF0YRgK",
            "IAMoAUICEAESFwoLdWludDY0X2RhdGEYCyADKARCAhABGiUKB1NlZ21lbnQS",
            "DQoFYmVnaW4YASABKAMSCwoDZW5kGAIgASgDItoBCghEYXRhVHlwZRINCglV",
            "TkRFRklORUQQABIJCgVGTE9BVBABEgkKBVVJTlQ4EAISCAoESU5UOBADEgoK",
            "BlVJTlQxNhAEEgkKBUlOVDE2EAUSCQoFSU5UMzIQBhIJCgVJTlQ2NBAHEgoK",
            "BlNUUklORxAIEggKBEJPT0wQCRILCgdGTE9BVDE2EAoSCgoGRE9VQkxFEAsS",
            "CgoGVUlOVDMyEAwSCgoGVUlOVDY0EA0SDQoJQ09NUExFWDY0EA4SDgoKQ09N",
            "UExFWDEyOBAPEgwKCEJGTE9BVDE2EBAiKQoMRGF0YUxvY2F0aW9uEgsKB0RF",
            "RkFVTFQQABIMCghFWFRFUk5BTBABImgKEVNwYXJzZVRlbnNvclByb3RvEiEK",
            "BnZhbHVlcxgBIAEoCzIRLm9ubnguVGVuc29yUHJvdG8SIgoHaW5kaWNlcxgC",
            "IAEoCzIRLm9ubnguVGVuc29yUHJvdG8SDAoEZGltcxgDIAMoAyKVAQoQVGVu",
            "c29yU2hhcGVQcm90bxItCgNkaW0YASADKAsyIC5vbm54LlRlbnNvclNoYXBl",
            "UHJvdG8uRGltZW5zaW9uGlIKCURpbWVuc2lvbhITCglkaW1fdmFsdWUYASAB",
            "KANIABITCglkaW1fcGFyYW0YAiABKAlIABISCgpkZW5vdGF0aW9uGAMgASgJ",
            "QgcKBXZhbHVlIuUBCglUeXBlUHJvdG8SLQoLdGVuc29yX3R5cGUYASABKAsy",
            "Fi5vbm54LlR5cGVQcm90by5UZW5zb3JIABISCgpkZW5vdGF0aW9uGAYgASgJ",
            "GkIKBlRlbnNvchIRCgllbGVtX3R5cGUYASABKAUSJQoFc2hhcGUYAiABKAsy",
            "Fi5vbm54LlRlbnNvclNoYXBlUHJvdG8aSAoMU3BhcnNlVGVuc29yEhEKCWVs",
            "ZW1fdHlwZRgBIAEoBRIlCgVzaGFwZRgCIAEoCzIWLm9ubnguVGVuc29yU2hh",
            "cGVQcm90b0IHCgV2YWx1ZSI1ChJPcGVyYXRvclNldElkUHJvdG8SDgoGZG9t",
            "YWluGAEgASgJEg8KB3ZlcnNpb24YAiABKAMqsQEKB1ZlcnNpb24SEgoOX1NU",
            "QVJUX1ZFUlNJT04QABIZChVJUl9WRVJTSU9OXzIwMTdfMTBfMTAQARIZChVJ",
            "Ul9WRVJTSU9OXzIwMTdfMTBfMzAQAhIYChRJUl9WRVJTSU9OXzIwMTdfMTFf",
            "MxADEhgKFElSX1ZFUlNJT05fMjAxOV8xXzIyEAQSGAoUSVJfVkVSU0lPTl8y",
            "MDE5XzNfMTgQBRIOCgpJUl9WRVJTSU9OEAZiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Onnx.Version), }, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.AttributeProto), global::Onnx.AttributeProto.Parser, new[]{ "Name", "RefAttrName", "DocString", "Type", "F", "I", "S", "T", "G", "SparseTensor", "Floats", "Ints", "Strings", "Tensors", "Graphs", "SparseTensors" }, null, new[]{ typeof(global::Onnx.AttributeProto.Types.AttributeType) }, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.ValueInfoProto), global::Onnx.ValueInfoProto.Parser, new[]{ "Name", "Type", "DocString" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.NodeProto), global::Onnx.NodeProto.Parser, new[]{ "Input", "Output", "Name", "OpType", "Domain", "Attribute", "DocString" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.ModelProto), global::Onnx.ModelProto.Parser, new[]{ "IrVersion", "OpsetImport", "ProducerName", "ProducerVersion", "Domain", "ModelVersion", "DocString", "Graph", "MetadataProps" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.StringStringEntryProto), global::Onnx.StringStringEntryProto.Parser, new[]{ "Key", "Value" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TensorAnnotation), global::Onnx.TensorAnnotation.Parser, new[]{ "TensorName", "QuantParameterTensorNames" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.GraphProto), global::Onnx.GraphProto.Parser, new[]{ "Node", "Name", "Initializer", "SparseInitializer", "DocString", "Input", "Output", "ValueInfo", "QuantizationAnnotation" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TensorProto), global::Onnx.TensorProto.Parser, new[]{ "Dims", "DataType", "Segment", "FloatData", "Int32Data", "StringData", "Int64Data", "Name", "DocString", "RawData", "ExternalData", "DataLocation", "DoubleData", "Uint64Data" }, null, new[]{ typeof(global::Onnx.TensorProto.Types.DataType), typeof(global::Onnx.TensorProto.Types.DataLocation) }, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TensorProto.Types.Segment), global::Onnx.TensorProto.Types.Segment.Parser, new[]{ "Begin", "End" }, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.SparseTensorProto), global::Onnx.SparseTensorProto.Parser, new[]{ "Values", "Indices", "Dims" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TensorShapeProto), global::Onnx.TensorShapeProto.Parser, new[]{ "Dim" }, null, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TensorShapeProto.Types.Dimension), global::Onnx.TensorShapeProto.Types.Dimension.Parser, new[]{ "DimValue", "DimParam", "Denotation" }, new[]{ "Value" }, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TypeProto), global::Onnx.TypeProto.Parser, new[]{ "TensorType", "Denotation" }, new[]{ "Value" }, null, new pbr::GeneratedClrTypeInfo[] { new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TypeProto.Types.Tensor), global::Onnx.TypeProto.Types.Tensor.Parser, new[]{ "ElemType", "Shape" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.TypeProto.Types.SparseTensor), global::Onnx.TypeProto.Types.SparseTensor.Parser, new[]{ "ElemType", "Shape" }, null, null, null)}),
            new pbr::GeneratedClrTypeInfo(typeof(global::Onnx.OperatorSetIdProto), global::Onnx.OperatorSetIdProto.Parser, new[]{ "Domain", "Version" }, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// Versioning
  ///
  /// ONNX versioning is specified in docs/IR.md and elaborated on in docs/Versioning.md
  ///
  /// To be compatible with both proto2 and proto3, we will use a version number
  /// that is not defined by the default value but an explicit enum number.
  /// </summary>
  internal enum Version {
    /// <summary>
    /// proto3 requires the first enum value to be zero.
    /// We add this just to appease the compiler.
    /// </summary>
    [pbr::OriginalName("_START_VERSION")] StartVersion = 0,
    /// <summary>
    /// The version field is always serialized and we will use it to store the
    /// version that the  graph is generated from. This helps us set up version
    /// control. 
    /// For the IR, we are using simple numbers starting with with 0x00000001, 
    /// which was the version we published on Oct 10, 2017.
    /// </summary>
    [pbr::OriginalName("IR_VERSION_2017_10_10")] IrVersion20171010 = 1,
    /// <summary>
    /// IR_VERSION 2 published on Oct 30, 2017
    /// - Added type discriminator to AttributeProto to support proto3 users
    /// </summary>
    [pbr::OriginalName("IR_VERSION_2017_10_30")] IrVersion20171030 = 2,
    /// <summary>
    /// IR VERSION 3 published on Nov 3, 2017
    /// - For operator versioning:
    ///    - Added new message OperatorSetIdProto
    ///    - Added opset_import in ModelProto
    /// - For vendor extensions, added domain in NodeProto
    /// </summary>
    [pbr::OriginalName("IR_VERSION_2017_11_3")] IrVersion2017113 = 3,
    /// <summary>
    /// IR VERSION 4 published on Jan 22, 2019
    /// - Relax constraint that initializers should be a subset of graph inputs
    /// - Add type BFLOAT16
    /// </summary>
    [pbr::OriginalName("IR_VERSION_2019_1_22")] IrVersion2019122 = 4,
    /// <summary>
    /// IR VERSION 5 published on March 18, 2019
    /// - Add message TensorAnnotation.
    /// - Add quantization annotation in GraphProto to map tensor with its scale and zero point quantization parameters.
    /// </summary>
    [pbr::OriginalName("IR_VERSION_2019_3_18")] IrVersion2019318 = 5,
    /// <summary>
    /// IR VERSION 6 published on &lt;TBD>
    /// - Add support for sparse tensor constants stored in model.
    ///   - Add message SparseTensorProto
    ///   - Add sparse initializers
    /// </summary>
    [pbr::OriginalName("IR_VERSION")] IrVersion = 6,
  }

  #endregion

  #region Messages
  /// <summary>
  /// Attributes
  ///
  /// A named attribute containing either singular float, integer, string, graph,
  /// and tensor values, or repeated float, integer, string, graph, and tensor values.
  /// An AttributeProto MUST contain the name field, and *only one* of the
  /// following content fields, effectively enforcing a C/C++ union equivalent.
  /// </summary>
  internal sealed partial class AttributeProto : pb::IMessage<AttributeProto> {
    private static readonly pb::MessageParser<AttributeProto> _parser = new pb::MessageParser<AttributeProto>(() => new AttributeProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AttributeProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AttributeProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AttributeProto(AttributeProto other) : this() {
      name_ = other.name_;
      refAttrName_ = other.refAttrName_;
      docString_ = other.docString_;
      type_ = other.type_;
      f_ = other.f_;
      i_ = other.i_;
      s_ = other.s_;
      T = other.t_ != null ? other.T.Clone() : null;
      G = other.g_ != null ? other.G.Clone() : null;
      SparseTensor = other.sparseTensor_ != null ? other.SparseTensor.Clone() : null;
      floats_ = other.floats_.Clone();
      ints_ = other.ints_.Clone();
      strings_ = other.strings_.Clone();
      tensors_ = other.tensors_.Clone();
      graphs_ = other.graphs_.Clone();
      sparseTensors_ = other.sparseTensors_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AttributeProto Clone() {
      return new AttributeProto(this);
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 1;
    private string name_ = "";
    /// <summary>
    /// The name field MUST be present for this version of the IR.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "ref_attr_name" field.</summary>
    public const int RefAttrNameFieldNumber = 21;
    private string refAttrName_ = "";
    /// <summary>
    /// if ref_attr_name is not empty, ref_attr_name is the attribute name in parent function.
    /// In this case, this AttributeProto does not contain data, and it's a reference of attribute
    /// in parent scope.
    /// NOTE: This should ONLY be used in function (sub-graph). It's invalid to be used in main graph.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string RefAttrName {
      get { return refAttrName_; }
      set {
        refAttrName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "doc_string" field.</summary>
    public const int DocStringFieldNumber = 13;
    private string docString_ = "";
    /// <summary>
    /// A human-readable documentation for this attribute. Markdown is allowed.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DocString {
      get { return docString_; }
      set {
        docString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 20;
    private global::Onnx.AttributeProto.Types.AttributeType type_ = 0;
    /// <summary>
    /// The type field MUST be present for this version of the IR.
    /// For 0.0.1 versions of the IR, this field was not defined, and
    /// implementations needed to use has_field hueristics to determine
    /// which value field was in use.  For IR_VERSION 0.0.2 or later, this
    /// field MUST be set and match the f|i|s|t|... field in use.  This
    /// change was made to accomodate proto3 implementations.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.AttributeProto.Types.AttributeType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "f" field.</summary>
    public const int FFieldNumber = 2;
    private float f_;
    /// <summary>
    /// Exactly ONE of the following fields must be present for this version of the IR
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public float F {
      get { return f_; }
      set {
        f_ = value;
      }
    }

    /// <summary>Field number for the "i" field.</summary>
    public const int IFieldNumber = 3;
    private long i_;
    /// <summary>
    /// int
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long I {
      get { return i_; }
      set {
        i_ = value;
      }
    }

    /// <summary>Field number for the "s" field.</summary>
    public const int SFieldNumber = 4;
    private pb::ByteString s_ = pb::ByteString.Empty;
    /// <summary>
    /// UTF-8 string
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pb::ByteString S {
      get { return s_; }
      set {
        s_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "t" field.</summary>
    public const int TFieldNumber = 5;
    private global::Onnx.TensorProto t_;
    /// <summary>
    /// tensor value
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TensorProto T {
      get { return t_; }
      set {
        t_ = value;
      }
    }

    /// <summary>Field number for the "g" field.</summary>
    public const int GFieldNumber = 6;
    private global::Onnx.GraphProto g_;
    /// <summary>
    /// graph
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.GraphProto G {
      get { return g_; }
      set {
        g_ = value;
      }
    }

    /// <summary>Field number for the "sparse_tensor" field.</summary>
    public const int SparseTensorFieldNumber = 22;
    private global::Onnx.SparseTensorProto sparseTensor_;
    /// <summary>
    /// sparse tensor value
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.SparseTensorProto SparseTensor {
      get { return sparseTensor_; }
      set {
        sparseTensor_ = value;
      }
    }

    /// <summary>Field number for the "floats" field.</summary>
    public const int FloatsFieldNumber = 7;
    private static readonly pb::FieldCodec<float> _repeated_floats_codec
        = pb::FieldCodec.ForFloat(58);
    private readonly pbc::RepeatedField<float> floats_ = new pbc::RepeatedField<float>();
    /// <summary>
    /// list of floats
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<float> Floats {
      get { return floats_; }
    }

    /// <summary>Field number for the "ints" field.</summary>
    public const int IntsFieldNumber = 8;
    private static readonly pb::FieldCodec<long> _repeated_ints_codec
        = pb::FieldCodec.ForInt64(66);
    private readonly pbc::RepeatedField<long> ints_ = new pbc::RepeatedField<long>();
    /// <summary>
    /// list of ints
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<long> Ints {
      get { return ints_; }
    }

    /// <summary>Field number for the "strings" field.</summary>
    public const int StringsFieldNumber = 9;
    private static readonly pb::FieldCodec<pb::ByteString> _repeated_strings_codec
        = pb::FieldCodec.ForBytes(74);
    private readonly pbc::RepeatedField<pb::ByteString> strings_ = new pbc::RepeatedField<pb::ByteString>();
    /// <summary>
    /// list of UTF-8 strings
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<pb::ByteString> Strings {
      get { return strings_; }
    }

    /// <summary>Field number for the "tensors" field.</summary>
    public const int TensorsFieldNumber = 10;
    private static readonly pb::FieldCodec<global::Onnx.TensorProto> _repeated_tensors_codec
        = pb::FieldCodec.ForMessage(82, global::Onnx.TensorProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.TensorProto> tensors_ = new pbc::RepeatedField<global::Onnx.TensorProto>();
    /// <summary>
    /// list of tensors
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.TensorProto> Tensors {
      get { return tensors_; }
    }

    /// <summary>Field number for the "graphs" field.</summary>
    public const int GraphsFieldNumber = 11;
    private static readonly pb::FieldCodec<global::Onnx.GraphProto> _repeated_graphs_codec
        = pb::FieldCodec.ForMessage(90, global::Onnx.GraphProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.GraphProto> graphs_ = new pbc::RepeatedField<global::Onnx.GraphProto>();
    /// <summary>
    /// list of graph
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.GraphProto> Graphs {
      get { return graphs_; }
    }

    /// <summary>Field number for the "sparse_tensors" field.</summary>
    public const int SparseTensorsFieldNumber = 23;
    private static readonly pb::FieldCodec<global::Onnx.SparseTensorProto> _repeated_sparseTensors_codec
        = pb::FieldCodec.ForMessage(186, global::Onnx.SparseTensorProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.SparseTensorProto> sparseTensors_ = new pbc::RepeatedField<global::Onnx.SparseTensorProto>();
    /// <summary>
    /// list of sparse tensors
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.SparseTensorProto> SparseTensors {
      get { return sparseTensors_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AttributeProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AttributeProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Name != other.Name) return false;
      if (RefAttrName != other.RefAttrName) return false;
      if (DocString != other.DocString) return false;
      if (Type != other.Type) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(F, other.F)) return false;
      if (I != other.I) return false;
      if (S != other.S) return false;
      if (!object.Equals(T, other.T)) return false;
      if (!object.Equals(G, other.G)) return false;
      if (!object.Equals(SparseTensor, other.SparseTensor)) return false;
      if(!floats_.Equals(other.floats_)) return false;
      if(!ints_.Equals(other.ints_)) return false;
      if(!strings_.Equals(other.strings_)) return false;
      if(!tensors_.Equals(other.tensors_)) return false;
      if(!graphs_.Equals(other.graphs_)) return false;
      if(!sparseTensors_.Equals(other.sparseTensors_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (RefAttrName.Length != 0) hash ^= RefAttrName.GetHashCode();
      if (DocString.Length != 0) hash ^= DocString.GetHashCode();
      if (Type != 0) hash ^= Type.GetHashCode();
      if (F != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(F);
      if (I != 0L) hash ^= I.GetHashCode();
      if (S.Length != 0) hash ^= S.GetHashCode();
      if (t_ != null) hash ^= T.GetHashCode();
      if (g_ != null) hash ^= G.GetHashCode();
      if (sparseTensor_ != null) hash ^= SparseTensor.GetHashCode();
      hash ^= floats_.GetHashCode();
      hash ^= ints_.GetHashCode();
      hash ^= strings_.GetHashCode();
      hash ^= tensors_.GetHashCode();
      hash ^= graphs_.GetHashCode();
      hash ^= sparseTensors_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (F != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(F);
      }
      if (I != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(I);
      }
      if (S.Length != 0) {
        output.WriteRawTag(34);
        output.WriteBytes(S);
      }
      if (t_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(T);
      }
      if (g_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(G);
      }
      floats_.WriteTo(output, _repeated_floats_codec);
      ints_.WriteTo(output, _repeated_ints_codec);
      strings_.WriteTo(output, _repeated_strings_codec);
      tensors_.WriteTo(output, _repeated_tensors_codec);
      graphs_.WriteTo(output, _repeated_graphs_codec);
      if (DocString.Length != 0) {
        output.WriteRawTag(106);
        output.WriteString(DocString);
      }
      if (Type != 0) {
        output.WriteRawTag(160, 1);
        output.WriteEnum((int) Type);
      }
      if (RefAttrName.Length != 0) {
        output.WriteRawTag(170, 1);
        output.WriteString(RefAttrName);
      }
      if (sparseTensor_ != null) {
        output.WriteRawTag(178, 1);
        output.WriteMessage(SparseTensor);
      }
      sparseTensors_.WriteTo(output, _repeated_sparseTensors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (RefAttrName.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(RefAttrName);
      }
      if (DocString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DocString);
      }
      if (Type != 0) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (F != 0F) {
        size += 1 + 4;
      }
      if (I != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(I);
      }
      if (S.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(S);
      }
      if (t_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(T);
      }
      if (g_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(G);
      }
      if (sparseTensor_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(SparseTensor);
      }
      size += floats_.CalculateSize(_repeated_floats_codec);
      size += ints_.CalculateSize(_repeated_ints_codec);
      size += strings_.CalculateSize(_repeated_strings_codec);
      size += tensors_.CalculateSize(_repeated_tensors_codec);
      size += graphs_.CalculateSize(_repeated_graphs_codec);
      size += sparseTensors_.CalculateSize(_repeated_sparseTensors_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AttributeProto other) {
      if (other == null) {
        return;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.RefAttrName.Length != 0) {
        RefAttrName = other.RefAttrName;
      }
      if (other.DocString.Length != 0) {
        DocString = other.DocString;
      }
      if (other.Type != 0) {
        Type = other.Type;
      }
      if (other.F != 0F) {
        F = other.F;
      }
      if (other.I != 0L) {
        I = other.I;
      }
      if (other.S.Length != 0) {
        S = other.S;
      }
      if (other.t_ != null) {
        if (t_ == null) {
          t_ = new global::Onnx.TensorProto();
        }
        T.MergeFrom(other.T);
      }
      if (other.g_ != null) {
        if (g_ == null) {
          g_ = new global::Onnx.GraphProto();
        }
        G.MergeFrom(other.G);
      }
      if (other.sparseTensor_ != null) {
        if (sparseTensor_ == null) {
          sparseTensor_ = new global::Onnx.SparseTensorProto();
        }
        SparseTensor.MergeFrom(other.SparseTensor);
      }
      floats_.Add(other.floats_);
      ints_.Add(other.ints_);
      strings_.Add(other.strings_);
      tensors_.Add(other.tensors_);
      graphs_.Add(other.graphs_);
      sparseTensors_.Add(other.sparseTensors_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 21: {
            F = input.ReadFloat();
            break;
          }
          case 24: {
            I = input.ReadInt64();
            break;
          }
          case 34: {
            S = input.ReadBytes();
            break;
          }
          case 42: {
            if (t_ == null) {
              t_ = new global::Onnx.TensorProto();
            }
            input.ReadMessage(t_);
            break;
          }
          case 50: {
            if (g_ == null) {
              g_ = new global::Onnx.GraphProto();
            }
            input.ReadMessage(g_);
            break;
          }
          case 58:
          case 61: {
            floats_.AddEntriesFrom(input, _repeated_floats_codec);
            break;
          }
          case 66:
          case 64: {
            ints_.AddEntriesFrom(input, _repeated_ints_codec);
            break;
          }
          case 74: {
            strings_.AddEntriesFrom(input, _repeated_strings_codec);
            break;
          }
          case 82: {
            tensors_.AddEntriesFrom(input, _repeated_tensors_codec);
            break;
          }
          case 90: {
            graphs_.AddEntriesFrom(input, _repeated_graphs_codec);
            break;
          }
          case 106: {
            DocString = input.ReadString();
            break;
          }
          case 160: {
            type_ = (global::Onnx.AttributeProto.Types.AttributeType) input.ReadEnum();
            break;
          }
          case 170: {
            RefAttrName = input.ReadString();
            break;
          }
          case 178: {
            if (sparseTensor_ == null) {
              sparseTensor_ = new global::Onnx.SparseTensorProto();
            }
            input.ReadMessage(sparseTensor_);
            break;
          }
          case 186: {
            sparseTensors_.AddEntriesFrom(input, _repeated_sparseTensors_codec);
            break;
          }
        }
      }
    }

    #region Nested types
    /// <summary>Container for nested types declared in the AttributeProto message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static partial class Types {
      /// <summary>
      /// Note: this enum is structurally identical to the OpSchema::AttrType
      /// enum defined in schema.h.  If you rev one, you likely need to rev the other.
      /// </summary>
      internal enum AttributeType {
        [pbr::OriginalName("UNDEFINED")] Undefined = 0,
        [pbr::OriginalName("FLOAT")] Float = 1,
        [pbr::OriginalName("INT")] Int = 2,
        [pbr::OriginalName("STRING")] String = 3,
        [pbr::OriginalName("TENSOR")] Tensor = 4,
        [pbr::OriginalName("GRAPH")] Graph = 5,
        [pbr::OriginalName("SPARSE_TENSOR")] SparseTensor = 11,
        [pbr::OriginalName("FLOATS")] Floats = 6,
        [pbr::OriginalName("INTS")] Ints = 7,
        [pbr::OriginalName("STRINGS")] Strings = 8,
        [pbr::OriginalName("TENSORS")] Tensors = 9,
        [pbr::OriginalName("GRAPHS")] Graphs = 10,
        [pbr::OriginalName("SPARSE_TENSORS")] SparseTensors = 12,
      }

    }
    #endregion

  }

  /// <summary>
  /// Defines information on value, including the name, the type, and
  /// the shape of the value.
  /// </summary>
  internal sealed partial class ValueInfoProto : pb::IMessage<ValueInfoProto> {
    private static readonly pb::MessageParser<ValueInfoProto> _parser = new pb::MessageParser<ValueInfoProto>(() => new ValueInfoProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ValueInfoProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ValueInfoProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ValueInfoProto(ValueInfoProto other) : this() {
      name_ = other.name_;
      Type = other.type_ != null ? other.Type.Clone() : null;
      docString_ = other.docString_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ValueInfoProto Clone() {
      return new ValueInfoProto(this);
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 1;
    private string name_ = "";
    /// <summary>
    /// This field MUST be present in this version of the IR.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 2;
    private global::Onnx.TypeProto type_;
    /// <summary>
    /// This field MUST be present in this version of the IR.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TypeProto Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "doc_string" field.</summary>
    public const int DocStringFieldNumber = 3;
    private string docString_ = "";
    /// <summary>
    /// A human-readable documentation for this value. Markdown is allowed.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DocString {
      get { return docString_; }
      set {
        docString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ValueInfoProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ValueInfoProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Name != other.Name) return false;
      if (!object.Equals(Type, other.Type)) return false;
      if (DocString != other.DocString) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (type_ != null) hash ^= Type.GetHashCode();
      if (DocString.Length != 0) hash ^= DocString.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (type_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Type);
      }
      if (DocString.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(DocString);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (type_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Type);
      }
      if (DocString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DocString);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ValueInfoProto other) {
      if (other == null) {
        return;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.type_ != null) {
        if (type_ == null) {
          type_ = new global::Onnx.TypeProto();
        }
        Type.MergeFrom(other.Type);
      }
      if (other.DocString.Length != 0) {
        DocString = other.DocString;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 18: {
            if (type_ == null) {
              type_ = new global::Onnx.TypeProto();
            }
            input.ReadMessage(type_);
            break;
          }
          case 26: {
            DocString = input.ReadString();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// Nodes
  ///
  /// Computation graphs are made up of a DAG of nodes, which represent what is
  /// commonly called a "layer" or "pipeline stage" in machine learning frameworks.
  ///
  /// For example, it can be a node of type "Conv" that takes in an image, a filter 
  /// tensor and a bias tensor, and produces the convolved output.
  /// </summary>
  internal sealed partial class NodeProto : pb::IMessage<NodeProto> {
    private static readonly pb::MessageParser<NodeProto> _parser = new pb::MessageParser<NodeProto>(() => new NodeProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<NodeProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public NodeProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public NodeProto(NodeProto other) : this() {
      input_ = other.input_.Clone();
      output_ = other.output_.Clone();
      name_ = other.name_;
      opType_ = other.opType_;
      domain_ = other.domain_;
      attribute_ = other.attribute_.Clone();
      docString_ = other.docString_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public NodeProto Clone() {
      return new NodeProto(this);
    }

    /// <summary>Field number for the "input" field.</summary>
    public const int InputFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_input_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> input_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// namespace Value
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<string> Input {
      get { return input_; }
    }

    /// <summary>Field number for the "output" field.</summary>
    public const int OutputFieldNumber = 2;
    private static readonly pb::FieldCodec<string> _repeated_output_codec
        = pb::FieldCodec.ForString(18);
    private readonly pbc::RepeatedField<string> output_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// namespace Value
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<string> Output {
      get { return output_; }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 3;
    private string name_ = "";
    /// <summary>
    /// An optional identifier for this node in a graph.
    /// This field MAY be absent in ths version of the IR.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "op_type" field.</summary>
    public const int OpTypeFieldNumber = 4;
    private string opType_ = "";
    /// <summary>
    /// The symbolic identifier of the Operator to execute.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string OpType {
      get { return opType_; }
      set {
        opType_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "domain" field.</summary>
    public const int DomainFieldNumber = 7;
    private string domain_ = "";
    /// <summary>
    /// The domain of the OperatorSet that specifies the operator named by op_type.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Domain {
      get { return domain_; }
      set {
        domain_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "attribute" field.</summary>
    public const int AttributeFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Onnx.AttributeProto> _repeated_attribute_codec
        = pb::FieldCodec.ForMessage(42, global::Onnx.AttributeProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.AttributeProto> attribute_ = new pbc::RepeatedField<global::Onnx.AttributeProto>();
    /// <summary>
    /// Additional named attributes.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.AttributeProto> Attribute {
      get { return attribute_; }
    }

    /// <summary>Field number for the "doc_string" field.</summary>
    public const int DocStringFieldNumber = 6;
    private string docString_ = "";
    /// <summary>
    /// A human-readable documentation for this node. Markdown is allowed.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DocString {
      get { return docString_; }
      set {
        docString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as NodeProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(NodeProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!input_.Equals(other.input_)) return false;
      if(!output_.Equals(other.output_)) return false;
      if (Name != other.Name) return false;
      if (OpType != other.OpType) return false;
      if (Domain != other.Domain) return false;
      if(!attribute_.Equals(other.attribute_)) return false;
      if (DocString != other.DocString) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= input_.GetHashCode();
      hash ^= output_.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (OpType.Length != 0) hash ^= OpType.GetHashCode();
      if (Domain.Length != 0) hash ^= Domain.GetHashCode();
      hash ^= attribute_.GetHashCode();
      if (DocString.Length != 0) hash ^= DocString.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      input_.WriteTo(output, _repeated_input_codec);
      output_.WriteTo(output, _repeated_output_codec);
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (OpType.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(OpType);
      }
      attribute_.WriteTo(output, _repeated_attribute_codec);
      if (DocString.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(DocString);
      }
      if (Domain.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(Domain);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += input_.CalculateSize(_repeated_input_codec);
      size += output_.CalculateSize(_repeated_output_codec);
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (OpType.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OpType);
      }
      if (Domain.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Domain);
      }
      size += attribute_.CalculateSize(_repeated_attribute_codec);
      if (DocString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DocString);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(NodeProto other) {
      if (other == null) {
        return;
      }
      input_.Add(other.input_);
      output_.Add(other.output_);
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.OpType.Length != 0) {
        OpType = other.OpType;
      }
      if (other.Domain.Length != 0) {
        Domain = other.Domain;
      }
      attribute_.Add(other.attribute_);
      if (other.DocString.Length != 0) {
        DocString = other.DocString;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            input_.AddEntriesFrom(input, _repeated_input_codec);
            break;
          }
          case 18: {
            output_.AddEntriesFrom(input, _repeated_output_codec);
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 34: {
            OpType = input.ReadString();
            break;
          }
          case 42: {
            attribute_.AddEntriesFrom(input, _repeated_attribute_codec);
            break;
          }
          case 50: {
            DocString = input.ReadString();
            break;
          }
          case 58: {
            Domain = input.ReadString();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// Models
  ///
  /// ModelProto is a top-level file/container format for bundling a ML model and
  /// associating its computation graph with metadata.
  ///
  /// The semantics of the model are described by the associated GraphProto.
  /// </summary>
  internal sealed partial class ModelProto : pb::IMessage<ModelProto> {
    private static readonly pb::MessageParser<ModelProto> _parser = new pb::MessageParser<ModelProto>(() => new ModelProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ModelProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ModelProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ModelProto(ModelProto other) : this() {
      irVersion_ = other.irVersion_;
      opsetImport_ = other.opsetImport_.Clone();
      producerName_ = other.producerName_;
      producerVersion_ = other.producerVersion_;
      domain_ = other.domain_;
      modelVersion_ = other.modelVersion_;
      docString_ = other.docString_;
      Graph = other.graph_ != null ? other.Graph.Clone() : null;
      metadataProps_ = other.metadataProps_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ModelProto Clone() {
      return new ModelProto(this);
    }

    /// <summary>Field number for the "ir_version" field.</summary>
    public const int IrVersionFieldNumber = 1;
    private long irVersion_;
    /// <summary>
    /// The version of the IR this model targets. See Version enum above.
    /// This field MUST be present.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long IrVersion {
      get { return irVersion_; }
      set {
        irVersion_ = value;
      }
    }

    /// <summary>Field number for the "opset_import" field.</summary>
    public const int OpsetImportFieldNumber = 8;
    private static readonly pb::FieldCodec<global::Onnx.OperatorSetIdProto> _repeated_opsetImport_codec
        = pb::FieldCodec.ForMessage(66, global::Onnx.OperatorSetIdProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.OperatorSetIdProto> opsetImport_ = new pbc::RepeatedField<global::Onnx.OperatorSetIdProto>();
    /// <summary>
    /// The OperatorSets this model relies on.
    /// All ModelProtos MUST have at least one entry that
    /// specifies which version of the ONNX OperatorSet is
    /// being imported.
    ///
    /// All nodes in the ModelProto's graph will bind against the operator
    /// with the same-domain/same-op_type operator with the HIGHEST version
    /// in the referenced operator sets.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.OperatorSetIdProto> OpsetImport {
      get { return opsetImport_; }
    }

    /// <summary>Field number for the "producer_name" field.</summary>
    public const int ProducerNameFieldNumber = 2;
    private string producerName_ = "";
    /// <summary>
    /// The name of the framework or tool used to generate this model.
    /// This field SHOULD be present to indicate which implementation/tool/framework
    /// emitted the model.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string ProducerName {
      get { return producerName_; }
      set {
        producerName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "producer_version" field.</summary>
    public const int ProducerVersionFieldNumber = 3;
    private string producerVersion_ = "";
    /// <summary>
    /// The version of the framework or tool used to generate this model.
    /// This field SHOULD be present to indicate which implementation/tool/framework
    /// emitted the model.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string ProducerVersion {
      get { return producerVersion_; }
      set {
        producerVersion_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "domain" field.</summary>
    public const int DomainFieldNumber = 4;
    private string domain_ = "";
    /// <summary>
    /// Domain name of the model.
    /// We use reverse domain names as name space indicators. For example:
    /// `com.facebook.fair` or `com.microsoft.cognitiveservices`
    ///
    /// Together with `model_version` and GraphProto.name, this forms the unique identity of
    /// the graph.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Domain {
      get { return domain_; }
      set {
        domain_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "model_version" field.</summary>
    public const int ModelVersionFieldNumber = 5;
    private long modelVersion_;
    /// <summary>
    /// The version of the graph encoded. See Version enum below.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long ModelVersion {
      get { return modelVersion_; }
      set {
        modelVersion_ = value;
      }
    }

    /// <summary>Field number for the "doc_string" field.</summary>
    public const int DocStringFieldNumber = 6;
    private string docString_ = "";
    /// <summary>
    /// A human-readable documentation for this model. Markdown is allowed.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DocString {
      get { return docString_; }
      set {
        docString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "graph" field.</summary>
    public const int GraphFieldNumber = 7;
    private global::Onnx.GraphProto graph_;
    /// <summary>
    /// The parameterized graph that is evaluated to execute the model.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.GraphProto Graph {
      get { return graph_; }
      set {
        graph_ = value;
      }
    }

    /// <summary>Field number for the "metadata_props" field.</summary>
    public const int MetadataPropsFieldNumber = 14;
    private static readonly pb::FieldCodec<global::Onnx.StringStringEntryProto> _repeated_metadataProps_codec
        = pb::FieldCodec.ForMessage(114, global::Onnx.StringStringEntryProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.StringStringEntryProto> metadataProps_ = new pbc::RepeatedField<global::Onnx.StringStringEntryProto>();
    /// <summary>
    /// Named metadata values; keys should be distinct.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.StringStringEntryProto> MetadataProps {
      get { return metadataProps_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ModelProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ModelProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (IrVersion != other.IrVersion) return false;
      if(!opsetImport_.Equals(other.opsetImport_)) return false;
      if (ProducerName != other.ProducerName) return false;
      if (ProducerVersion != other.ProducerVersion) return false;
      if (Domain != other.Domain) return false;
      if (ModelVersion != other.ModelVersion) return false;
      if (DocString != other.DocString) return false;
      if (!object.Equals(Graph, other.Graph)) return false;
      if(!metadataProps_.Equals(other.metadataProps_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (IrVersion != 0L) hash ^= IrVersion.GetHashCode();
      hash ^= opsetImport_.GetHashCode();
      if (ProducerName.Length != 0) hash ^= ProducerName.GetHashCode();
      if (ProducerVersion.Length != 0) hash ^= ProducerVersion.GetHashCode();
      if (Domain.Length != 0) hash ^= Domain.GetHashCode();
      if (ModelVersion != 0L) hash ^= ModelVersion.GetHashCode();
      if (DocString.Length != 0) hash ^= DocString.GetHashCode();
      if (graph_ != null) hash ^= Graph.GetHashCode();
      hash ^= metadataProps_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (IrVersion != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(IrVersion);
      }
      if (ProducerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ProducerName);
      }
      if (ProducerVersion.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ProducerVersion);
      }
      if (Domain.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Domain);
      }
      if (ModelVersion != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(ModelVersion);
      }
      if (DocString.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(DocString);
      }
      if (graph_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Graph);
      }
      opsetImport_.WriteTo(output, _repeated_opsetImport_codec);
      metadataProps_.WriteTo(output, _repeated_metadataProps_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (IrVersion != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(IrVersion);
      }
      size += opsetImport_.CalculateSize(_repeated_opsetImport_codec);
      if (ProducerName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ProducerName);
      }
      if (ProducerVersion.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ProducerVersion);
      }
      if (Domain.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Domain);
      }
      if (ModelVersion != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ModelVersion);
      }
      if (DocString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DocString);
      }
      if (graph_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Graph);
      }
      size += metadataProps_.CalculateSize(_repeated_metadataProps_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ModelProto other) {
      if (other == null) {
        return;
      }
      if (other.IrVersion != 0L) {
        IrVersion = other.IrVersion;
      }
      opsetImport_.Add(other.opsetImport_);
      if (other.ProducerName.Length != 0) {
        ProducerName = other.ProducerName;
      }
      if (other.ProducerVersion.Length != 0) {
        ProducerVersion = other.ProducerVersion;
      }
      if (other.Domain.Length != 0) {
        Domain = other.Domain;
      }
      if (other.ModelVersion != 0L) {
        ModelVersion = other.ModelVersion;
      }
      if (other.DocString.Length != 0) {
        DocString = other.DocString;
      }
      if (other.graph_ != null) {
        if (graph_ == null) {
          graph_ = new global::Onnx.GraphProto();
        }
        Graph.MergeFrom(other.Graph);
      }
      metadataProps_.Add(other.metadataProps_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            IrVersion = input.ReadInt64();
            break;
          }
          case 18: {
            ProducerName = input.ReadString();
            break;
          }
          case 26: {
            ProducerVersion = input.ReadString();
            break;
          }
          case 34: {
            Domain = input.ReadString();
            break;
          }
          case 40: {
            ModelVersion = input.ReadInt64();
            break;
          }
          case 50: {
            DocString = input.ReadString();
            break;
          }
          case 58: {
            if (graph_ == null) {
              graph_ = new global::Onnx.GraphProto();
            }
            input.ReadMessage(graph_);
            break;
          }
          case 66: {
            opsetImport_.AddEntriesFrom(input, _repeated_opsetImport_codec);
            break;
          }
          case 114: {
            metadataProps_.AddEntriesFrom(input, _repeated_metadataProps_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// StringStringEntryProto follows the pattern for cross-proto-version maps.
  /// See https://developers.google.com/protocol-buffers/docs/proto3#maps
  /// </summary>
  internal sealed partial class StringStringEntryProto : pb::IMessage<StringStringEntryProto> {
    private static readonly pb::MessageParser<StringStringEntryProto> _parser = new pb::MessageParser<StringStringEntryProto>(() => new StringStringEntryProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<StringStringEntryProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StringStringEntryProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StringStringEntryProto(StringStringEntryProto other) : this() {
      key_ = other.key_;
      value_ = other.value_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StringStringEntryProto Clone() {
      return new StringStringEntryProto(this);
    }

    /// <summary>Field number for the "key" field.</summary>
    public const int KeyFieldNumber = 1;
    private string key_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Key {
      get { return key_; }
      set {
        key_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    private string value_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Value {
      get { return value_; }
      set {
        value_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as StringStringEntryProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(StringStringEntryProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Key != other.Key) return false;
      if (Value != other.Value) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Key.Length != 0) hash ^= Key.GetHashCode();
      if (Value.Length != 0) hash ^= Value.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Key.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Key);
      }
      if (Value.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Key.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Key);
      }
      if (Value.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(StringStringEntryProto other) {
      if (other == null) {
        return;
      }
      if (other.Key.Length != 0) {
        Key = other.Key;
      }
      if (other.Value.Length != 0) {
        Value = other.Value;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Key = input.ReadString();
            break;
          }
          case 18: {
            Value = input.ReadString();
            break;
          }
        }
      }
    }

  }

  internal sealed partial class TensorAnnotation : pb::IMessage<TensorAnnotation> {
    private static readonly pb::MessageParser<TensorAnnotation> _parser = new pb::MessageParser<TensorAnnotation>(() => new TensorAnnotation());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<TensorAnnotation> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorAnnotation() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorAnnotation(TensorAnnotation other) : this() {
      tensorName_ = other.tensorName_;
      quantParameterTensorNames_ = other.quantParameterTensorNames_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorAnnotation Clone() {
      return new TensorAnnotation(this);
    }

    /// <summary>Field number for the "tensor_name" field.</summary>
    public const int TensorNameFieldNumber = 1;
    private string tensorName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string TensorName {
      get { return tensorName_; }
      set {
        tensorName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "quant_parameter_tensor_names" field.</summary>
    public const int QuantParameterTensorNamesFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Onnx.StringStringEntryProto> _repeated_quantParameterTensorNames_codec
        = pb::FieldCodec.ForMessage(18, global::Onnx.StringStringEntryProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.StringStringEntryProto> quantParameterTensorNames_ = new pbc::RepeatedField<global::Onnx.StringStringEntryProto>();
    /// <summary>
    /// &lt;key, value> pairs to annotate tensor specified by &lt;tensor_name> above.
    /// The keys used in the mapping below must be pre-defined in ONNX spec.
    /// For example, for 8-bit linear quantization case, 'SCALE_TENSOR', 'ZERO_POINT_TENSOR' will be pre-defined as
    /// quantization parameter keys.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.StringStringEntryProto> QuantParameterTensorNames {
      get { return quantParameterTensorNames_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as TensorAnnotation);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(TensorAnnotation other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TensorName != other.TensorName) return false;
      if(!quantParameterTensorNames_.Equals(other.quantParameterTensorNames_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (TensorName.Length != 0) hash ^= TensorName.GetHashCode();
      hash ^= quantParameterTensorNames_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (TensorName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(TensorName);
      }
      quantParameterTensorNames_.WriteTo(output, _repeated_quantParameterTensorNames_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (TensorName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TensorName);
      }
      size += quantParameterTensorNames_.CalculateSize(_repeated_quantParameterTensorNames_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(TensorAnnotation other) {
      if (other == null) {
        return;
      }
      if (other.TensorName.Length != 0) {
        TensorName = other.TensorName;
      }
      quantParameterTensorNames_.Add(other.quantParameterTensorNames_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            TensorName = input.ReadString();
            break;
          }
          case 18: {
            quantParameterTensorNames_.AddEntriesFrom(input, _repeated_quantParameterTensorNames_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// Graphs
  ///
  /// A graph defines the computational logic of a model and is comprised of a parameterized 
  /// list of nodes that form a directed acyclic graph based on their inputs and outputs.
  /// This is the equivalent of the "network" or "graph" in many deep learning
  /// frameworks.
  /// </summary>
  internal sealed partial class GraphProto : pb::IMessage<GraphProto> {
    private static readonly pb::MessageParser<GraphProto> _parser = new pb::MessageParser<GraphProto>(() => new GraphProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GraphProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GraphProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GraphProto(GraphProto other) : this() {
      node_ = other.node_.Clone();
      name_ = other.name_;
      initializer_ = other.initializer_.Clone();
      sparseInitializer_ = other.sparseInitializer_.Clone();
      docString_ = other.docString_;
      input_ = other.input_.Clone();
      output_ = other.output_.Clone();
      valueInfo_ = other.valueInfo_.Clone();
      quantizationAnnotation_ = other.quantizationAnnotation_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GraphProto Clone() {
      return new GraphProto(this);
    }

    /// <summary>Field number for the "node" field.</summary>
    public const int NodeFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Onnx.NodeProto> _repeated_node_codec
        = pb::FieldCodec.ForMessage(10, global::Onnx.NodeProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.NodeProto> node_ = new pbc::RepeatedField<global::Onnx.NodeProto>();
    /// <summary>
    /// The nodes in the graph, sorted topologically.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.NodeProto> Node {
      get { return node_; }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    /// <summary>
    /// The name of the graph.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "initializer" field.</summary>
    public const int InitializerFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Onnx.TensorProto> _repeated_initializer_codec
        = pb::FieldCodec.ForMessage(42, global::Onnx.TensorProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.TensorProto> initializer_ = new pbc::RepeatedField<global::Onnx.TensorProto>();
    /// <summary>
    /// A list of named tensor values, used to specify constant inputs of the graph.
    /// Each TensorProto entry must have a distinct name (within the list) that
    /// MAY also appear in the input list.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.TensorProto> Initializer {
      get { return initializer_; }
    }

    /// <summary>Field number for the "sparse_initializer" field.</summary>
    public const int SparseInitializerFieldNumber = 15;
    private static readonly pb::FieldCodec<global::Onnx.SparseTensorProto> _repeated_sparseInitializer_codec
        = pb::FieldCodec.ForMessage(122, global::Onnx.SparseTensorProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.SparseTensorProto> sparseInitializer_ = new pbc::RepeatedField<global::Onnx.SparseTensorProto>();
    /// <summary>
    /// Initializers (see above) stored in sparse format.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.SparseTensorProto> SparseInitializer {
      get { return sparseInitializer_; }
    }

    /// <summary>Field number for the "doc_string" field.</summary>
    public const int DocStringFieldNumber = 10;
    private string docString_ = "";
    /// <summary>
    /// A human-readable documentation for this graph. Markdown is allowed.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DocString {
      get { return docString_; }
      set {
        docString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "input" field.</summary>
    public const int InputFieldNumber = 11;
    private static readonly pb::FieldCodec<global::Onnx.ValueInfoProto> _repeated_input_codec
        = pb::FieldCodec.ForMessage(90, global::Onnx.ValueInfoProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.ValueInfoProto> input_ = new pbc::RepeatedField<global::Onnx.ValueInfoProto>();
    /// <summary>
    /// The inputs and outputs of the graph.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.ValueInfoProto> Input {
      get { return input_; }
    }

    /// <summary>Field number for the "output" field.</summary>
    public const int OutputFieldNumber = 12;
    private static readonly pb::FieldCodec<global::Onnx.ValueInfoProto> _repeated_output_codec
        = pb::FieldCodec.ForMessage(98, global::Onnx.ValueInfoProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.ValueInfoProto> output_ = new pbc::RepeatedField<global::Onnx.ValueInfoProto>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.ValueInfoProto> Output {
      get { return output_; }
    }

    /// <summary>Field number for the "value_info" field.</summary>
    public const int ValueInfoFieldNumber = 13;
    private static readonly pb::FieldCodec<global::Onnx.ValueInfoProto> _repeated_valueInfo_codec
        = pb::FieldCodec.ForMessage(106, global::Onnx.ValueInfoProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.ValueInfoProto> valueInfo_ = new pbc::RepeatedField<global::Onnx.ValueInfoProto>();
    /// <summary>
    /// Information for the values in the graph. The ValueInfoProto.name's
    /// must be distinct. It is optional for a value to appear in value_info list.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.ValueInfoProto> ValueInfo {
      get { return valueInfo_; }
    }

    /// <summary>Field number for the "quantization_annotation" field.</summary>
    public const int QuantizationAnnotationFieldNumber = 14;
    private static readonly pb::FieldCodec<global::Onnx.TensorAnnotation> _repeated_quantizationAnnotation_codec
        = pb::FieldCodec.ForMessage(114, global::Onnx.TensorAnnotation.Parser);
    private readonly pbc::RepeatedField<global::Onnx.TensorAnnotation> quantizationAnnotation_ = new pbc::RepeatedField<global::Onnx.TensorAnnotation>();
    /// <summary>
    /// This field carries information to indicate the mapping among a tensor and its
    /// quantization parameter tensors. For example:
    /// For tensor 'a', it may have {'SCALE_TENSOR', 'a_scale'} and {'ZERO_POINT_TENSOR', 'a_zero_point'} annotated,
    /// which means, tensor 'a_scale' and tensor 'a_zero_point' are scale and zero point of tensor 'a' in the model.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.TensorAnnotation> QuantizationAnnotation {
      get { return quantizationAnnotation_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GraphProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GraphProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!node_.Equals(other.node_)) return false;
      if (Name != other.Name) return false;
      if(!initializer_.Equals(other.initializer_)) return false;
      if(!sparseInitializer_.Equals(other.sparseInitializer_)) return false;
      if (DocString != other.DocString) return false;
      if(!input_.Equals(other.input_)) return false;
      if(!output_.Equals(other.output_)) return false;
      if(!valueInfo_.Equals(other.valueInfo_)) return false;
      if(!quantizationAnnotation_.Equals(other.quantizationAnnotation_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= node_.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      hash ^= initializer_.GetHashCode();
      hash ^= sparseInitializer_.GetHashCode();
      if (DocString.Length != 0) hash ^= DocString.GetHashCode();
      hash ^= input_.GetHashCode();
      hash ^= output_.GetHashCode();
      hash ^= valueInfo_.GetHashCode();
      hash ^= quantizationAnnotation_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      node_.WriteTo(output, _repeated_node_codec);
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      initializer_.WriteTo(output, _repeated_initializer_codec);
      if (DocString.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(DocString);
      }
      input_.WriteTo(output, _repeated_input_codec);
      output_.WriteTo(output, _repeated_output_codec);
      valueInfo_.WriteTo(output, _repeated_valueInfo_codec);
      quantizationAnnotation_.WriteTo(output, _repeated_quantizationAnnotation_codec);
      sparseInitializer_.WriteTo(output, _repeated_sparseInitializer_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += node_.CalculateSize(_repeated_node_codec);
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      size += initializer_.CalculateSize(_repeated_initializer_codec);
      size += sparseInitializer_.CalculateSize(_repeated_sparseInitializer_codec);
      if (DocString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DocString);
      }
      size += input_.CalculateSize(_repeated_input_codec);
      size += output_.CalculateSize(_repeated_output_codec);
      size += valueInfo_.CalculateSize(_repeated_valueInfo_codec);
      size += quantizationAnnotation_.CalculateSize(_repeated_quantizationAnnotation_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GraphProto other) {
      if (other == null) {
        return;
      }
      node_.Add(other.node_);
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      initializer_.Add(other.initializer_);
      sparseInitializer_.Add(other.sparseInitializer_);
      if (other.DocString.Length != 0) {
        DocString = other.DocString;
      }
      input_.Add(other.input_);
      output_.Add(other.output_);
      valueInfo_.Add(other.valueInfo_);
      quantizationAnnotation_.Add(other.quantizationAnnotation_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            node_.AddEntriesFrom(input, _repeated_node_codec);
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 42: {
            initializer_.AddEntriesFrom(input, _repeated_initializer_codec);
            break;
          }
          case 82: {
            DocString = input.ReadString();
            break;
          }
          case 90: {
            input_.AddEntriesFrom(input, _repeated_input_codec);
            break;
          }
          case 98: {
            output_.AddEntriesFrom(input, _repeated_output_codec);
            break;
          }
          case 106: {
            valueInfo_.AddEntriesFrom(input, _repeated_valueInfo_codec);
            break;
          }
          case 114: {
            quantizationAnnotation_.AddEntriesFrom(input, _repeated_quantizationAnnotation_codec);
            break;
          }
          case 122: {
            sparseInitializer_.AddEntriesFrom(input, _repeated_sparseInitializer_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// Tensors
  ///
  /// A serialized tensor value.
  /// </summary>
  internal sealed partial class TensorProto : pb::IMessage<TensorProto> {
    private static readonly pb::MessageParser<TensorProto> _parser = new pb::MessageParser<TensorProto>(() => new TensorProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<TensorProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorProto(TensorProto other) : this() {
      dims_ = other.dims_.Clone();
      dataType_ = other.dataType_;
      Segment = other.segment_ != null ? other.Segment.Clone() : null;
      floatData_ = other.floatData_.Clone();
      int32Data_ = other.int32Data_.Clone();
      stringData_ = other.stringData_.Clone();
      int64Data_ = other.int64Data_.Clone();
      name_ = other.name_;
      docString_ = other.docString_;
      rawData_ = other.rawData_;
      externalData_ = other.externalData_.Clone();
      dataLocation_ = other.dataLocation_;
      doubleData_ = other.doubleData_.Clone();
      uint64Data_ = other.uint64Data_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorProto Clone() {
      return new TensorProto(this);
    }

    /// <summary>Field number for the "dims" field.</summary>
    public const int DimsFieldNumber = 1;
    private static readonly pb::FieldCodec<long> _repeated_dims_codec
        = pb::FieldCodec.ForInt64(10);
    private readonly pbc::RepeatedField<long> dims_ = new pbc::RepeatedField<long>();
    /// <summary>
    /// The shape of the tensor.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<long> Dims {
      get { return dims_; }
    }

    /// <summary>Field number for the "data_type" field.</summary>
    public const int DataTypeFieldNumber = 2;
    private int dataType_;
    /// <summary>
    /// The data type of the tensor.
    /// This field MUST have a valid TensorProto.DataType value
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int DataType {
      get { return dataType_; }
      set {
        dataType_ = value;
      }
    }

    /// <summary>Field number for the "segment" field.</summary>
    public const int SegmentFieldNumber = 3;
    private global::Onnx.TensorProto.Types.Segment segment_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TensorProto.Types.Segment Segment {
      get { return segment_; }
      set {
        segment_ = value;
      }
    }

    /// <summary>Field number for the "float_data" field.</summary>
    public const int FloatDataFieldNumber = 4;
    private static readonly pb::FieldCodec<float> _repeated_floatData_codec
        = pb::FieldCodec.ForFloat(34);
    private readonly pbc::RepeatedField<float> floatData_ = new pbc::RepeatedField<float>();
    /// <summary>
    /// For float and complex64 values
    /// Complex64 tensors are encoded as a single array of floats,
    /// with the real components appearing in odd numbered positions,
    /// and the corresponding imaginary component apparing in the
    /// subsequent even numbered position. (e.g., [1.0 + 2.0i, 3.0 + 4.0i]
    /// is encoded as [1.0, 2.0 ,3.0 ,4.0]
    /// When this field is present, the data_type field MUST be FLOAT or COMPLEX64.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<float> FloatData {
      get { return floatData_; }
    }

    /// <summary>Field number for the "int32_data" field.</summary>
    public const int Int32DataFieldNumber = 5;
    private static readonly pb::FieldCodec<int> _repeated_int32Data_codec
        = pb::FieldCodec.ForInt32(42);
    private readonly pbc::RepeatedField<int> int32Data_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// For int32, uint8, int8, uint16, int16, bool, and float16 values
    /// float16 values must be bit-wise converted to an uint16_t prior
    /// to writing to the buffer.
    /// When this field is present, the data_type field MUST be
    /// INT32, INT16, INT8, UINT16, UINT8, BOOL, or FLOAT16
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> Int32Data {
      get { return int32Data_; }
    }

    /// <summary>Field number for the "string_data" field.</summary>
    public const int StringDataFieldNumber = 6;
    private static readonly pb::FieldCodec<pb::ByteString> _repeated_stringData_codec
        = pb::FieldCodec.ForBytes(50);
    private readonly pbc::RepeatedField<pb::ByteString> stringData_ = new pbc::RepeatedField<pb::ByteString>();
    /// <summary>
    /// For strings.
    /// Each element of string_data is a UTF-8 encoded Unicode
    /// string. No trailing null, no leading BOM. The protobuf "string"
    /// scalar type is not used to match ML community conventions.
    /// When this field is present, the data_type field MUST be STRING
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<pb::ByteString> StringData {
      get { return stringData_; }
    }

    /// <summary>Field number for the "int64_data" field.</summary>
    public const int Int64DataFieldNumber = 7;
    private static readonly pb::FieldCodec<long> _repeated_int64Data_codec
        = pb::FieldCodec.ForInt64(58);
    private readonly pbc::RepeatedField<long> int64Data_ = new pbc::RepeatedField<long>();
    /// <summary>
    /// For int64.
    /// When this field is present, the data_type field MUST be INT64
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<long> Int64Data {
      get { return int64Data_; }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 8;
    private string name_ = "";
    /// <summary>
    /// Optionally, a name for the tensor.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "doc_string" field.</summary>
    public const int DocStringFieldNumber = 12;
    private string docString_ = "";
    /// <summary>
    /// A human-readable documentation for this tensor. Markdown is allowed.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DocString {
      get { return docString_; }
      set {
        docString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "raw_data" field.</summary>
    public const int RawDataFieldNumber = 9;
    private pb::ByteString rawData_ = pb::ByteString.Empty;
    /// <summary>
    /// Serializations can either use one of the fields above, or use this
    /// raw bytes field. The only exception is the string case, where one is
    /// required to store the content in the repeated bytes string_data field.
    ///
    /// When this raw_data field is used to store tensor value, elements MUST
    /// be stored in as fixed-width, little-endian order.
    /// Floating-point data types MUST be stored in IEEE 754 format.
    /// Complex64 elements must be written as two consecutive FLOAT values, real component first.
    /// Complex128 elements must be written as two consecutive DOUBLE values, real component first.
    /// Boolean type MUST be written one byte per tensor element (00000001 for true, 00000000 for false).
    ///
    /// Note: the advantage of specific field rather than the raw_data field is
    /// that in some cases (e.g. int data), protobuf does a better packing via
    /// variable length storage, and may lead to smaller binary footprint.
    /// When this field is present, the data_type field MUST NOT be STRING or UNDEFINED
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pb::ByteString RawData {
      get { return rawData_; }
      set {
        rawData_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "external_data" field.</summary>
    public const int ExternalDataFieldNumber = 13;
    private static readonly pb::FieldCodec<global::Onnx.StringStringEntryProto> _repeated_externalData_codec
        = pb::FieldCodec.ForMessage(106, global::Onnx.StringStringEntryProto.Parser);
    private readonly pbc::RepeatedField<global::Onnx.StringStringEntryProto> externalData_ = new pbc::RepeatedField<global::Onnx.StringStringEntryProto>();
    /// <summary>
    /// Data can be stored inside the protobuf file using type-specific fields or raw_data.
    /// Alternatively, raw bytes data can be stored in an external file, using the external_data field.
    /// external_data stores key-value pairs describing data location. Recognized keys are:
    /// - "location" (required) - POSIX filesystem path relative to the directory where the ONNX
    ///                           protobuf model was stored
    /// - "offset" (optional) - position of byte at which stored data begins. Integer stored as string.
    ///                         Offset values SHOULD be multiples 4096 (page size) to enable mmap support.
    /// - "length" (optional) - number of bytes containing data. Integer stored as string.
    /// - "checksum" (optional) - SHA1 digest of file specified in under 'location' key.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.StringStringEntryProto> ExternalData {
      get { return externalData_; }
    }

    /// <summary>Field number for the "data_location" field.</summary>
    public const int DataLocationFieldNumber = 14;
    private global::Onnx.TensorProto.Types.DataLocation dataLocation_ = 0;
    /// <summary>
    /// If value not set, data is stored in raw_data (if set) otherwise in type-specified field.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TensorProto.Types.DataLocation DataLocation {
      get { return dataLocation_; }
      set {
        dataLocation_ = value;
      }
    }

    /// <summary>Field number for the "double_data" field.</summary>
    public const int DoubleDataFieldNumber = 10;
    private static readonly pb::FieldCodec<double> _repeated_doubleData_codec
        = pb::FieldCodec.ForDouble(82);
    private readonly pbc::RepeatedField<double> doubleData_ = new pbc::RepeatedField<double>();
    /// <summary>
    /// For double
    /// Complex128 tensors are encoded as a single array of doubles,
    /// with the real components appearing in odd numbered positions,
    /// and the corresponding imaginary component apparing in the
    /// subsequent even numbered position. (e.g., [1.0 + 2.0i, 3.0 + 4.0i]
    /// is encoded as [1.0, 2.0 ,3.0 ,4.0]
    /// When this field is present, the data_type field MUST be DOUBLE or COMPLEX128
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<double> DoubleData {
      get { return doubleData_; }
    }

    /// <summary>Field number for the "uint64_data" field.</summary>
    public const int Uint64DataFieldNumber = 11;
    private static readonly pb::FieldCodec<ulong> _repeated_uint64Data_codec
        = pb::FieldCodec.ForUInt64(90);
    private readonly pbc::RepeatedField<ulong> uint64Data_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// For uint64 and uint32 values
    /// When this field is present, the data_type field MUST be
    /// UINT32 or UINT64
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<ulong> Uint64Data {
      get { return uint64Data_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as TensorProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(TensorProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!dims_.Equals(other.dims_)) return false;
      if (DataType != other.DataType) return false;
      if (!object.Equals(Segment, other.Segment)) return false;
      if(!floatData_.Equals(other.floatData_)) return false;
      if(!int32Data_.Equals(other.int32Data_)) return false;
      if(!stringData_.Equals(other.stringData_)) return false;
      if(!int64Data_.Equals(other.int64Data_)) return false;
      if (Name != other.Name) return false;
      if (DocString != other.DocString) return false;
      if (RawData != other.RawData) return false;
      if(!externalData_.Equals(other.externalData_)) return false;
      if (DataLocation != other.DataLocation) return false;
      if(!doubleData_.Equals(other.doubleData_)) return false;
      if(!uint64Data_.Equals(other.uint64Data_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= dims_.GetHashCode();
      if (DataType != 0) hash ^= DataType.GetHashCode();
      if (segment_ != null) hash ^= Segment.GetHashCode();
      hash ^= floatData_.GetHashCode();
      hash ^= int32Data_.GetHashCode();
      hash ^= stringData_.GetHashCode();
      hash ^= int64Data_.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (DocString.Length != 0) hash ^= DocString.GetHashCode();
      if (RawData.Length != 0) hash ^= RawData.GetHashCode();
      hash ^= externalData_.GetHashCode();
      if (DataLocation != 0) hash ^= DataLocation.GetHashCode();
      hash ^= doubleData_.GetHashCode();
      hash ^= uint64Data_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      dims_.WriteTo(output, _repeated_dims_codec);
      if (DataType != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(DataType);
      }
      if (segment_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Segment);
      }
      floatData_.WriteTo(output, _repeated_floatData_codec);
      int32Data_.WriteTo(output, _repeated_int32Data_codec);
      stringData_.WriteTo(output, _repeated_stringData_codec);
      int64Data_.WriteTo(output, _repeated_int64Data_codec);
      if (Name.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(Name);
      }
      if (RawData.Length != 0) {
        output.WriteRawTag(74);
        output.WriteBytes(RawData);
      }
      doubleData_.WriteTo(output, _repeated_doubleData_codec);
      uint64Data_.WriteTo(output, _repeated_uint64Data_codec);
      if (DocString.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(DocString);
      }
      externalData_.WriteTo(output, _repeated_externalData_codec);
      if (DataLocation != 0) {
        output.WriteRawTag(112);
        output.WriteEnum((int) DataLocation);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += dims_.CalculateSize(_repeated_dims_codec);
      if (DataType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DataType);
      }
      if (segment_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Segment);
      }
      size += floatData_.CalculateSize(_repeated_floatData_codec);
      size += int32Data_.CalculateSize(_repeated_int32Data_codec);
      size += stringData_.CalculateSize(_repeated_stringData_codec);
      size += int64Data_.CalculateSize(_repeated_int64Data_codec);
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (DocString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DocString);
      }
      if (RawData.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(RawData);
      }
      size += externalData_.CalculateSize(_repeated_externalData_codec);
      if (DataLocation != 0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) DataLocation);
      }
      size += doubleData_.CalculateSize(_repeated_doubleData_codec);
      size += uint64Data_.CalculateSize(_repeated_uint64Data_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(TensorProto other) {
      if (other == null) {
        return;
      }
      dims_.Add(other.dims_);
      if (other.DataType != 0) {
        DataType = other.DataType;
      }
      if (other.segment_ != null) {
        if (segment_ == null) {
          segment_ = new global::Onnx.TensorProto.Types.Segment();
        }
        Segment.MergeFrom(other.Segment);
      }
      floatData_.Add(other.floatData_);
      int32Data_.Add(other.int32Data_);
      stringData_.Add(other.stringData_);
      int64Data_.Add(other.int64Data_);
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.DocString.Length != 0) {
        DocString = other.DocString;
      }
      if (other.RawData.Length != 0) {
        RawData = other.RawData;
      }
      externalData_.Add(other.externalData_);
      if (other.DataLocation != 0) {
        DataLocation = other.DataLocation;
      }
      doubleData_.Add(other.doubleData_);
      uint64Data_.Add(other.uint64Data_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            dims_.AddEntriesFrom(input, _repeated_dims_codec);
            break;
          }
          case 16: {
            DataType = input.ReadInt32();
            break;
          }
          case 26: {
            if (segment_ == null) {
              segment_ = new global::Onnx.TensorProto.Types.Segment();
            }
            input.ReadMessage(segment_);
            break;
          }
          case 34:
          case 37: {
            floatData_.AddEntriesFrom(input, _repeated_floatData_codec);
            break;
          }
          case 42:
          case 40: {
            int32Data_.AddEntriesFrom(input, _repeated_int32Data_codec);
            break;
          }
          case 50: {
            stringData_.AddEntriesFrom(input, _repeated_stringData_codec);
            break;
          }
          case 58:
          case 56: {
            int64Data_.AddEntriesFrom(input, _repeated_int64Data_codec);
            break;
          }
          case 66: {
            Name = input.ReadString();
            break;
          }
          case 74: {
            RawData = input.ReadBytes();
            break;
          }
          case 82:
          case 81: {
            doubleData_.AddEntriesFrom(input, _repeated_doubleData_codec);
            break;
          }
          case 90:
          case 88: {
            uint64Data_.AddEntriesFrom(input, _repeated_uint64Data_codec);
            break;
          }
          case 98: {
            DocString = input.ReadString();
            break;
          }
          case 106: {
            externalData_.AddEntriesFrom(input, _repeated_externalData_codec);
            break;
          }
          case 112: {
            dataLocation_ = (global::Onnx.TensorProto.Types.DataLocation) input.ReadEnum();
            break;
          }
        }
      }
    }

    #region Nested types
    /// <summary>Container for nested types declared in the TensorProto message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static partial class Types {
      internal enum DataType {
        [pbr::OriginalName("UNDEFINED")] Undefined = 0,
        /// <summary>
        /// Basic types.
        /// </summary>
        [pbr::OriginalName("FLOAT")] Float = 1,
        /// <summary>
        /// uint8_t
        /// </summary>
        [pbr::OriginalName("UINT8")] Uint8 = 2,
        /// <summary>
        /// int8_t
        /// </summary>
        [pbr::OriginalName("INT8")] Int8 = 3,
        /// <summary>
        /// uint16_t
        /// </summary>
        [pbr::OriginalName("UINT16")] Uint16 = 4,
        /// <summary>
        /// int16_t
        /// </summary>
        [pbr::OriginalName("INT16")] Int16 = 5,
        /// <summary>
        /// int32_t
        /// </summary>
        [pbr::OriginalName("INT32")] Int32 = 6,
        /// <summary>
        /// int64_t
        /// </summary>
        [pbr::OriginalName("INT64")] Int64 = 7,
        /// <summary>
        /// string
        /// </summary>
        [pbr::OriginalName("STRING")] String = 8,
        /// <summary>
        /// bool
        /// </summary>
        [pbr::OriginalName("BOOL")] Bool = 9,
        /// <summary>
        /// IEEE754 half-precision floating-point format (16 bits wide).
        /// This format has 1 sign bit, 5 exponent bits, and 10 mantissa bits.
        /// </summary>
        [pbr::OriginalName("FLOAT16")] Float16 = 10,
        [pbr::OriginalName("DOUBLE")] Double = 11,
        [pbr::OriginalName("UINT32")] Uint32 = 12,
        [pbr::OriginalName("UINT64")] Uint64 = 13,
        /// <summary>
        /// complex with float32 real and imaginary components
        /// </summary>
        [pbr::OriginalName("COMPLEX64")] Complex64 = 14,
        /// <summary>
        /// complex with float64 real and imaginary components
        /// </summary>
        [pbr::OriginalName("COMPLEX128")] Complex128 = 15,
        /// <summary>
        /// Non-IEEE floating-point format based on IEEE754 single-precision
        /// floating-point number truncated to 16 bits.
        /// This format has 1 sign bit, 8 exponent bits, and 7 mantissa bits.
        /// </summary>
        [pbr::OriginalName("BFLOAT16")] Bfloat16 = 16,
      }

      /// <summary>
      /// Location of the data for this tensor. MUST be one of:
      /// - DEFAULT - data stored inside the protobuf message. Data is stored in raw_data (if set) otherwise in type-specified field.
      /// - EXTERNAL - data stored in an external location as described by external_data field.
      /// </summary>
      internal enum DataLocation {
        [pbr::OriginalName("DEFAULT")] Default = 0,
        [pbr::OriginalName("EXTERNAL")] External = 1,
      }

      /// <summary>
      /// For very large tensors, we may want to store them in chunks, in which
      /// case the following fields will specify the segment that is stored in
      /// the current TensorProto.
      /// </summary>
      internal sealed partial class Segment : pb::IMessage<Segment> {
        private static readonly pb::MessageParser<Segment> _parser = new pb::MessageParser<Segment>(() => new Segment());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pb::MessageParser<Segment> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::Onnx.TensorProto.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Segment() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Segment(Segment other) : this() {
          begin_ = other.begin_;
          end_ = other.end_;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Segment Clone() {
          return new Segment(this);
        }

        /// <summary>Field number for the "begin" field.</summary>
        public const int BeginFieldNumber = 1;
        private long begin_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public long Begin {
          get { return begin_; }
          set {
            begin_ = value;
          }
        }

        /// <summary>Field number for the "end" field.</summary>
        public const int EndFieldNumber = 2;
        private long end_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public long End {
          get { return end_; }
          set {
            end_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override bool Equals(object other) {
          return Equals(other as Segment);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public bool Equals(Segment other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (Begin != other.Begin) return false;
          if (End != other.End) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override int GetHashCode() {
          int hash = 1;
          if (Begin != 0L) hash ^= Begin.GetHashCode();
          if (End != 0L) hash ^= End.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void WriteTo(pb::CodedOutputStream output) {
          if (Begin != 0L) {
            output.WriteRawTag(8);
            output.WriteInt64(Begin);
          }
          if (End != 0L) {
            output.WriteRawTag(16);
            output.WriteInt64(End);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int CalculateSize() {
          int size = 0;
          if (Begin != 0L) {
            size += 1 + pb::CodedOutputStream.ComputeInt64Size(Begin);
          }
          if (End != 0L) {
            size += 1 + pb::CodedOutputStream.ComputeInt64Size(End);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(Segment other) {
          if (other == null) {
            return;
          }
          if (other.Begin != 0L) {
            Begin = other.Begin;
          }
          if (other.End != 0L) {
            End = other.End;
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(pb::CodedInputStream input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                Begin = input.ReadInt64();
                break;
              }
              case 16: {
                End = input.ReadInt64();
                break;
              }
            }
          }
        }

      }

    }
    #endregion

  }

  /// <summary>
  /// A serialized sparse-tensor value
  /// </summary>
  internal sealed partial class SparseTensorProto : pb::IMessage<SparseTensorProto> {
    private static readonly pb::MessageParser<SparseTensorProto> _parser = new pb::MessageParser<SparseTensorProto>(() => new SparseTensorProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<SparseTensorProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SparseTensorProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SparseTensorProto(SparseTensorProto other) : this() {
      Values = other.values_ != null ? other.Values.Clone() : null;
      Indices = other.indices_ != null ? other.Indices.Clone() : null;
      dims_ = other.dims_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public SparseTensorProto Clone() {
      return new SparseTensorProto(this);
    }

    /// <summary>Field number for the "values" field.</summary>
    public const int ValuesFieldNumber = 1;
    private global::Onnx.TensorProto values_;
    /// <summary>
    /// The sequence of non-default values are encoded as a tensor of shape [NNZ].
    /// The default-value is zero for numeric tensors, and empty-string for string tensors.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TensorProto Values {
      get { return values_; }
      set {
        values_ = value;
      }
    }

    /// <summary>Field number for the "indices" field.</summary>
    public const int IndicesFieldNumber = 2;
    private global::Onnx.TensorProto indices_;
    /// <summary>
    /// The indices of the non-default values, which may be stored in one of two formats.
    /// (a) Indices can be a tensor of shape [NNZ, rank] with the [i,j]-th value
    /// corresponding to the j-th index of the i-th value (in the values tensor).
    /// (b) Indices can be a tensor of shape [NNZ], in which case the i-th value
    /// must be the linearized-index of the i-th value (in the values tensor).
    /// The linearized-index can be converted into an index tuple (k_1,...,k_rank)
    /// using the shape provided below.
    /// The indices must appear in ascending order without duplication.
    /// In the first format, the ordering is lexicographic-ordering:
    /// e.g., index-value [1,4] must appear before [2,1]
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TensorProto Indices {
      get { return indices_; }
      set {
        indices_ = value;
      }
    }

    /// <summary>Field number for the "dims" field.</summary>
    public const int DimsFieldNumber = 3;
    private static readonly pb::FieldCodec<long> _repeated_dims_codec
        = pb::FieldCodec.ForInt64(26);
    private readonly pbc::RepeatedField<long> dims_ = new pbc::RepeatedField<long>();
    /// <summary>
    /// The shape of the underlying dense-tensor: [dim_1, dim_2, ... dim_rank]
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<long> Dims {
      get { return dims_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as SparseTensorProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(SparseTensorProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Values, other.Values)) return false;
      if (!object.Equals(Indices, other.Indices)) return false;
      if(!dims_.Equals(other.dims_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (values_ != null) hash ^= Values.GetHashCode();
      if (indices_ != null) hash ^= Indices.GetHashCode();
      hash ^= dims_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (values_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Values);
      }
      if (indices_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Indices);
      }
      dims_.WriteTo(output, _repeated_dims_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (values_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Values);
      }
      if (indices_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Indices);
      }
      size += dims_.CalculateSize(_repeated_dims_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(SparseTensorProto other) {
      if (other == null) {
        return;
      }
      if (other.values_ != null) {
        if (values_ == null) {
          values_ = new global::Onnx.TensorProto();
        }
        Values.MergeFrom(other.Values);
      }
      if (other.indices_ != null) {
        if (indices_ == null) {
          indices_ = new global::Onnx.TensorProto();
        }
        Indices.MergeFrom(other.Indices);
      }
      dims_.Add(other.dims_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (values_ == null) {
              values_ = new global::Onnx.TensorProto();
            }
            input.ReadMessage(values_);
            break;
          }
          case 18: {
            if (indices_ == null) {
              indices_ = new global::Onnx.TensorProto();
            }
            input.ReadMessage(indices_);
            break;
          }
          case 26:
          case 24: {
            dims_.AddEntriesFrom(input, _repeated_dims_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// Defines a tensor shape. A dimension can be either an integer value
  /// or a symbolic variable. A symbolic variable represents an unknown
  /// dimension.
  /// </summary>
  internal sealed partial class TensorShapeProto : pb::IMessage<TensorShapeProto> {
    private static readonly pb::MessageParser<TensorShapeProto> _parser = new pb::MessageParser<TensorShapeProto>(() => new TensorShapeProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<TensorShapeProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorShapeProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorShapeProto(TensorShapeProto other) : this() {
      dim_ = other.dim_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TensorShapeProto Clone() {
      return new TensorShapeProto(this);
    }

    /// <summary>Field number for the "dim" field.</summary>
    public const int DimFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Onnx.TensorShapeProto.Types.Dimension> _repeated_dim_codec
        = pb::FieldCodec.ForMessage(10, global::Onnx.TensorShapeProto.Types.Dimension.Parser);
    private readonly pbc::RepeatedField<global::Onnx.TensorShapeProto.Types.Dimension> dim_ = new pbc::RepeatedField<global::Onnx.TensorShapeProto.Types.Dimension>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Onnx.TensorShapeProto.Types.Dimension> Dim {
      get { return dim_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as TensorShapeProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(TensorShapeProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!dim_.Equals(other.dim_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= dim_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      dim_.WriteTo(output, _repeated_dim_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += dim_.CalculateSize(_repeated_dim_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(TensorShapeProto other) {
      if (other == null) {
        return;
      }
      dim_.Add(other.dim_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dim_.AddEntriesFrom(input, _repeated_dim_codec);
            break;
          }
        }
      }
    }

    #region Nested types
    /// <summary>Container for nested types declared in the TensorShapeProto message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static partial class Types {
      internal sealed partial class Dimension : pb::IMessage<Dimension> {
        private static readonly pb::MessageParser<Dimension> _parser = new pb::MessageParser<Dimension>(() => new Dimension());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pb::MessageParser<Dimension> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::Onnx.TensorShapeProto.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Dimension() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Dimension(Dimension other) : this() {
          denotation_ = other.denotation_;
          switch (other.ValueCase) {
            case ValueOneofCase.DimValue:
              DimValue = other.DimValue;
              break;
            case ValueOneofCase.DimParam:
              DimParam = other.DimParam;
              break;
          }

          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Dimension Clone() {
          return new Dimension(this);
        }

        /// <summary>Field number for the "dim_value" field.</summary>
        public const int DimValueFieldNumber = 1;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public long DimValue {
          get { return valueCase_ == ValueOneofCase.DimValue ? (long) value_ : 0L; }
          set {
            value_ = value;
            valueCase_ = ValueOneofCase.DimValue;
          }
        }

        /// <summary>Field number for the "dim_param" field.</summary>
        public const int DimParamFieldNumber = 2;
        /// <summary>
        /// namespace Shape
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public string DimParam {
          get { return valueCase_ == ValueOneofCase.DimParam ? (string) value_ : ""; }
          set {
            value_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
            valueCase_ = ValueOneofCase.DimParam;
          }
        }

        /// <summary>Field number for the "denotation" field.</summary>
        public const int DenotationFieldNumber = 3;
        private string denotation_ = "";
        /// <summary>
        /// Standard denotation can optionally be used to denote tensor
        /// dimensions with standard semantic descriptions to ensure
        /// that operations are applied to the correct axis of a tensor.
        /// Refer to https://github.com/onnx/onnx/blob/master/docs/DimensionDenotation.md#denotation-definition
        /// for pre-defined dimension denotations.
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public string Denotation {
          get { return denotation_; }
          set {
            denotation_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
          }
        }

        private object value_;
        /// <summary>Enum of possible cases for the "value" oneof.</summary>
        public enum ValueOneofCase {
          None = 0,
          DimValue = 1,
          DimParam = 2,
        }
        private ValueOneofCase valueCase_ = ValueOneofCase.None;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public ValueOneofCase ValueCase {
          get { return valueCase_; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void ClearValue() {
          valueCase_ = ValueOneofCase.None;
          value_ = null;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override bool Equals(object other) {
          return Equals(other as Dimension);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public bool Equals(Dimension other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (DimValue != other.DimValue) return false;
          if (DimParam != other.DimParam) return false;
          if (Denotation != other.Denotation) return false;
          if (ValueCase != other.ValueCase) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override int GetHashCode() {
          int hash = 1;
          if (valueCase_ == ValueOneofCase.DimValue) hash ^= DimValue.GetHashCode();
          if (valueCase_ == ValueOneofCase.DimParam) hash ^= DimParam.GetHashCode();
          if (Denotation.Length != 0) hash ^= Denotation.GetHashCode();
          hash ^= (int) valueCase_;
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void WriteTo(pb::CodedOutputStream output) {
          if (valueCase_ == ValueOneofCase.DimValue) {
            output.WriteRawTag(8);
            output.WriteInt64(DimValue);
          }
          if (valueCase_ == ValueOneofCase.DimParam) {
            output.WriteRawTag(18);
            output.WriteString(DimParam);
          }
          if (Denotation.Length != 0) {
            output.WriteRawTag(26);
            output.WriteString(Denotation);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int CalculateSize() {
          int size = 0;
          if (valueCase_ == ValueOneofCase.DimValue) {
            size += 1 + pb::CodedOutputStream.ComputeInt64Size(DimValue);
          }
          if (valueCase_ == ValueOneofCase.DimParam) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(DimParam);
          }
          if (Denotation.Length != 0) {
            size += 1 + pb::CodedOutputStream.ComputeStringSize(Denotation);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(Dimension other) {
          if (other == null) {
            return;
          }
          if (other.Denotation.Length != 0) {
            Denotation = other.Denotation;
          }
          switch (other.ValueCase) {
            case ValueOneofCase.DimValue:
              DimValue = other.DimValue;
              break;
            case ValueOneofCase.DimParam:
              DimParam = other.DimParam;
              break;
          }

          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(pb::CodedInputStream input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                DimValue = input.ReadInt64();
                break;
              }
              case 18: {
                DimParam = input.ReadString();
                break;
              }
              case 26: {
                Denotation = input.ReadString();
                break;
              }
            }
          }
        }

      }

    }
    #endregion

  }

  /// <summary>
  /// Types
  ///
  /// The standard ONNX data types.
  /// </summary>
  internal sealed partial class TypeProto : pb::IMessage<TypeProto> {
    private static readonly pb::MessageParser<TypeProto> _parser = new pb::MessageParser<TypeProto>(() => new TypeProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<TypeProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TypeProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TypeProto(TypeProto other) : this() {
      denotation_ = other.denotation_;
      switch (other.ValueCase) {
        case ValueOneofCase.TensorType:
          TensorType = other.TensorType.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TypeProto Clone() {
      return new TypeProto(this);
    }

    /// <summary>Field number for the "tensor_type" field.</summary>
    public const int TensorTypeFieldNumber = 1;
    /// <summary>
    /// The type of a tensor.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Onnx.TypeProto.Types.Tensor TensorType {
      get { return valueCase_ == ValueOneofCase.TensorType ? (global::Onnx.TypeProto.Types.Tensor) value_ : null; }
      set {
        value_ = value;
        valueCase_ = value == null ? ValueOneofCase.None : ValueOneofCase.TensorType;
      }
    }

    /// <summary>Field number for the "denotation" field.</summary>
    public const int DenotationFieldNumber = 6;
    private string denotation_ = "";
    /// <summary>
    /// An optional denotation can be used to denote the whole 
    /// type with a standard semantic description as to what is 
    /// stored inside. Refer to https://github.com/onnx/onnx/blob/master/docs/TypeDenotation.md#type-denotation-definition
    /// for pre-defined type denotations.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Denotation {
      get { return denotation_; }
      set {
        denotation_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    private object value_;
    /// <summary>Enum of possible cases for the "value" oneof.</summary>
    public enum ValueOneofCase {
      None = 0,
      TensorType = 1,
    }
    private ValueOneofCase valueCase_ = ValueOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ValueOneofCase ValueCase {
      get { return valueCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void ClearValue() {
      valueCase_ = ValueOneofCase.None;
      value_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as TypeProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(TypeProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(TensorType, other.TensorType)) return false;
      if (Denotation != other.Denotation) return false;
      if (ValueCase != other.ValueCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (valueCase_ == ValueOneofCase.TensorType) hash ^= TensorType.GetHashCode();
      if (Denotation.Length != 0) hash ^= Denotation.GetHashCode();
      hash ^= (int) valueCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (valueCase_ == ValueOneofCase.TensorType) {
        output.WriteRawTag(10);
        output.WriteMessage(TensorType);
      }
      if (Denotation.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Denotation);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (valueCase_ == ValueOneofCase.TensorType) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(TensorType);
      }
      if (Denotation.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Denotation);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(TypeProto other) {
      if (other == null) {
        return;
      }
      if (other.Denotation.Length != 0) {
        Denotation = other.Denotation;
      }
      switch (other.ValueCase) {
        case ValueOneofCase.TensorType:
          if (TensorType == null) {
            TensorType = new global::Onnx.TypeProto.Types.Tensor();
          }
          TensorType.MergeFrom(other.TensorType);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Onnx.TypeProto.Types.Tensor subBuilder = new global::Onnx.TypeProto.Types.Tensor();
            if (valueCase_ == ValueOneofCase.TensorType) {
              subBuilder.MergeFrom(TensorType);
            }
            input.ReadMessage(subBuilder);
            TensorType = subBuilder;
            break;
          }
          case 50: {
            Denotation = input.ReadString();
            break;
          }
        }
      }
    }

    #region Nested types
    /// <summary>Container for nested types declared in the TypeProto message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static partial class Types {
      internal sealed partial class Tensor : pb::IMessage<Tensor> {
        private static readonly pb::MessageParser<Tensor> _parser = new pb::MessageParser<Tensor>(() => new Tensor());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pb::MessageParser<Tensor> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::Onnx.TypeProto.Descriptor.NestedTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Tensor() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Tensor(Tensor other) : this() {
          elemType_ = other.elemType_;
          Shape = other.shape_ != null ? other.Shape.Clone() : null;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public Tensor Clone() {
          return new Tensor(this);
        }

        /// <summary>Field number for the "elem_type" field.</summary>
        public const int ElemTypeFieldNumber = 1;
        private int elemType_;
        /// <summary>
        /// This field MUST NOT have the value of UNDEFINED
        /// This field MUST have a valid TensorProto.DataType value
        /// This field MUST be present for this version of the IR.
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int ElemType {
          get { return elemType_; }
          set {
            elemType_ = value;
          }
        }

        /// <summary>Field number for the "shape" field.</summary>
        public const int ShapeFieldNumber = 2;
        private global::Onnx.TensorShapeProto shape_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public global::Onnx.TensorShapeProto Shape {
          get { return shape_; }
          set {
            shape_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override bool Equals(object other) {
          return Equals(other as Tensor);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public bool Equals(Tensor other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (ElemType != other.ElemType) return false;
          if (!object.Equals(Shape, other.Shape)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override int GetHashCode() {
          int hash = 1;
          if (ElemType != 0) hash ^= ElemType.GetHashCode();
          if (shape_ != null) hash ^= Shape.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void WriteTo(pb::CodedOutputStream output) {
          if (ElemType != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ElemType);
          }
          if (shape_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Shape);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int CalculateSize() {
          int size = 0;
          if (ElemType != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(ElemType);
          }
          if (shape_ != null) {
            size += 1 + pb::CodedOutputStream.ComputeMessageSize(Shape);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(Tensor other) {
          if (other == null) {
            return;
          }
          if (other.ElemType != 0) {
            ElemType = other.ElemType;
          }
          if (other.shape_ != null) {
            if (shape_ == null) {
              shape_ = new global::Onnx.TensorShapeProto();
            }
            Shape.MergeFrom(other.Shape);
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(pb::CodedInputStream input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                ElemType = input.ReadInt32();
                break;
              }
              case 18: {
                if (shape_ == null) {
                  shape_ = new global::Onnx.TensorShapeProto();
                }
                input.ReadMessage(shape_);
                break;
              }
            }
          }
        }

      }

      internal sealed partial class SparseTensor : pb::IMessage<SparseTensor> {
        private static readonly pb::MessageParser<SparseTensor> _parser = new pb::MessageParser<SparseTensor>(() => new SparseTensor());
        private pb::UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pb::MessageParser<SparseTensor> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static pbr::MessageDescriptor Descriptor {
          get { return global::Onnx.TypeProto.Descriptor.NestedTypes[1]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        pbr::MessageDescriptor pb::IMessage.Descriptor {
          get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public SparseTensor() {
          OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public SparseTensor(SparseTensor other) : this() {
          elemType_ = other.elemType_;
          Shape = other.shape_ != null ? other.Shape.Clone() : null;
          _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public SparseTensor Clone() {
          return new SparseTensor(this);
        }

        /// <summary>Field number for the "elem_type" field.</summary>
        public const int ElemTypeFieldNumber = 1;
        private int elemType_;
        /// <summary>
        /// This field MUST NOT have the value of UNDEFINED 
        /// This field MUST have a valid TensorProto.DataType value
        /// This field MUST be present for this version of the IR. 
        /// </summary>
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int ElemType {
          get { return elemType_; }
          set {
            elemType_ = value;
          }
        }

        /// <summary>Field number for the "shape" field.</summary>
        public const int ShapeFieldNumber = 2;
        private global::Onnx.TensorShapeProto shape_;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public global::Onnx.TensorShapeProto Shape {
          get { return shape_; }
          set {
            shape_ = value;
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override bool Equals(object other) {
          return Equals(other as SparseTensor);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public bool Equals(SparseTensor other) {
          if (ReferenceEquals(other, null)) {
            return false;
          }
          if (ReferenceEquals(other, this)) {
            return true;
          }
          if (ElemType != other.ElemType) return false;
          if (!object.Equals(Shape, other.Shape)) return false;
          return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override int GetHashCode() {
          int hash = 1;
          if (ElemType != 0) hash ^= ElemType.GetHashCode();
          if (shape_ != null) hash ^= Shape.GetHashCode();
          if (_unknownFields != null) {
            hash ^= _unknownFields.GetHashCode();
          }
          return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override string ToString() {
          return pb::JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void WriteTo(pb::CodedOutputStream output) {
          if (ElemType != 0) {
            output.WriteRawTag(8);
            output.WriteInt32(ElemType);
          }
          if (shape_ != null) {
            output.WriteRawTag(18);
            output.WriteMessage(Shape);
          }
          if (_unknownFields != null) {
            _unknownFields.WriteTo(output);
          }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int CalculateSize() {
          int size = 0;
          if (ElemType != 0) {
            size += 1 + pb::CodedOutputStream.ComputeInt32Size(ElemType);
          }
          if (shape_ != null) {
            size += 1 + pb::CodedOutputStream.ComputeMessageSize(Shape);
          }
          if (_unknownFields != null) {
            size += _unknownFields.CalculateSize();
          }
          return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(SparseTensor other) {
          if (other == null) {
            return;
          }
          if (other.ElemType != 0) {
            ElemType = other.ElemType;
          }
          if (other.shape_ != null) {
            if (shape_ == null) {
              shape_ = new global::Onnx.TensorShapeProto();
            }
            Shape.MergeFrom(other.Shape);
          }
          _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(pb::CodedInputStream input) {
          uint tag;
          while ((tag = input.ReadTag()) != 0) {
            switch(tag) {
              default:
                _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                break;
              case 8: {
                ElemType = input.ReadInt32();
                break;
              }
              case 18: {
                if (shape_ == null) {
                  shape_ = new global::Onnx.TensorShapeProto();
                }
                input.ReadMessage(shape_);
                break;
              }
            }
          }
        }

      }

    }
    #endregion

  }

  /// <summary>
  /// Operator Sets
  ///
  /// OperatorSets are uniquely identified by a (domain, opset_version) pair.
  /// </summary>
  internal sealed partial class OperatorSetIdProto : pb::IMessage<OperatorSetIdProto> {
    private static readonly pb::MessageParser<OperatorSetIdProto> _parser = new pb::MessageParser<OperatorSetIdProto>(() => new OperatorSetIdProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<OperatorSetIdProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Onnx.OnnxReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public OperatorSetIdProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public OperatorSetIdProto(OperatorSetIdProto other) : this() {
      domain_ = other.domain_;
      version_ = other.version_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public OperatorSetIdProto Clone() {
      return new OperatorSetIdProto(this);
    }

    /// <summary>Field number for the "domain" field.</summary>
    public const int DomainFieldNumber = 1;
    private string domain_ = "";
    /// <summary>
    /// The domain of the operator set being identified.
    /// The empty string ("") or absence of this field implies the operator
    /// set that is defined as part of the ONNX specification.
    /// This field MUST be present in this version of the IR when referring to any other operator set.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Domain {
      get { return domain_; }
      set {
        domain_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 2;
    private long version_;
    /// <summary>
    /// The version of the operator set being identified.
    /// This field MUST be present in this version of the IR.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Version {
      get { return version_; }
      set {
        version_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as OperatorSetIdProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(OperatorSetIdProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Domain != other.Domain) return false;
      if (Version != other.Version) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Domain.Length != 0) hash ^= Domain.GetHashCode();
      if (Version != 0L) hash ^= Version.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Domain.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Domain);
      }
      if (Version != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Domain.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Domain);
      }
      if (Version != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Version);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(OperatorSetIdProto other) {
      if (other == null) {
        return;
      }
      if (other.Domain.Length != 0) {
        Domain = other.Domain;
      }
      if (other.Version != 0L) {
        Version = other.Version;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Domain = input.ReadString();
            break;
          }
          case 16: {
            Version = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
