{"version": "0.2.0", "configurations": [{"name": "Unity Editor", "type": "unity", "request": "launch", "unityEditorPid": "${command:unity.selectUnityProcess}"}, {"name": "Unity Player", "type": "unity", "request": "launch", "target": "player"}, {"name": "Attach to Unity Editor", "type": "unity", "request": "attach"}, {"name": "Python: ML-Agents Training", "type": "python", "request": "launch", "program": "${workspaceFolder}/train_squadmate.py", "args": ["--run-id=debug_session", "--config=config/squadmate_config.yaml"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "Python: Unity 6 Training", "type": "python", "request": "launch", "program": "${workspaceFolder}/train_squadmate.py", "args": ["--run-id=unity6_debug", "--config=config/squadmate_unity6_config.yaml"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false}, {"name": "Python: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/validate_setup.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}, {"name": "Python: Unity 6 Quick Start", "type": "python", "request": "launch", "program": "${workspaceFolder}/unity6_quickstart.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}, {"name": "Python: Fix Dependencies", "type": "python", "request": "launch", "program": "${workspaceFolder}/fix_dependencies.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}], "compounds": [{"name": "Unity + ML-Agents Debug", "configurations": ["Unity Editor", "Python: ML-Agents Training"], "stopAll": true, "presentation": {"hidden": false, "group": "ML-Agents", "order": 1}}, {"name": "Unity 6 + ML-Agents Debug", "configurations": ["Unity Editor", "Python: Unity 6 Training"], "stopAll": true, "presentation": {"hidden": false, "group": "Unity 6", "order": 1}}]}