@echo off
echo 🎮 PUBG SquadMate AI Training Launcher
echo =====================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if ML-Agents is installed
python -c "import mlagents" >nul 2>&1
if errorlevel 1 (
    echo ❌ ML-Agents is not installed
    echo Running dependency fix script...
    python fix_dependencies.py
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        echo Please check the output above for solutions
        pause
        exit /b 1
    )
)

echo ✅ Dependencies check passed
echo.

REM Validate project structure
echo 📁 Validating project structure...
python train_squadmate.py --validate
if errorlevel 1 (
    echo ❌ Project validation failed
    echo Please check that all required files are present
    pause
    exit /b 1
)

echo ✅ Project structure is valid
echo.

REM Ask user for training options
echo 🎯 Training Options:
echo 1. Start new training
echo 2. Resume existing training
echo 3. Start with TensorBoard monitoring
echo 4. Export existing model
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo 🚀 Starting new training...
    python train_squadmate.py --run-id squadmate_v1
) else if "%choice%"=="2" (
    echo 🔄 Resuming training...
    python train_squadmate.py --run-id squadmate_v1 --resume
) else if "%choice%"=="3" (
    echo 📊 Starting training with TensorBoard...
    python train_squadmate.py --run-id squadmate_v1 --tensorboard
) else if "%choice%"=="4" (
    echo 📦 Exporting model...
    python train_squadmate.py --export --run-id squadmate_v1
) else (
    echo ❌ Invalid choice
    pause
    exit /b 1
)

echo.
echo 🎉 Operation completed!
echo.
echo 📋 Next Steps:
echo 1. If training: Monitor progress in Unity and TensorBoard
echo 2. If exported: Copy the .onnx file to your Unity project
echo 3. Check the results/ folder for saved models
echo.
pause
