using UnityEngine;

/// <summary>
/// Simple rotation component for pickup objects
/// Makes medkits and weapons rotate for visual appeal
/// </summary>
public class RotateObject : MonoBehaviour
{
    [Header("Rotation Settings")]
    public float rotationSpeed = 50f;
    public Vector3 rotationAxis = Vector3.up;
    public bool randomizeStartRotation = true;
    
    [Header("Bob Animation")]
    public bool enableBobbing = true;
    public float bobHeight = 0.2f;
    public float bobSpeed = 2f;
    
    private Vector3 startPosition;
    private float bobOffset;
    
    void Start()
    {
        // Store initial position for bobbing
        startPosition = transform.position;
        
        // Randomize starting rotation if enabled
        if (randomizeStartRotation)
        {
            transform.rotation = Quaternion.Euler(
                Random.Range(0f, 360f),
                Random.Range(0f, 360f),
                Random.Range(0f, 360f)
            );
        }
        
        // Randomize bob offset so objects don't all bob in sync
        bobOffset = Random.Range(0f, Mathf.PI * 2f);
    }
    
    void Update()
    {
        // Rotate the object
        transform.Rotate(rotationAxis * rotationSpeed * Time.deltaTime);
        
        // Add bobbing animation if enabled
        if (enableBobbing)
        {
            float bobY = Mathf.Sin(Time.time * bobSpeed + bobOffset) * bobHeight;
            transform.position = startPosition + Vector3.up * bobY;
        }
    }
    
    /// <summary>
    /// Set custom rotation speed
    /// </summary>
    public void SetRotationSpeed(float speed)
    {
        rotationSpeed = speed;
    }
    
    /// <summary>
    /// Set custom rotation axis
    /// </summary>
    public void SetRotationAxis(Vector3 axis)
    {
        rotationAxis = axis.normalized;
    }
    
    /// <summary>
    /// Enable or disable bobbing animation
    /// </summary>
    public void SetBobbing(bool enabled)
    {
        enableBobbing = enabled;
        if (!enabled)
        {
            transform.position = startPosition;
        }
    }
}
