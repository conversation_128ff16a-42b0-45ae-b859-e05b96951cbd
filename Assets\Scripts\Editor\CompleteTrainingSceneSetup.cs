using UnityEngine;
using UnityEditor;
using UnityEngine.AI;
using Unity.MLAgents.Policies;
using UnityEditor.AI;

/// <summary>
/// Complete automated training scene setup for SquadMate AI
/// Creates everything needed for ML-Agents training in one click
/// </summary>
public class CompleteTrainingSceneSetup : EditorWindow
{
    [MenuItem("SquadMate AI/Create Complete Training Scene")]
    public static void ShowWindow()
    {
        GetWindow<CompleteTrainingSceneSetup>("Complete Training Setup");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Complete SquadMate Training Scene Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("This will create EVERYTHING needed for training:");
        GUILayout.Label("✅ Complete terrain with NavMesh");
        GUILayout.Label("✅ Player with PlayerController");
        GUILayout.Label("✅ SquadMate with all ML-Agents components");
        GUILayout.Label("✅ Environment with GameEnvironment script");
        GUILayout.Label("✅ All prefabs and materials");
        GUILayout.Label("✅ Spawn points and references");
        GUILayout.Label("✅ Fully configured and ready to train!");
        
        GUILayout.Space(20);
        
        if (GUILayout.Button("🚀 CREATE COMPLETE TRAINING SCENE", GUILayout.Height(50)))
        {
            CreateCompleteTrainingScene();
        }
        
        GUILayout.Space(10);
        
        EditorGUILayout.HelpBox("This will clear the current scene and create a new training environment. Make sure to save any important work first!", MessageType.Warning);
    }
    
    public static void CreateCompleteTrainingScene()
    {
        Debug.Log("🚀 Creating Complete SquadMate Training Scene...");
        Debug.Log("This may take a few moments...");
        
        try
        {
            // Step 1: Clear and prepare scene
            ClearCurrentScene();
            
            // Step 2: Create all materials
            CreateAllMaterials();
            
            // Step 3: Create terrain
            GameObject terrain = CreateTrainingTerrain();
            
            // Step 4: Setup lighting
            SetupSceneLighting();
            
            // Step 5: Create player with components
            GameObject player = CreatePlayerWithComponents();
            
            // Step 6: Create squadmate with all ML-Agents components
            GameObject squadmate = CreateSquadMateWithComponents();
            
            // Step 7: Create environment with scripts
            GameObject environment = CreateEnvironmentWithComponents();
            
            // Step 8: Create spawn points
            CreateSpawnPointsSystem(environment);
            
            // Step 9: Create all prefabs
            CreateAllPrefabs();
            
            // Step 10: Configure all references
            ConfigureAllReferences(player, squadmate, environment);
            
            // Step 11: Setup NavMesh
            SetupNavMeshSystem();
            
            // Step 12: Save scene
            SaveTrainingScene();
            
            Debug.Log("🎉 COMPLETE TRAINING SCENE CREATED SUCCESSFULLY!");
            Debug.Log("✅ Everything is configured and ready!");
            Debug.Log("🎮 Press Play in Unity to start training your SquadMate AI!");
            
            // Show success dialog
            EditorUtility.DisplayDialog("Success!", 
                "Complete SquadMate training scene created successfully!\n\n" +
                "✅ All components configured\n" +
                "✅ All references assigned\n" +
                "✅ NavMesh baked\n" +
                "✅ Ready to train!\n\n" +
                "Press Play to start training!", "Awesome!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error creating training scene: {e.Message}");
            EditorUtility.DisplayDialog("Error", $"Failed to create training scene:\n{e.Message}", "OK");
        }
    }
    
    private static void ClearCurrentScene()
    {
        Debug.Log("🧹 Clearing current scene...");
        
        // Keep only the main camera
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name != "Main Camera" && obj.transform.parent == null)
            {
                DestroyImmediate(obj);
            }
        }
    }
    
    private static void CreateAllMaterials()
    {
        Debug.Log("🎨 Creating materials...");
        
        if (!AssetDatabase.IsValidFolder("Assets/Materials"))
            AssetDatabase.CreateFolder("Assets", "Materials");
        
        CreateMaterial("PlayerMaterial", Color.blue);
        CreateMaterial("SquadMateMaterial", Color.green);
        CreateMaterial("EnemyMaterial", Color.red);
        CreateMaterial("MedkitMaterial", Color.cyan);
        CreateMaterial("WeaponMaterial", Color.yellow);
        CreateMaterial("SpawnPointMaterial", Color.magenta);
        
        AssetDatabase.SaveAssets();
    }
    
    private static void CreateMaterial(string name, Color color)
    {
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        mat.color = color;
        AssetDatabase.CreateAsset(mat, $"Assets/Materials/{name}.mat");
    }
    
    private static GameObject CreateTrainingTerrain()
    {
        Debug.Log("🌍 Creating training terrain...");
        
        TerrainData terrainData = new TerrainData();
        terrainData.heightmapResolution = 513;
        terrainData.size = new Vector3(50, 8, 50);
        
        // Create interesting height variation
        float[,] heights = new float[513, 513];
        for (int x = 0; x < 513; x++)
        {
            for (int y = 0; y < 513; y++)
            {
                float height = Mathf.PerlinNoise(x * 0.01f, y * 0.01f) * 0.1f;
                height += Mathf.PerlinNoise(x * 0.05f, y * 0.05f) * 0.02f;
                heights[x, y] = height;
            }
        }
        terrainData.SetHeights(0, 0, heights);
        
        if (!AssetDatabase.IsValidFolder("Assets/Terrain"))
            AssetDatabase.CreateFolder("Assets", "Terrain");
        AssetDatabase.CreateAsset(terrainData, "Assets/Terrain/TrainingTerrain.asset");
        
        GameObject terrainObj = Terrain.CreateTerrainGameObject(terrainData);
        terrainObj.name = "Training Terrain";
        GameObjectUtility.SetStaticEditorFlags(terrainObj, StaticEditorFlags.NavigationStatic);
        
        return terrainObj;
    }
    
    private static void SetupSceneLighting()
    {
        Debug.Log("💡 Setting up lighting...");
        
        GameObject lightObj = new GameObject("Directional Light");
        Light light = lightObj.AddComponent<Light>();
        light.type = LightType.Directional;
        light.intensity = 1.5f;
        light.shadows = LightShadows.Soft;
        lightObj.transform.rotation = Quaternion.Euler(45f, 45f, 0f);
        
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
        RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
        RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);
    }
    
    private static GameObject CreatePlayerWithComponents()
    {
        Debug.Log("👤 Creating player with components...");
        
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        player.transform.localScale = new Vector3(1, 2, 1);
        
        // Add Rigidbody
        Rigidbody rb = player.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
        
        // Add PlayerController
        player.AddComponent<PlayerController>();
        
        // Apply material
        Material playerMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PlayerMaterial.mat");
        if (playerMat != null)
            player.GetComponent<Renderer>().material = playerMat;
        
        return player;
    }
    
    private static GameObject CreateSquadMateWithComponents()
    {
        Debug.Log("🤖 Creating SquadMate with ML-Agents components...");
        
        GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        squadmate.name = "SquadMate";
        squadmate.transform.position = new Vector3(3, 1, 0);
        squadmate.transform.localScale = new Vector3(1, 2, 1);
        
        // Add Rigidbody
        Rigidbody rb = squadmate.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
        
        // Add ML-Agents components
        BehaviorParameters behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;
        
        // Add SquadMate scripts
        squadmate.AddComponent<SquadMateAgent>();
        squadmate.AddComponent<RewardCalculator>();
        squadmate.AddComponent<SquadMateDecisionTree>();
        
        // Apply material
        Material squadmateMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/SquadMateMaterial.mat");
        if (squadmateMat != null)
            squadmate.GetComponent<Renderer>().material = squadmateMat;
        
        return squadmate;
    }
    
    private static GameObject CreateEnvironmentWithComponents()
    {
        Debug.Log("🌍 Creating environment with components...");
        
        GameObject environment = new GameObject("Environment");
        environment.transform.position = Vector3.zero;
        
        // Add environment scripts
        environment.AddComponent<GameEnvironment>();
        environment.AddComponent<ObjectSpawner>();
        
        return environment;
    }
    
    private static void CreateSpawnPointsSystem(GameObject environment)
    {
        Debug.Log("📍 Creating spawn points system...");
        
        Vector3[] spawnPositions = new Vector3[]
        {
            new Vector3(10, 1, 10), new Vector3(-10, 1, 10),
            new Vector3(10, 1, -10), new Vector3(-10, 1, -10),
            new Vector3(15, 1, 0), new Vector3(-15, 1, 0),
            new Vector3(0, 1, 15), new Vector3(0, 1, -15),
            new Vector3(20, 1, 5), new Vector3(-20, 1, -5)
        };
        
        for (int i = 0; i < spawnPositions.Length; i++)
        {
            GameObject spawnPoint = new GameObject($"SpawnPoint_{i + 1}");
            spawnPoint.transform.position = spawnPositions[i];
            spawnPoint.transform.SetParent(environment.transform);
            
            // Add visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            indicator.name = "Indicator";
            indicator.transform.SetParent(spawnPoint.transform);
            indicator.transform.localPosition = Vector3.zero;
            indicator.transform.localScale = Vector3.one * 0.3f;
            
            Material spawnMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/SpawnPointMaterial.mat");
            if (spawnMat != null)
                indicator.GetComponent<Renderer>().material = spawnMat;
            
            DestroyImmediate(indicator.GetComponent<Collider>());
        }
    }
    
    private static void CreateAllPrefabs()
    {
        Debug.Log("📦 Creating all prefabs...");
        
        if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
            AssetDatabase.CreateFolder("Assets", "Prefabs");
        
        CreateEnemyPrefab();
        CreateMedkitPrefab();
        CreateWeaponPrefab();
        
        AssetDatabase.SaveAssets();
    }
    
    private static void CreateEnemyPrefab()
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "Enemy";
        enemy.transform.localScale = new Vector3(1, 2, 1);
        
        NavMeshAgent navAgent = enemy.AddComponent<NavMeshAgent>();
        navAgent.speed = 3f;
        navAgent.stoppingDistance = 2f;
        
        enemy.AddComponent<EnemyController>();
        
        Material enemyMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/EnemyMaterial.mat");
        if (enemyMat != null)
            enemy.GetComponent<Renderer>().material = enemyMat;
        
        PrefabUtility.SaveAsPrefabAsset(enemy, "Assets/Prefabs/Enemy.prefab");
        DestroyImmediate(enemy);
    }
    
    private static void CreateMedkitPrefab()
    {
        GameObject medkit = GameObject.CreatePrimitive(PrimitiveType.Cube);
        medkit.name = "Medkit";
        medkit.transform.localScale = Vector3.one * 0.8f;
        medkit.GetComponent<Collider>().isTrigger = true;
        
        medkit.AddComponent<MedkitPickup>();
        medkit.AddComponent<RotateObject>();
        
        Material medkitMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/MedkitMaterial.mat");
        if (medkitMat != null)
            medkit.GetComponent<Renderer>().material = medkitMat;
        
        PrefabUtility.SaveAsPrefabAsset(medkit, "Assets/Prefabs/Medkit.prefab");
        DestroyImmediate(medkit);
    }
    
    private static void CreateWeaponPrefab()
    {
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        weapon.name = "Weapon";
        weapon.transform.localScale = new Vector3(0.3f, 1f, 0.3f);
        weapon.GetComponent<Collider>().isTrigger = true;
        
        weapon.AddComponent<WeaponPickup>();
        weapon.AddComponent<RotateObject>();
        
        Material weaponMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/WeaponMaterial.mat");
        if (weaponMat != null)
            weapon.GetComponent<Renderer>().material = weaponMat;
        
        PrefabUtility.SaveAsPrefabAsset(weapon, "Assets/Prefabs/Weapon.prefab");
        DestroyImmediate(weapon);
    }
    
    private static void ConfigureAllReferences(GameObject player, GameObject squadmate, GameObject environment)
    {
        Debug.Log("🔗 Configuring all references...");
        
        // Configure SquadMate references
        SquadMateAgent agent = squadmate.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.player = player.transform;
            agent.environment = environment.GetComponent<GameEnvironment>();
        }
        
        // Configure Environment references
        GameEnvironment gameEnv = environment.GetComponent<GameEnvironment>();
        if (gameEnv != null)
        {
            gameEnv.player = player.transform;
            gameEnv.squadMate = agent;
            
            // Assign prefabs
            gameEnv.enemyPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Enemy.prefab");
            gameEnv.medkitPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Medkit.prefab");
            gameEnv.weaponPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Weapon.prefab");
            
            // Assign spawn points
            Transform[] spawnPoints = new Transform[10];
            for (int i = 0; i < 10; i++)
            {
                Transform spawnPoint = environment.transform.Find($"SpawnPoint_{i + 1}");
                if (spawnPoint != null)
                    spawnPoints[i] = spawnPoint;
            }
            gameEnv.spawnPoints = spawnPoints;
            
            // Configure environment settings
            gameEnv.environmentSize = new Vector3(50f, 0f, 50f);
            gameEnv.maxEnemies = 3;
            gameEnv.maxMedkits = 2;
            gameEnv.maxWeapons = 2;
        }
    }
    
    private static void SetupNavMeshSystem()
    {
        Debug.Log("🗺️ Setting up NavMesh system...");
        
        try
        {
            UnityEditor.AI.NavMeshBuilder.BuildNavMesh();
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"NavMesh baking failed: {e.Message}. You can bake manually later.");
        }
    }
    
    private static void SaveTrainingScene()
    {
        Debug.Log("💾 Saving training scene...");
        
        if (!AssetDatabase.IsValidFolder("Assets/Scenes"))
            AssetDatabase.CreateFolder("Assets", "Scenes");
        
        UnityEditor.SceneManagement.EditorSceneManager.SaveScene(
            UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene(),
            "Assets/Scenes/TrainingEnvironment.unity"
        );
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
}
