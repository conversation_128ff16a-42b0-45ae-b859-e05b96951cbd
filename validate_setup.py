#!/usr/bin/env python3
"""
Setup Validation Script for PUBG SquadMate AI
Checks if everything is ready for Unity training
"""

import os
import sys

def check_python_packages():
    """Check if required Python packages are available"""
    print("🐍 Checking Python packages...")
    
    packages = {
        'mlagents.trainers': 'ML-Agents trainers',
        'torch': 'PyTorch',
        'tensorboard': 'TensorBoard',
        'numpy': 'NumPy',
        'h5py': 'HDF5 support'
    }
    
    all_good = True
    for package, description in packages.items():
        try:
            __import__(package)
            print(f"  ✅ {description}")
        except ImportError:
            print(f"  ❌ {description} - Not installed")
            all_good = False
    
    return all_good

def check_unity_scripts():
    """Check if Unity scripts are present"""
    print("\n📁 Checking Unity scripts...")
    
    required_scripts = [
        'Assets/Scripts/Agents/SquadMateAgent.cs',
        'Assets/Scripts/Agents/SquadMateDecisionTree.cs',
        'Assets/Scripts/Controllers/PlayerController.cs',
        'Assets/Scripts/Controllers/EnemyController.cs',
        'Assets/Scripts/Environment/GameEnvironment.cs',
        'Assets/Scripts/Environment/ObjectSpawner.cs',
        'Assets/Scripts/Utils/RewardCalculator.cs'
    ]
    
    all_good = True
    for script in required_scripts:
        if os.path.exists(script):
            print(f"  ✅ {script}")
        else:
            print(f"  ❌ {script} - Missing")
            all_good = False
    
    return all_good

def check_config_files():
    """Check if configuration files are present"""
    print("\n⚙️ Checking configuration files...")
    
    config_files = [
        'config/squadmate_config.yaml',
        'Assets/Data/decision_tree.json'
    ]
    
    all_good = True
    for config in config_files:
        if os.path.exists(config):
            print(f"  ✅ {config}")
        else:
            print(f"  ❌ {config} - Missing")
            all_good = False
    
    return all_good

def check_documentation():
    """Check if documentation files are present"""
    print("\n📚 Checking documentation...")
    
    docs = [
        'README.md',
        'SETUP_GUIDE.md',
        'UNITY_TRAINING_GUIDE.md'
    ]
    
    for doc in docs:
        if os.path.exists(doc):
            print(f"  ✅ {doc}")
        else:
            print(f"  ⚠️ {doc} - Missing (optional)")

def print_next_steps():
    """Print next steps for the user"""
    print("\n🚀 Next Steps:")
    print("=" * 50)
    print("1. Open Unity 2022.3 LTS")
    print("2. Create new 3D project named 'SquadMate-AI'")
    print("3. Install ML-Agents package in Unity:")
    print("   - Window → Package Manager")
    print("   - '+' → Add package from git URL")
    print("   - Enter: com.unity.ml-agents")
    print("4. Copy all Assets/ folder contents to Unity project")
    print("5. Follow UNITY_TRAINING_GUIDE.md for detailed setup")
    print("6. Create training scene with Player and SquadMate")
    print("7. Press Play in Unity to start training!")
    print("\n📖 Read UNITY_TRAINING_GUIDE.md for complete instructions")

def print_troubleshooting():
    """Print troubleshooting information"""
    print("\n🔧 Troubleshooting:")
    print("=" * 50)
    print("If you have issues:")
    print("1. Python 3.13 compatibility: Use Unity-based training")
    print("2. Missing packages: Run 'pip install mlagents torch'")
    print("3. Unity errors: Check ML-Agents package installation")
    print("4. Training not working: Verify Behavior Parameters setup")
    print("5. Performance issues: Reduce Time Scale in Unity")

def main():
    print("🎮 PUBG SquadMate AI - Setup Validation")
    print("=" * 50)
    
    # Check Python version
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 12):
        print("⚠️  Python 3.12+ detected - Unity-based training recommended")
    
    # Run all checks
    packages_ok = check_python_packages()
    scripts_ok = check_unity_scripts()
    config_ok = check_config_files()
    check_documentation()
    
    print("\n" + "=" * 50)
    
    # Summary
    if packages_ok and scripts_ok and config_ok:
        print("🎉 All checks passed! You're ready to start training.")
        print_next_steps()
    elif packages_ok and scripts_ok:
        print("✅ Core components ready. Missing some config files.")
        print("💡 You can still proceed with Unity training.")
        print_next_steps()
    elif packages_ok:
        print("✅ Python packages ready. Missing Unity scripts.")
        print("📁 Make sure to copy all files to your Unity project.")
    else:
        print("❌ Some components are missing.")
        print("🔧 Please install missing packages and files.")
        print_troubleshooting()
    
    print("\n📋 Training Approach for Python 3.13:")
    print("- Use Unity's built-in ML-Agents training")
    print("- No external command-line tools needed")
    print("- More stable and easier to use")
    print("- Follow UNITY_TRAINING_GUIDE.md")

if __name__ == "__main__":
    main()
