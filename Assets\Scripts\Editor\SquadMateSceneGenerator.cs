using UnityEngine;
using UnityEditor;
using UnityEngine.AI;
using Unity.MLAgents.Policies;
using UnityEditor.AI;

/// <summary>
/// Unity Editor script to automatically generate the complete SquadMate training scene
/// This creates everything needed for ML-Agents training in one click!
/// </summary>
public class SquadMateSceneGenerator : EditorWindow
{
    [MenuItem("SquadMate AI/Generate Training Scene")]
    public static void ShowWindow()
    {
        GetWindow<SquadMateSceneGenerator>("SquadMate Scene Generator");
    }

    private void OnGUI()
    {
        GUILayout.Label("SquadMate AI Training Scene Generator", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This will create a complete training environment with:");
        GUILayout.Label("• Terrain with NavMesh");
        GUILayout.Label("• Player and SquadMate agents");
        GUILayout.Label("• Environment manager");
        GUILayout.Label("• Spawn points");
        GUILayout.Label("• Prefabs (Enemy, Medkit, Weapon)");
        GUILayout.Label("• Materials and lighting");

        GUILayout.Space(20);

        if (GUILayout.Button("Generate Complete Training Scene", GUILayout.Height(40)))
        {
            GenerateTrainingScene();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("Generate Prefabs Only", GUILayout.Height(30)))
        {
            GeneratePrefabs();
        }

        if (GUILayout.Button("Setup NavMesh", GUILayout.Height(30)))
        {
            SetupNavMesh();
        }
    }

    public static void GenerateTrainingScene()
    {
        Debug.Log("🚀 Generating SquadMate Training Scene...");

        // Clear existing scene
        ClearScene();

        // Create materials first
        CreateMaterials();

        // Create terrain
        GameObject terrain = CreateTerrain();

        // Create lighting
        SetupLighting();

        // Create player
        GameObject player = CreatePlayer();

        // Create squadmate
        GameObject squadmate = CreateSquadMate();

        // Create environment manager
        GameObject environment = CreateEnvironment(player, squadmate);

        // Create spawn points
        CreateSpawnPoints(environment);

        // Generate prefabs
        GeneratePrefabs();

        // Setup NavMesh
        SetupNavMesh();

        // Configure environment with prefabs
        ConfigureEnvironmentPrefabs(environment);

        // Save scene
        SaveScene();

        Debug.Log("✅ SquadMate Training Scene Generated Successfully!");
        Debug.Log("🎮 Press Play to start training your AI!");
    }

    private static void ClearScene()
    {
        // Remove all objects except camera
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name != "Main Camera")
            {
                DestroyImmediate(obj);
            }
        }
    }

    private static void CreateMaterials()
    {
        // Create Materials folder
        if (!AssetDatabase.IsValidFolder("Assets/Materials"))
            AssetDatabase.CreateFolder("Assets", "Materials");

        // Player material (Blue)
        Material playerMat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        playerMat.color = Color.blue;
        AssetDatabase.CreateAsset(playerMat, "Assets/Materials/PlayerMaterial.mat");

        // SquadMate material (Green)
        Material squadmateMat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        squadmateMat.color = Color.green;
        AssetDatabase.CreateAsset(squadmateMat, "Assets/Materials/SquadMateMaterial.mat");

        // Enemy material (Red)
        Material enemyMat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        enemyMat.color = Color.red;
        AssetDatabase.CreateAsset(enemyMat, "Assets/Materials/EnemyMaterial.mat");

        // Medkit material (Bright Green)
        Material medkitMat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        medkitMat.color = Color.cyan;
        AssetDatabase.CreateAsset(medkitMat, "Assets/Materials/MedkitMaterial.mat");

        // Weapon material (Yellow)
        Material weaponMat = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        weaponMat.color = Color.yellow;
        AssetDatabase.CreateAsset(weaponMat, "Assets/Materials/WeaponMaterial.mat");

        AssetDatabase.SaveAssets();
    }

    private static GameObject CreateTerrain()
    {
        // Create terrain
        TerrainData terrainData = new TerrainData();
        terrainData.heightmapResolution = 513;
        terrainData.size = new Vector3(50, 8, 50);

        // Create some height variation
        float[,] heights = new float[513, 513];
        for (int x = 0; x < 513; x++)
        {
            for (int y = 0; y < 513; y++)
            {
                heights[x, y] = Mathf.PerlinNoise(x * 0.01f, y * 0.01f) * 0.1f;
            }
        }
        terrainData.SetHeights(0, 0, heights);

        // Save terrain data
        if (!AssetDatabase.IsValidFolder("Assets/Terrain"))
            AssetDatabase.CreateFolder("Assets", "Terrain");
        AssetDatabase.CreateAsset(terrainData, "Assets/Terrain/TrainingTerrain.asset");

        // Create terrain GameObject
        GameObject terrainObj = Terrain.CreateTerrainGameObject(terrainData);
        terrainObj.name = "Training Terrain";

        // Mark as navigation static
        GameObjectUtility.SetStaticEditorFlags(terrainObj, StaticEditorFlags.NavigationStatic);

        return terrainObj;
    }

    private static void SetupLighting()
    {
        // Create directional light
        GameObject lightObj = new GameObject("Directional Light");
        Light light = lightObj.AddComponent<Light>();
        light.type = LightType.Directional;
        light.intensity = 1.5f;
        lightObj.transform.rotation = Quaternion.Euler(45f, 45f, 0f);

        // Set ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
        RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
        RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);
    }

    private static GameObject CreatePlayer()
    {
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        player.transform.localScale = new Vector3(1, 2, 1);

        // Add Rigidbody
        Rigidbody rb = player.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Apply material
        Material playerMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/PlayerMaterial.mat");
        player.GetComponent<Renderer>().material = playerMat;

        // Add PlayerController script (will be added when script is attached)
        Debug.Log("📝 Remember to add PlayerController script to Player GameObject");

        return player;
    }

    private static GameObject CreateSquadMate()
    {
        GameObject squadmate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        squadmate.name = "SquadMate";
        squadmate.transform.position = new Vector3(3, 1, 0);
        squadmate.transform.localScale = new Vector3(1, 2, 1);

        // Add Rigidbody
        Rigidbody rb = squadmate.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Apply material
        Material squadmateMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/SquadMateMaterial.mat");
        squadmate.GetComponent<Renderer>().material = squadmateMat;

        // Add Behavior Parameters
        BehaviorParameters behaviorParams = squadmate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;

        // Note: In newer ML-Agents, action spaces are configured in the training config
        // The agent script handles the observation and action space definitions

        Debug.Log("📝 Remember to add SquadMateAgent and RewardCalculator scripts to SquadMate GameObject");

        return squadmate;
    }

    private static GameObject CreateEnvironment(GameObject player, GameObject squadmate)
    {
        GameObject environment = new GameObject("Environment");
        environment.transform.position = Vector3.zero;

        Debug.Log("📝 Remember to add GameEnvironment script to Environment GameObject");

        return environment;
    }

    private static void CreateSpawnPoints(GameObject environment)
    {
        Vector3[] spawnPositions = new Vector3[]
        {
            new Vector3(10, 1, 10),
            new Vector3(-10, 1, 10),
            new Vector3(10, 1, -10),
            new Vector3(-10, 1, -10),
            new Vector3(15, 1, 0),
            new Vector3(-15, 1, 0),
            new Vector3(0, 1, 15),
            new Vector3(0, 1, -15),
            new Vector3(20, 1, 20),
            new Vector3(-20, 1, -20)
        };

        for (int i = 0; i < spawnPositions.Length; i++)
        {
            GameObject spawnPoint = new GameObject($"SpawnPoint_{i + 1}");
            spawnPoint.transform.position = spawnPositions[i];
            spawnPoint.transform.SetParent(environment.transform);

            // Add visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            indicator.name = "Indicator";
            indicator.transform.SetParent(spawnPoint.transform);
            indicator.transform.localPosition = Vector3.zero;
            indicator.transform.localScale = Vector3.one * 0.5f;
            indicator.GetComponent<Renderer>().material.color = Color.magenta;
            DestroyImmediate(indicator.GetComponent<Collider>());
        }
    }

    private static void GeneratePrefabs()
    {
        // Create Prefabs folder
        if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
            AssetDatabase.CreateFolder("Assets", "Prefabs");

        // Create Enemy Prefab
        CreateEnemyPrefab();

        // Create Medkit Prefab
        CreateMedkitPrefab();

        // Create Weapon Prefab
        CreateWeaponPrefab();

        AssetDatabase.SaveAssets();
    }

    private static void CreateEnemyPrefab()
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "Enemy";
        enemy.transform.localScale = new Vector3(1, 2, 1);

        // Add NavMesh Agent
        NavMeshAgent navAgent = enemy.AddComponent<NavMeshAgent>();
        navAgent.speed = 3f;
        navAgent.stoppingDistance = 2f;

        // Add Rigidbody
        Rigidbody rb = enemy.AddComponent<Rigidbody>();
        rb.isKinematic = true; // NavMesh controls movement

        // Apply material
        Material enemyMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/EnemyMaterial.mat");
        enemy.GetComponent<Renderer>().material = enemyMat;

        // Save as prefab
        PrefabUtility.SaveAsPrefabAsset(enemy, "Assets/Prefabs/Enemy.prefab");
        DestroyImmediate(enemy);
    }

    private static void CreateMedkitPrefab()
    {
        GameObject medkit = GameObject.CreatePrimitive(PrimitiveType.Cube);
        medkit.name = "Medkit";
        medkit.transform.localScale = Vector3.one * 0.8f;

        // Make collider a trigger
        medkit.GetComponent<Collider>().isTrigger = true;

        // Apply material
        Material medkitMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/MedkitMaterial.mat");
        medkit.GetComponent<Renderer>().material = medkitMat;

        // Add rotation animation
        medkit.AddComponent<RotateObject>();

        // Save as prefab
        PrefabUtility.SaveAsPrefabAsset(medkit, "Assets/Prefabs/Medkit.prefab");
        DestroyImmediate(medkit);
    }

    private static void CreateWeaponPrefab()
    {
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        weapon.name = "Weapon";
        weapon.transform.localScale = new Vector3(0.3f, 1f, 0.3f);

        // Make collider a trigger
        weapon.GetComponent<Collider>().isTrigger = true;

        // Apply material
        Material weaponMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Materials/WeaponMaterial.mat");
        weapon.GetComponent<Renderer>().material = weaponMat;

        // Add rotation animation
        weapon.AddComponent<RotateObject>();

        // Save as prefab
        PrefabUtility.SaveAsPrefabAsset(weapon, "Assets/Prefabs/Weapon.prefab");
        DestroyImmediate(weapon);
    }

    private static void SetupNavMesh()
    {
        // Find all static objects and mark them for navigation
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name.Contains("Terrain"))
            {
                GameObjectUtility.SetStaticEditorFlags(obj, StaticEditorFlags.NavigationStatic);
            }
        }

        // Bake NavMesh using Unity Editor API
        UnityEditor.AI.NavMeshBuilder.BuildNavMesh();
        Debug.Log("✅ NavMesh baked successfully!");
    }

    private static void ConfigureEnvironmentPrefabs(GameObject environment)
    {
        Debug.Log("📝 Remember to assign prefabs to Environment script:");
        Debug.Log("   • Enemy Prefab: Assets/Prefabs/Enemy.prefab");
        Debug.Log("   • Medkit Prefab: Assets/Prefabs/Medkit.prefab");
        Debug.Log("   • Weapon Prefab: Assets/Prefabs/Weapon.prefab");
    }

    private static void SaveScene()
    {
        // Create Scenes folder if it doesn't exist
        if (!AssetDatabase.IsValidFolder("Assets/Scenes"))
            AssetDatabase.CreateFolder("Assets", "Scenes");

        // Save scene
        UnityEditor.SceneManagement.EditorSceneManager.SaveScene(
            UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene(),
            "Assets/Scenes/TrainingEnvironment.unity"
        );

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
}

// Simple rotation component for pickups
public class RotateObject : MonoBehaviour
{
    public float rotationSpeed = 50f;

    void Update()
    {
        transform.Rotate(0, rotationSpeed * Time.deltaTime, 0);
    }
}
